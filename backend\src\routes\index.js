const express = require('express');
const router = express.Router();

// 导入路由模块
const userRoutes = require('./user');
const departmentRoutes = require('./department');
const formRoutes = require('./form');
const workflowRoutes = require('./workflow');
const testAnalyticsRoutes = require('./test-analytics');
const roleRoutes = require('./role');

// 导入中间件
const { authenticate } = require('../middlewares/auth');

/**
 * @swagger
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */

// 用户路由
router.use('/users', userRoutes);

// 部门路由 - 需要认证
router.use('/departments', authenticate, departmentRoutes);

// 表单路由 - 需要认证
router.use('/forms', authenticate, formRoutes);

// 工作流路由 - 需要认证
router.use('/workflows', authenticate, workflowRoutes);

// 测试分析路由 - 需要认证
router.use('/test-analytics', authenticate, testAnalyticsRoutes);

// 角色路由
router.use('/roles', roleRoutes);

// API健康检查
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'API服务正常运行',
    timestamp: new Date().toISOString()
  });
});

// 根路由
router.get('/', (req, res) => {
  res.json({
    message: '工作流系统API服务',
    version: '1.0.0'
  });
});

// 测试路由
router.get('/test-departments', async (req, res) => {
  try {
    const { Department } = require('../models');
    console.log('Department model:', Department);

    if (Department && Department.findAll) {
      const departments = await Department.findAll();
      res.json({
        success: true,
        data: departments
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Department model or findAll method is not defined'
      });
    }
  } catch (error) {
    console.error('Test departments error:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

module.exports = router;
