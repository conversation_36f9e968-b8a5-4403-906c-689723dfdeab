/**
 * Test Reporter Service
 * Generates comprehensive test reports
 */

const fs = require('fs');
const path = require('path');
const TestMonitor = require('./test-monitor');
const TestAnalyticsClient = require('./test-analytics-client');

class TestReporter {
  constructor(config = {}) {
    this.reportDir = config.reportDir || path.join(__dirname, '../test-reports');
    this.testRunId = config.testRunId || this.generateTestRunId();
    this.reportFile = config.reportFile || path.join(this.reportDir, `report-${this.testRunId}.html`);
    this.jsonReportFile = config.jsonReportFile || path.join(this.reportDir, `report-${this.testRunId}.json`);
    this.monitor = config.monitor || null;
    this.testResults = config.testResults || null;
    this.startTime = Date.now();
    this.analyticsEnabled = config.analyticsEnabled !== false;
    this.analyticsClient = null;

    // Initialize analytics client if enabled
    if (this.analyticsEnabled) {
      this.analyticsClient = new TestAnalyticsClient({
        testRunId: this.testRunId,
        testType: config.testType || 'unknown',
        config: config.testConfig || {}
      });
    }

    // Create report directory if it doesn't exist
    if (!fs.existsSync(this.reportDir)) {
      fs.mkdirSync(this.reportDir, { recursive: true });
    }
  }

  /**
   * Generate a unique test run ID
   * @returns {string} - Unique test run ID
   */
  generateTestRunId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * Set test monitor
   * @param {TestMonitor} monitor - Test monitor
   */
  setMonitor(monitor) {
    this.monitor = monitor;
  }

  /**
   * Set test results
   * @param {object} results - Test results
   */
  setTestResults(results) {
    this.testResults = results;
  }

  /**
   * Generate HTML report
   * @returns {string} - HTML report
   */
  generateHtmlReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;

    // Get performance metrics if monitor is available
    const performanceReport = this.monitor ? this.monitor.generatePerformanceReport() : null;

    // Create HTML content
    let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试报告 - ${this.testRunId}</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }
    h1 { color: #2c3e50; border-bottom: 2px solid #eee; padding-bottom: 10px; }
    h2 { color: #3498db; margin-top: 30px; }
    h3 { color: #2980b9; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 30px; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
    th { background-color: #f2f2f2; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    .summary { background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    .success { color: #27ae60; }
    .failure { color: #e74c3c; }
    .warning { color: #f39c12; }
    .error-details { background-color: #ffeeee; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 10px; }
    .chart-container { width: 100%; height: 300px; margin-bottom: 30px; }
    .tabs { display: flex; margin-bottom: 20px; border-bottom: 1px solid #ddd; }
    .tab { padding: 10px 20px; cursor: pointer; background-color: #f9f9f9; border: 1px solid #ddd; border-bottom: none; margin-right: 5px; border-radius: 5px 5px 0 0; }
    .tab.active { background-color: #fff; border-bottom: 1px solid #fff; margin-bottom: -1px; }
    .tab-content { display: none; }
    .tab-content.active { display: block; }
    .collapsible { cursor: pointer; padding: 10px; background-color: #f9f9f9; border: 1px solid #ddd; margin-bottom: 5px; }
    .collapsible:after { content: '\\002B'; float: right; }
    .active:after { content: '\\2212'; }
    .content { padding: 0 10px; max-height: 0; overflow: hidden; transition: max-height 0.2s ease-out; background-color: #fff; }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <h1>测试报告</h1>

  <div class="summary">
    <p>测试运行 ID: ${this.testRunId}</p>
    <p>开始时间: ${new Date(this.startTime).toLocaleString()}</p>
    <p>结束时间: ${new Date(endTime).toLocaleString()}</p>
    <p>持续时间: ${(duration / 1000).toFixed(2)} 秒</p>
    ${this.testResults ? `
    <p>测试结果: <span class="${this.testResults.success ? 'success' : 'failure'}">${this.testResults.success ? '成功' : '失败'}</span></p>
    <p>测试总数: ${this.testResults.total}</p>
    <p>通过: <span class="success">${this.testResults.passed}</span></p>
    <p>失败: <span class="failure">${this.testResults.failed}</span></p>
    <p>跳过: <span class="warning">${this.testResults.skipped}</span></p>
    ` : ''}
  </div>

  <div class="tabs">
    <div class="tab active" onclick="openTab(event, 'results')">测试结果</div>
    <div class="tab" onclick="openTab(event, 'performance')">性能指标</div>
    <div class="tab" onclick="openTab(event, 'timeline')">时间线</div>
    <div class="tab" onclick="openTab(event, 'system')">系统信息</div>
  </div>

  <div id="results" class="tab-content active">
    <h2>测试结果</h2>
    ${this.testResults ? this.generateTestResultsHtml() : '<p>没有测试结果数据</p>'}
  </div>

  <div id="performance" class="tab-content">
    <h2>性能指标</h2>
    ${performanceReport ? this.generatePerformanceHtml(performanceReport) : '<p>没有性能监控数据</p>'}
  </div>

  <div id="timeline" class="tab-content">
    <h2>测试时间线</h2>
    ${this.monitor ? this.generateTimelineHtml() : '<p>没有时间线数据</p>'}
  </div>

  <div id="system" class="tab-content">
    <h2>系统信息</h2>
    ${performanceReport ? this.generateSystemInfoHtml(performanceReport.system) : '<p>没有系统信息数据</p>'}
  </div>

  <script>
    function openTab(evt, tabName) {
      var i, tabcontent, tablinks;
      tabcontent = document.getElementsByClassName("tab-content");
      for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].className = tabcontent[i].className.replace(" active", "");
      }
      tablinks = document.getElementsByClassName("tab");
      for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
      }
      document.getElementById(tabName).className += " active";
      evt.currentTarget.className += " active";
    }

    var coll = document.getElementsByClassName("collapsible");
    var i;

    for (i = 0; i < coll.length; i++) {
      coll[i].addEventListener("click", function() {
        this.classList.toggle("active");
        var content = this.nextElementSibling;
        if (content.style.maxHeight) {
          content.style.maxHeight = null;
        } else {
          content.style.maxHeight = content.scrollHeight + "px";
        }
      });
    }

    ${this.generateChartScripts()}
  </script>
</body>
</html>
    `;

    // Save HTML report
    fs.writeFileSync(this.reportFile, html);

    return html;
  }

  /**
   * Generate test results HTML
   * @returns {string} - Test results HTML
   */
  generateTestResultsHtml() {
    if (!this.testResults || !this.testResults.specs) {
      return '<p>没有详细的测试结果数据</p>';
    }

    let html = `
    <div class="chart-container">
      <canvas id="testResultsChart"></canvas>
    </div>

    <h3>测试文件</h3>
    <table>
      <tr>
        <th>文件</th>
        <th>通过</th>
        <th>失败</th>
        <th>跳过</th>
        <th>总数</th>
        <th>状态</th>
      </tr>
    `;

    // Add rows for each file
    Object.entries(this.testResults.files || {}).forEach(([file, stats]) => {
      const status = stats.failed > 0 ? 'failure' : 'success';

      html += `
      <tr>
        <td>${file}</td>
        <td>${stats.passed}</td>
        <td>${stats.failed}</td>
        <td>${stats.skipped}</td>
        <td>${stats.total}</td>
        <td class="${status}">${stats.failed > 0 ? '失败' : '通过'}</td>
      </tr>
      `;
    });

    html += `
    </table>

    <h3>测试详情</h3>
    `;

    // Add details for each test
    this.testResults.specs.forEach((spec, index) => {
      const status = spec.status === 'passed' ? 'success' : (spec.status === 'skipped' ? 'warning' : 'failure');

      html += `
      <button class="collapsible">${spec.title} <span class="${status}">[${spec.status}]</span></button>
      <div class="content">
        <p><strong>文件:</strong> ${spec.file}</p>
        <p><strong>持续时间:</strong> ${spec.duration}ms</p>
        ${spec.error ? `
        <div class="error-details">
          <p><strong>错误:</strong> ${spec.error.message}</p>
          <pre>${spec.error.stack}</pre>
        </div>
        ` : ''}
      </div>
      `;
    });

    return html;
  }

  /**
   * Generate performance HTML
   * @param {object} performanceReport - Performance report
   * @returns {string} - Performance HTML
   */
  generatePerformanceHtml(performanceReport) {
    return `
    <div class="chart-container">
      <canvas id="cpuChart"></canvas>
    </div>

    <div class="chart-container">
      <canvas id="memoryChart"></canvas>
    </div>

    <div class="chart-container">
      <canvas id="responseTimeChart"></canvas>
    </div>

    <h3>性能摘要</h3>
    <table>
      <tr>
        <th>指标</th>
        <th>平均值</th>
        <th>最大值</th>
        <th>样本数</th>
      </tr>
      <tr>
        <td>CPU 使用率</td>
        <td>${performanceReport.cpu.avg.toFixed(2)}%</td>
        <td>${performanceReport.cpu.max.toFixed(2)}%</td>
        <td>${performanceReport.cpu.samples}</td>
      </tr>
      <tr>
        <td>内存使用 (MB)</td>
        <td>${performanceReport.memory.avg.toFixed(2)}</td>
        <td>${performanceReport.memory.max.toFixed(2)}</td>
        <td>${performanceReport.memory.samples}</td>
      </tr>
      <tr>
        <td>响应时间 (ms)</td>
        <td>${performanceReport.responseTime.avg.toFixed(2)}</td>
        <td>${performanceReport.responseTime.max.toFixed(2)}</td>
        <td>${performanceReport.responseTime.samples}</td>
      </tr>
    </table>
    `;
  }

  /**
   * Generate timeline HTML
   * @returns {string} - Timeline HTML
   */
  generateTimelineHtml() {
    const events = this.monitor.getEvents();

    if (!events || events.length === 0) {
      return '<p>没有时间线数据</p>';
    }

    let html = `
    <table>
      <tr>
        <th>时间</th>
        <th>事件类型</th>
        <th>详情</th>
      </tr>
    `;

    // Add rows for each event
    events.forEach(event => {
      html += `
      <tr>
        <td>${new Date(event.timestamp).toLocaleTimeString()}</td>
        <td>${event.type}</td>
        <td>${JSON.stringify(event.data)}</td>
      </tr>
      `;
    });

    html += `
    </table>
    `;

    return html;
  }

  /**
   * Generate system info HTML
   * @param {object} systemInfo - System info
   * @returns {string} - System info HTML
   */
  generateSystemInfoHtml(systemInfo) {
    return `
    <table>
      <tr>
        <th>属性</th>
        <th>值</th>
      </tr>
      <tr>
        <td>平台</td>
        <td>${systemInfo.platform}</td>
      </tr>
      <tr>
        <td>架构</td>
        <td>${systemInfo.arch}</td>
      </tr>
      <tr>
        <td>Node.js 版本</td>
        <td>${systemInfo.nodeVersion}</td>
      </tr>
      <tr>
        <td>CPU 核心数</td>
        <td>${systemInfo.cpus}</td>
      </tr>
      <tr>
        <td>总内存 (GB)</td>
        <td>${systemInfo.totalMemory.toFixed(2)}</td>
      </tr>
      <tr>
        <td>可用内存 (GB)</td>
        <td>${systemInfo.freeMemory.toFixed(2)}</td>
      </tr>
      <tr>
        <td>系统运行时间 (秒)</td>
        <td>${systemInfo.uptime}</td>
      </tr>
    </table>
    `;
  }

  /**
   * Generate chart scripts
   * @returns {string} - Chart scripts
   */
  generateChartScripts() {
    let scripts = '';

    // Test results chart
    if (this.testResults) {
      scripts += `
      // Test results chart
      const testResultsCtx = document.getElementById('testResultsChart');
      if (testResultsCtx) {
        new Chart(testResultsCtx, {
          type: 'pie',
          data: {
            labels: ['通过', '失败', '跳过'],
            datasets: [{
              data: [${this.testResults.passed}, ${this.testResults.failed}, ${this.testResults.skipped}],
              backgroundColor: [
                '#27ae60',
                '#e74c3c',
                '#f39c12'
              ]
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: {
                position: 'right',
              },
              title: {
                display: true,
                text: '测试结果分布'
              }
            }
          }
        });
      }
      `;
    }

    // Performance charts
    if (this.monitor) {
      const metrics = this.monitor.getMetrics();

      if (metrics.performance.cpu.length > 0) {
        scripts += `
        // CPU chart
        const cpuCtx = document.getElementById('cpuChart');
        if (cpuCtx) {
          new Chart(cpuCtx, {
            type: 'line',
            data: {
              labels: [${metrics.performance.cpu.map(m => `'${new Date(m.timestamp).toLocaleTimeString()}'`).join(', ')}],
              datasets: [{
                label: 'CPU 使用率 (%)',
                data: [${metrics.performance.cpu.map(m => m.value.toFixed(2)).join(', ')}],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.1
              }]
            },
            options: {
              responsive: true,
              plugins: {
                title: {
                  display: true,
                  text: 'CPU 使用率'
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: '使用率 (%)'
                  }
                }
              }
            }
          });
        }
        `;
      }

      if (metrics.performance.memory.length > 0) {
        scripts += `
        // Memory chart
        const memoryCtx = document.getElementById('memoryChart');
        if (memoryCtx) {
          new Chart(memoryCtx, {
            type: 'line',
            data: {
              labels: [${metrics.performance.memory.map(m => `'${new Date(m.timestamp).toLocaleTimeString()}'`).join(', ')}],
              datasets: [{
                label: 'RSS 内存 (MB)',
                data: [${metrics.performance.memory.map(m => m.rss.toFixed(2)).join(', ')}],
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                tension: 0.1
              }, {
                label: '堆内存使用 (MB)',
                data: [${metrics.performance.memory.map(m => m.heapUsed.toFixed(2)).join(', ')}],
                borderColor: '#f39c12',
                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                tension: 0.1
              }]
            },
            options: {
              responsive: true,
              plugins: {
                title: {
                  display: true,
                  text: '内存使用'
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: '内存 (MB)'
                  }
                }
              }
            }
          });
        }
        `;
      }

      if (metrics.performance.responseTime.length > 0) {
        scripts += `
        // Response time chart
        const responseTimeCtx = document.getElementById('responseTimeChart');
        if (responseTimeCtx) {
          new Chart(responseTimeCtx, {
            type: 'line',
            data: {
              labels: [${metrics.performance.responseTime.map(m => `'${new Date(m.timestamp).toLocaleTimeString()}'`).join(', ')}],
              datasets: [{
                label: '响应时间 (ms)',
                data: [${metrics.performance.responseTime.map(m => m.value.toFixed(2)).join(', ')}],
                borderColor: '#2ecc71',
                backgroundColor: 'rgba(46, 204, 113, 0.1)',
                tension: 0.1
              }]
            },
            options: {
              responsive: true,
              plugins: {
                title: {
                  display: true,
                  text: 'API 响应时间'
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: '响应时间 (ms)'
                  }
                }
              }
            }
          });
        }
        `;
      }
    }

    return scripts;
  }

  /**
   * Generate JSON report
   * @returns {object} - JSON report
   */
  generateJsonReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;

    // Get performance metrics if monitor is available
    const performanceReport = this.monitor ? this.monitor.generatePerformanceReport() : null;

    // Create JSON report
    const report = {
      testRunId: this.testRunId,
      startTime: new Date(this.startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      duration,
      testResults: this.testResults,
      performance: performanceReport,
      events: this.monitor ? this.monitor.getEvents() : [],
      system: performanceReport ? performanceReport.system : null
    };

    // Save JSON report
    fs.writeFileSync(this.jsonReportFile, JSON.stringify(report, null, 2));

    return report;
  }

  /**
   * Generate reports
   * @returns {object} - Generated reports
   */
  generateReports() {
    const htmlReport = this.generateHtmlReport();
    const jsonReport = this.generateJsonReport();

    // Send results to analytics if enabled
    if (this.analyticsEnabled && this.analyticsClient && this.testResults) {
      this.sendToAnalytics();
    }

    return {
      html: {
        path: this.reportFile,
        content: htmlReport
      },
      json: {
        path: this.jsonReportFile,
        content: jsonReport
      }
    };
  }

  /**
   * Send test results to analytics
   * @returns {Promise} - Promise that resolves when results are sent
   */
  async sendToAnalytics() {
    if (!this.analyticsClient || !this.testResults) {
      return Promise.resolve();
    }

    try {
      // Record performance metrics if monitor is available
      if (this.monitor) {
        const performanceReport = this.monitor.generatePerformanceReport();

        // Record CPU metrics
        if (performanceReport.cpu && performanceReport.cpu.samples > 0) {
          this.analyticsClient.recordMetric('cpu', performanceReport.cpu.avg, '%', {
            max: performanceReport.cpu.max,
            samples: performanceReport.cpu.samples
          });
        }

        // Record memory metrics
        if (performanceReport.memory && performanceReport.memory.samples > 0) {
          this.analyticsClient.recordMetric('memory', performanceReport.memory.avg, 'MB', {
            max: performanceReport.memory.max,
            samples: performanceReport.memory.samples
          });
        }

        // Record response time metrics
        if (performanceReport.responseTime && performanceReport.responseTime.samples > 0) {
          this.analyticsClient.recordMetric('responseTime', performanceReport.responseTime.avg, 'ms', {
            max: performanceReport.responseTime.max,
            samples: performanceReport.responseTime.samples
          });
        }
      }

      // Send results to analytics API
      return await this.analyticsClient.sendResults(this.testResults);
    } catch (error) {
      console.error('Error sending results to analytics:', error.message);
      return Promise.resolve();
    }
  }
}

module.exports = TestReporter;
