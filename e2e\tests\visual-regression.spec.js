/**
 * Visual regression tests
 * These tests capture screenshots of key pages for visual comparison
 */

const { test, expect } = require('@playwright/test');

test.describe('Visual Regression Tests', () => {
  // Configure screenshot comparison
  test.use({ 
    screenshot: 'on',
  });

  test('login page visual regression', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for comparison
    await expect(page).toHaveScreenshot('login-page.png', {
      maxDiffPixelRatio: 0.05, // Allow 5% difference
      fullPage: true
    });
  });

  test('home page visual regression', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for comparison
    await expect(page).toHaveScreenshot('home-page.png', {
      maxDiffPixelRatio: 0.05,
      fullPage: true
    });
  });

  test('department page visual regression', async ({ page }) => {
    // Navigate to department page
    await page.goto('/department');
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for comparison
    await expect(page).toHaveScreenshot('department-page.png', {
      maxDiffPixelRatio: 0.05,
      fullPage: true
    });
  });

  test('form design page visual regression', async ({ page }) => {
    // Navigate to form design page
    await page.goto('/form-design');
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for comparison
    await expect(page).toHaveScreenshot('form-design-page.png', {
      maxDiffPixelRatio: 0.05,
      fullPage: true
    });
  });

  test('workflow design page visual regression', async ({ page }) => {
    // Navigate to workflow design page
    await page.goto('/workflow-design');
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for comparison
    await expect(page).toHaveScreenshot('workflow-design-page.png', {
      maxDiffPixelRatio: 0.05,
      fullPage: true
    });
  });

  test('workflow form page visual regression', async ({ page }) => {
    // Navigate to workflow form page
    await page.goto('/workflow-form');
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for comparison
    await expect(page).toHaveScreenshot('workflow-form-page.png', {
      maxDiffPixelRatio: 0.05,
      fullPage: true
    });
  });

  test('workflow process page visual regression', async ({ page }) => {
    // Navigate to workflow process page
    await page.goto('/workflow-process');
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for comparison
    await expect(page).toHaveScreenshot('workflow-process-page.png', {
      maxDiffPixelRatio: 0.05,
      fullPage: true
    });
  });

  test('form dialog visual regression', async ({ page }) => {
    // Navigate to department page
    await page.goto('/department');
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Open add department dialog
    await page.click('button:has-text("添加部门")');
    
    // Wait for dialog to appear
    await page.waitForSelector('.el-dialog');
    
    // Take screenshot of dialog
    await expect(page.locator('.el-dialog')).toHaveScreenshot('department-dialog.png', {
      maxDiffPixelRatio: 0.05
    });
  });

  test('workflow designer visual regression', async ({ page }) => {
    // Navigate to workflow design page
    await page.goto('/workflow-design');
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Click on create workflow button
    await page.click('button:has-text("创建工作流")');
    
    // Wait for designer to load
    await page.waitForSelector('.workflow-canvas');
    
    // Take screenshot of workflow designer
    await expect(page.locator('.workflow-canvas')).toHaveScreenshot('workflow-designer.png', {
      maxDiffPixelRatio: 0.05
    });
  });
});
