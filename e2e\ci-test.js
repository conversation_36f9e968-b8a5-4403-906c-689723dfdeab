/**
 * CI/CD test runner script
 * This script is designed to run tests in CI/CD environments
 *
 * Usage:
 *   node ci-test.js [options]
 *
 * Options:
 *   --smoke         Run smoke tests only
 *   --regression    Run regression tests
 *   --visual        Run visual regression tests
 *   --api           Run API tests
 *   --a11y          Run accessibility tests
 *   --security      Run security tests
 *   --load          Run load tests
 *   --all           Run all tests (default)
 *   --parallel=N    Run N workers in parallel (default: 1)
 *   --retries=N     Retry failed tests N times (default: 1)
 *   --timeout=N     Set test timeout to N milliseconds (default: 60000)
 *   --shard=M/N     Run tests in shard M of N shards
 *   --project=NAME  Run specific project (admin, user, mobile)
 *   --reporter=NAME Set reporter (default: list,html)
 *   --update-snapshots Update visual test snapshots
 *   --debug         Run in debug mode
 *   --headed        Run in headed mode
 */

const { spawnSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');
const TestReporter = require('./utils/test-reporter');
const TestMonitor = require('./utils/test-monitor');

// Parse command line arguments
const args = process.argv.slice(2);

// Test type flags
const runSmoke = args.includes('--smoke');
const runRegression = args.includes('--regression');
const runVisual = args.includes('--visual');
const runApi = args.includes('--api');
const runA11y = args.includes('--a11y');
const runSecurity = args.includes('--security');
const runLoad = args.includes('--load');
const runAll = args.includes('--all') ||
  (!runSmoke && !runRegression && !runVisual && !runApi && !runA11y && !runSecurity && !runLoad);

// Run options
const updateSnapshots = args.includes('--update-snapshots');
const debugMode = args.includes('--debug');
const headedMode = args.includes('--headed');

// Get numeric parameters
const getParamValue = (paramName) => {
  const param = args.find(arg => arg.startsWith(`--${paramName}=`));
  if (param) {
    const value = param.split('=')[1];
    return isNaN(parseInt(value)) ? null : parseInt(value);
  }
  return null;
};

const getStringParamValue = (paramName) => {
  const param = args.find(arg => arg.startsWith(`--${paramName}=`));
  if (param) {
    return param.split('=')[1];
  }
  return null;
};

const parallelWorkers = getParamValue('parallel') || 1;
const retryCount = getParamValue('retries') || 1;
const testTimeout = getParamValue('timeout') || 60000;
const shardParam = getStringParamValue('shard');
const projectName = getStringParamValue('project');
const reporterName = getStringParamValue('reporter') || 'list,html';

// Parse shard parameter
let shardIndex, shardTotal;
if (shardParam) {
  [shardIndex, shardTotal] = shardParam.split('/').map(Number);
  if (isNaN(shardIndex) || isNaN(shardTotal) || shardIndex < 1 || shardTotal < 1 || shardIndex > shardTotal) {
    console.error('Invalid shard parameter. Format should be --shard=M/N where 1 ≤ M ≤ N');
    process.exit(1);
  }
}

// Test groups
const smokeTests = [
  'login.spec.js',
  'home.spec.js',
  'workflow-form.spec.js'
];

const regressionTests = [
  'department.spec.js',
  'form-design.spec.js',
  'workflow-design.spec.js',
  'workflow-process.spec.js',
  'permissions.spec.js',
  'form-validation.spec.js',
  'error-handling.spec.js'
];

const visualTests = [
  'visual-regression.spec.js'
];

const apiTests = [
  'api/auth.api.spec.js',
  'api/department.api.spec.js',
  'api/workflow.api.spec.js'
];

const a11yTests = [
  'accessibility/core-pages.a11y.spec.js'
];

const securityTests = [
  'security/auth-security.spec.js',
  'security/api-security.spec.js'
];

const loadTests = [
  'load/workflow-load.spec.js'
];

// Determine which tests to run
let testsToRun = [];

if (runSmoke) {
  testsToRun = [...testsToRun, ...smokeTests];
}

if (runRegression) {
  testsToRun = [...testsToRun, ...regressionTests];
}

if (runVisual) {
  testsToRun = [...testsToRun, ...visualTests];
}

if (runApi) {
  testsToRun = [...testsToRun, ...apiTests];
}

if (runA11y) {
  testsToRun = [...testsToRun, ...a11yTests];
}

if (runSecurity) {
  testsToRun = [...testsToRun, ...securityTests];
}

if (runLoad) {
  testsToRun = [...testsToRun, ...loadTests];
}

if (runAll) {
  testsToRun = [
    ...smokeTests,
    ...regressionTests,
    ...visualTests,
    ...apiTests,
    ...a11yTests,
    ...securityTests,
    ...loadTests
  ];
}

// Ensure tests exist
const testsDir = path.join(__dirname, 'tests');
const validTests = testsToRun.filter(test => {
  const testPath = path.join(testsDir, test);
  return fs.existsSync(testPath);
});

if (validTests.length === 0) {
  console.error('No valid tests found to run!');
  process.exit(1);
}

// Apply sharding if specified
let shardedTests = validTests;
if (shardTotal && shardIndex) {
  const testsPerShard = Math.ceil(validTests.length / shardTotal);
  const startIndex = (shardIndex - 1) * testsPerShard;
  const endIndex = Math.min(startIndex + testsPerShard, validTests.length);
  shardedTests = validTests.slice(startIndex, endIndex);

  console.log(`Running shard ${shardIndex}/${shardTotal} with ${shardedTests.length} tests`);

  if (shardedTests.length === 0) {
    console.log('No tests in this shard, exiting with success');
    process.exit(0);
  }
}

// Set CI environment variables
process.env.CI = 'true';

// Create timestamp for reports
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

// Create directory for reports
const reportsDir = path.join(__dirname, 'ci-reports', timestamp);
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
}

// Build command
let command = 'npx playwright test';

// Add test files
if (shardedTests.length > 0) {
  command += ' ' + shardedTests.map(test => `tests/${test}`).join(' ');
}

// Add CI-specific options
command += ` --reporter=${reporterName}`;
command += ` --workers=${parallelWorkers}`;
command += ` --retries=${retryCount}`;
command += ` --timeout=${testTimeout}`;

// Add project if specified
if (projectName) {
  command += ` --project=${projectName}`;
}

// Add update snapshots flag if specified
if (updateSnapshots) {
  command += ' --update-snapshots';
}

// Add debug mode flag if specified
if (debugMode) {
  command += ' --debug';
}

// Add headed mode flag if specified
if (headedMode) {
  command += ' --headed';
}

// Add output directory for reports
command += ` --output=${reportsDir}`;

// Log system info
console.log('=== System Information ===');
console.log(`OS: ${os.type()} ${os.release()}`);
console.log(`CPU: ${os.cpus().length} cores`);
console.log(`Memory: ${Math.round(os.totalmem() / (1024 * 1024 * 1024))} GB`);
console.log(`Node.js: ${process.version}`);
console.log('=========================\n');

// Log test configuration
console.log('=== Test Configuration ===');
console.log(`Running tests in CI mode: ${shardedTests.length} tests`);
console.log(`Parallel workers: ${parallelWorkers}`);
console.log(`Retry count: ${retryCount}`);
console.log(`Test timeout: ${testTimeout}ms`);
if (shardTotal) console.log(`Shard: ${shardIndex}/${shardTotal}`);
if (projectName) console.log(`Project: ${projectName}`);
console.log(`Reporter: ${reporterName}`);
console.log(`Update snapshots: ${updateSnapshots}`);
console.log(`Debug mode: ${debugMode}`);
console.log(`Headed mode: ${headedMode}`);
console.log('=========================\n');

console.log(`Command: ${command}`);

// Create a file to store test results
const resultsFile = path.join(reportsDir, 'test-results.json');

// Start time
const startTime = Date.now();

// Initialize test monitor
const testMonitor = new TestMonitor({
  logDir: path.join(reportsDir, 'logs')
});

// Initialize test reporter
const testReporter = new TestReporter({
  reportDir: reportsDir,
  testRunId: timestamp,
  testType: runSmoke ? 'smoke' :
            runRegression ? 'regression' :
            runVisual ? 'visual' :
            runApi ? 'api' :
            runA11y ? 'a11y' :
            runSecurity ? 'security' :
            runLoad ? 'load' : 'all',
  testConfig: {
    parallelWorkers,
    retryCount,
    testTimeout,
    shard: shardTotal ? `${shardIndex}/${shardTotal}` : null,
    project: projectName,
    reporter: reporterName,
    updateSnapshots,
    debugMode,
    headedMode
  },
  analyticsEnabled: true
});

// Start monitoring
testMonitor.startMonitoring();

// Run tests
try {
  // Run the command
  const result = spawnSync(command, { shell: true, stdio: 'inherit' });

  // End time
  const endTime = Date.now();
  const duration = (endTime - startTime) / 1000;

  // Stop monitoring
  testMonitor.stopMonitoring();

  // Save test results
  const testResults = {
    timestamp: new Date().toISOString(),
    command,
    exitCode: result.status,
    duration,
    success: result.status === 0,
    tests: {
      total: shardedTests.length,
      files: shardedTests
    },
    config: {
      parallelWorkers,
      retryCount,
      testTimeout,
      shard: shardTotal ? `${shardIndex}/${shardTotal}` : null,
      project: projectName,
      reporter: reporterName,
      updateSnapshots,
      debugMode,
      headedMode
    }
  };

  fs.writeFileSync(resultsFile, JSON.stringify(testResults, null, 2));

  // Generate reports with analytics
  testReporter.setTestResults(testResults);
  testReporter.setMonitor(testMonitor);
  const reports = testReporter.generateReports();

  // Log results
  console.log('\n=== Test Results ===');
  console.log(`Duration: ${duration.toFixed(2)} seconds`);
  console.log(`Exit code: ${result.status}`);
  console.log(`Success: ${result.status === 0}`);
  console.log(`HTML report: ${reports.html.path}`);
  console.log(`JSON report: ${reports.json.path}`);
  console.log(`Report directory: ${reportsDir}`);
  console.log('===================\n');

  if (result.status === 0) {
    console.log('Tests completed successfully!');
    process.exit(0);
  } else {
    console.error('Tests failed!');
    process.exit(result.status);
  }
} catch (error) {
  // End time
  const endTime = Date.now();
  const duration = (endTime - startTime) / 1000;

  // Stop monitoring
  testMonitor.stopMonitoring();

  // Save error results
  const errorResults = {
    timestamp: new Date().toISOString(),
    command,
    exitCode: 1,
    duration,
    success: false,
    error: error.message,
    tests: {
      total: shardedTests.length,
      files: shardedTests
    },
    config: {
      parallelWorkers,
      retryCount,
      testTimeout,
      shard: shardTotal ? `${shardIndex}/${shardTotal}` : null,
      project: projectName,
      reporter: reporterName,
      updateSnapshots,
      debugMode,
      headedMode
    }
  };

  fs.writeFileSync(resultsFile, JSON.stringify(errorResults, null, 2));

  // Generate reports with analytics
  testReporter.setTestResults(errorResults);
  testReporter.setMonitor(testMonitor);
  const reports = testReporter.generateReports();

  console.error('Tests failed with error:', error.message);
  console.log(`HTML report: ${reports.html.path}`);
  console.log(`JSON report: ${reports.json.path}`);
  console.log(`Report directory: ${reportsDir}`);
  process.exit(1);
}
