/**
 * <PERSON><PERSON><PERSON> to check users in the database
 */
const { sequelize, User, Role } = require('./src/models');

async function checkUsers() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Get all users
    console.log('Fetching all users...');
    const users = await User.findAll({
      include: [{ model: Role, as: 'roles' }]
    });

    console.log(`Found ${users.length} users:`);
    users.forEach(user => {
      console.log(`- ID: ${user.id}, Username: ${user.username}, Email: ${user.email}, Status: ${user.status}`);
      console.log(`  Roles: ${user.roles.map(role => role.name).join(', ') || 'No roles assigned'}`);
    });

    // Check for specific user
    console.log('\nChecking for user "rsb"...');
    const rsbUser = await User.findOne({
      where: { username: 'rsb' },
      include: [{ model: Role, as: 'roles' }]
    });

    if (rsbUser) {
      console.log('User "rsb" found:');
      console.log(`- ID: ${rsbUser.id}, Username: ${rsbUser.username}, Email: ${rsbUser.email}, Status: ${rsbUser.status}`);
      console.log(`  Roles: ${rsbUser.roles.map(role => role.name).join(', ') || 'No roles assigned'}`);
    } else {
      console.log('User "rsb" not found in the database.');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

checkUsers();
