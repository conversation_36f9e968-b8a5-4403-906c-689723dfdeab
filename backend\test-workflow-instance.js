/**
 * Test WorkflowInstance model
 * Run with: node test-workflow-instance.js
 */

const { WorkflowInstance, WorkflowTemplate, FormTemplate } = require('./src/models');

async function testWorkflowInstance() {
  try {
    console.log('=== Testing WorkflowInstance Model ===');
    
    // Get workflow instance 81
    console.log('\n1. Getting workflow instance 81...');
    const instance = await WorkflowInstance.findByPk(81);
    
    if (!instance) {
      console.log('Workflow instance 81 not found');
      return;
    }
    
    console.log('Found workflow instance:');
    console.log(`- ID: ${instance.id}`);
    console.log(`- Title: ${instance.title}`);
    console.log(`- Workflow template ID: ${instance.workflow_template_id}`);
    console.log(`- Has form_data: ${!!instance.form_data}`);
    
    // Get workflow template
    console.log(`\n2. Getting workflow template ${instance.workflow_template_id}...`);
    const template = await WorkflowTemplate.findByPk(instance.workflow_template_id);
    
    if (!template) {
      console.log(`Workflow template ${instance.workflow_template_id} not found`);
      return;
    }
    
    console.log('Found workflow template:');
    console.log(`- ID: ${template.id}`);
    console.log(`- Title: ${template.title}`);
    console.log(`- Form template ID: ${template.form_template_id}`);
    
    // Get form template
    console.log(`\n3. Getting form template ${template.form_template_id}...`);
    const formTemplate = await FormTemplate.findByPk(template.form_template_id);
    
    if (!formTemplate) {
      console.log(`Form template ${template.form_template_id} not found`);
      return;
    }
    
    console.log('Found form template:');
    console.log(`- ID: ${formTemplate.id}`);
    console.log(`- Title: ${formTemplate.title}`);
    console.log(`- Has schema: ${!!formTemplate.schema}`);
    
    if (formTemplate.schema) {
      console.log('- Schema type:', typeof formTemplate.schema);
      console.log('- Schema keys:', Object.keys(formTemplate.schema));
    }
    
    console.log('\n=== Test completed ===');
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run the test
testWorkflowInstance(); 