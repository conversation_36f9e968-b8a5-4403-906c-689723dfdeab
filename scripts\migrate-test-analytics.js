/**
 * Test Analytics Migration Script
 * 
 * This script runs the database migration for the test analytics tables.
 * 
 * Usage:
 *   node scripts/migrate-test-analytics.js
 */

const { execSync } = require('child_process');
const path = require('path');

// Set environment variables
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

console.log('=== Test Analytics Migration ===');
console.log(`Environment: ${process.env.NODE_ENV}`);
console.log('Running migration...');

try {
  // Run the migration
  execSync('npx sequelize-cli db:migrate --name 20250501000000-create-test-analytics-tables.js', {
    cwd: path.join(__dirname, '../backend'),
    stdio: 'inherit'
  });
  
  console.log('Migration completed successfully!');
} catch (error) {
  console.error('Migration failed:', error.message);
  process.exit(1);
}
