/**
 * Test fully loaded WorkflowInstance model
 * Run with: node test-workflow-instance-full.js
 */

const { WorkflowInstance, WorkflowTemplate, FormTemplate, FormField } = require('./src/models');

async function testWorkflowInstanceFull() {
  try {
    console.log('=== Testing Fully Loaded WorkflowInstance ===');
    
    // Get workflow instance 81 with all related data
    console.log('\n1. Getting workflow instance 81 with all related data...');
    const instance = await WorkflowInstance.findByPk(81, {
      include: [
        {
          model: WorkflowTemplate,
          as: 'workflowTemplate',
          include: [
            {
              model: FormTemplate,
              as: 'formTemplate',
              include: [
                {
                  model: FormField,
                  as: 'fields'
                }
              ]
            }
          ]
        }
      ]
    });
    
    if (!instance) {
      console.log('Workflow instance 81 not found');
      return;
    }
    
    console.log('Found workflow instance:');
    console.log(`- ID: ${instance.id}`);
    console.log(`- Title: ${instance.title}`);
    console.log(`- Has form_data: ${!!instance.form_data}`);
    
    // Check workflow template
    console.log('\n2. Checking workflow template...');
    if (instance.workflowTemplate) {
      console.log('- Workflow template found:');
      console.log(`  - ID: ${instance.workflowTemplate.id}`);
      console.log(`  - Title: ${instance.workflowTemplate.title}`);
      console.log(`  - Form template ID: ${instance.workflowTemplate.form_template_id}`);
      
      // Check form template
      console.log('\n3. Checking form template...');
      if (instance.workflowTemplate.formTemplate) {
        console.log('- Form template found:');
        console.log(`  - ID: ${instance.workflowTemplate.formTemplate.id}`);
        console.log(`  - Title: ${instance.workflowTemplate.formTemplate.title}`);
        console.log(`  - Has schema: ${!!instance.workflowTemplate.formTemplate.schema}`);
        
        if (instance.workflowTemplate.formTemplate.schema) {
          console.log('  - Schema type:', typeof instance.workflowTemplate.formTemplate.schema);
          console.log('  - Schema keys:', Object.keys(instance.workflowTemplate.formTemplate.schema));
        }
        
        if (instance.workflowTemplate.formTemplate.fields && 
            instance.workflowTemplate.formTemplate.fields.length > 0) {
          console.log(`  - Fields count: ${instance.workflowTemplate.formTemplate.fields.length}`);
          console.log('  - Field keys:', instance.workflowTemplate.formTemplate.fields.map(f => f.field_key).join(', '));
        } else {
          console.log('  - No fields found');
        }
      } else {
        console.log('- Form template not found in workflow template');
      }
    } else {
      console.log('- Workflow template not found in instance');
    }
    
    // Create modified response like we want in the controller
    console.log('\n4. Creating modified response...');
    const responseData = instance.toJSON();
    
    // Step 1: Try to get form template directly if not included
    if (responseData.workflowTemplate && 
        responseData.workflowTemplate.form_template_id && 
        !responseData.workflowTemplate.formTemplate) {
      console.log('- Form template not included, fetching directly...');
      
      const formTemplate = await FormTemplate.findByPk(responseData.workflowTemplate.form_template_id);
      
      if (formTemplate) {
        console.log(`- Found form template directly: ${formTemplate.id}`);
        responseData.form_schema = formTemplate.schema;
        console.log('- Added form_schema to response');
        
        if (responseData.form_data) {
          const formDataValues = {...responseData.form_data};
          responseData.form_data = {
            values: formDataValues,
            schema: formTemplate.schema
          };
          console.log('- Reformatted form_data with schema');
        }
      } else {
        console.log('- Form template not found directly');
      }
    } 
    // Step 2: Use included form template
    else if (responseData.workflowTemplate && 
             responseData.workflowTemplate.formTemplate && 
             responseData.workflowTemplate.formTemplate.schema) {
      console.log('- Form template included, using directly...');
      
      responseData.form_schema = responseData.workflowTemplate.formTemplate.schema;
      console.log('- Added form_schema to response');
      
      if (responseData.form_data) {
        const formDataValues = {...responseData.form_data};
        responseData.form_data = {
          values: formDataValues,
          schema: responseData.workflowTemplate.formTemplate.schema
        };
        console.log('- Reformatted form_data with schema');
      }
    } else {
      console.log('- Cannot add form schema to response');
    }
    
    // Check if our response has the form_schema now
    console.log('\n5. Checking final response...');
    console.log(`- Has form_schema: ${!!responseData.form_schema}`);
    console.log(`- Form data format: ${responseData.form_data && responseData.form_data.values ? 'values+schema' : 'raw'}`);
    
    console.log('\n=== Test completed ===');
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run the test
testWorkflowInstanceFull(); 