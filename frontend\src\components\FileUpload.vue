<template>
  <div class="file-upload-container">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="headers"
      :multiple="multiple"
      :limit="limit"
      :file-list="fileList"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-exceed="handleExceed"
      :before-upload="beforeUpload"
      :accept="accept"
      :disabled="disabled"
      :drag="drag"
      :auto-upload="autoUpload"
      :list-type="listType"
      class="file-uploader"
    >
      <template #trigger>
        <el-button type="primary" :disabled="disabled">
          <el-icon><i-ep-upload /></el-icon>
          {{ buttonText }}
        </el-button>
      </template>
      
      <template #tip v-if="showTip">
        <div class="el-upload__tip">
          {{ tipText }}
        </div>
      </template>
      
      <template #default v-if="drag">
        <el-icon class="el-icon--upload"><i-ep-upload-filled /></el-icon>
        <div class="el-upload__text">
          拖拽文件到此处或 <em>点击上传</em>
        </div>
      </template>
    </el-upload>
    
    <!-- 文件预览对话框 -->
    <el-dialog v-model="previewVisible" title="文件预览" width="50%">
      <div class="preview-container" v-if="previewFile">
        <!-- 图片预览 -->
        <img 
          v-if="isImage(previewFile)" 
          :src="previewFile.url" 
          class="preview-image" 
          alt="预览图片"
        />
        
        <!-- PDF预览 -->
        <iframe 
          v-else-if="isPdf(previewFile)" 
          :src="previewFile.url" 
          class="preview-pdf" 
          frameborder="0"
        ></iframe>
        
        <!-- 其他文件类型 -->
        <div v-else class="preview-other">
          <el-icon class="file-icon"><i-ep-document /></el-icon>
          <p>{{ previewFile.name }}</p>
          <el-button type="primary" @click="downloadFile(previewFile)">
            下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // 上传URL
  action: {
    type: String,
    default: '/api/upload'
  },
  // 是否支持多文件上传
  multiple: {
    type: Boolean,
    default: false
  },
  // 最大上传数量
  limit: {
    type: Number,
    default: 5
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: ''
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否支持拖拽上传
  drag: {
    type: Boolean,
    default: false
  },
  // 是否自动上传
  autoUpload: {
    type: Boolean,
    default: true
  },
  // 按钮文本
  buttonText: {
    type: String,
    default: '选择文件'
  },
  // 提示文本
  tipText: {
    type: String,
    default: '支持上传JPG/PNG/PDF文件，单个文件不超过10MB'
  },
  // 是否显示提示
  showTip: {
    type: Boolean,
    default: true
  },
  // 列表类型
  listType: {
    type: String,
    default: 'text', // text, picture, picture-card
  },
  // 初始文件列表
  initialFiles: {
    type: Array,
    default: () => []
  },
  // 最大文件大小(MB)
  maxSize: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits([
  'update:files', 
  'upload-success', 
  'upload-error', 
  'file-remove',
  'file-preview'
])

// 上传组件引用
const uploadRef = ref(null)

// 文件列表
const fileList = ref([...props.initialFiles])

// 预览相关
const previewVisible = ref(false)
const previewFile = ref(null)

// 计算上传URL
const uploadUrl = computed(() => props.action)

// 计算请求头
const headers = computed(() => {
  return {
    Authorization: `Bearer ${localStorage.getItem('token') || ''}`
  }
})

// 初始化
onMounted(() => {
  // 如果有初始文件，更新文件列表
  if (props.initialFiles && props.initialFiles.length > 0) {
    fileList.value = [...props.initialFiles]
    emit('update:files', fileList.value)
  }
})

// 上传前验证
const beforeUpload = (file) => {
  // 检查文件大小
  const isLessThanMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLessThanMaxSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB!`)
    return false
  }
  
  // 如果设置了accept属性，检查文件类型
  if (props.accept) {
    const acceptTypes = props.accept.split(',')
    const fileExt = file.name.substring(file.name.lastIndexOf('.'))
    const fileType = file.type
    
    // 检查文件扩展名或MIME类型是否在接受列表中
    const isAccepted = acceptTypes.some(type => {
      return type.trim() === fileExt || 
             type.trim() === fileType || 
             (type.trim().startsWith('.') && file.name.endsWith(type.trim())) ||
             (type.trim().includes('/*') && fileType.startsWith(type.trim().split('/*')[0]))
    })
    
    if (!isAccepted) {
      ElMessage.error(`只能上传 ${props.accept} 格式的文件!`)
      return false
    }
  }
  
  return true
}

// 上传成功处理
const handleSuccess = (response, uploadFile, uploadFiles) => {
  if (response.success) {
    ElMessage.success('文件上传成功')
    fileList.value = uploadFiles
    emit('update:files', fileList.value)
    emit('upload-success', response, uploadFile, uploadFiles)
  } else {
    ElMessage.error(response.message || '文件上传失败')
    // 从文件列表中移除上传失败的文件
    const index = fileList.value.findIndex(file => file.uid === uploadFile.uid)
    if (index !== -1) {
      fileList.value.splice(index, 1)
    }
    emit('upload-error', response, uploadFile, uploadFiles)
  }
}

// 上传错误处理
const handleError = (error, uploadFile, uploadFiles) => {
  ElMessage.error('文件上传失败: ' + (error.message || '未知错误'))
  emit('upload-error', error, uploadFile, uploadFiles)
}

// 文件数量超出限制
const handleExceed = (files) => {
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
}

// 移除文件
const handleRemove = (uploadFile, uploadFiles) => {
  fileList.value = uploadFiles
  emit('update:files', fileList.value)
  emit('file-remove', uploadFile, uploadFiles)
}

// 预览文件
const handlePreview = (uploadFile) => {
  previewFile.value = uploadFile
  previewVisible.value = true
  emit('file-preview', uploadFile)
}

// 判断是否为图片
const isImage = (file) => {
  return file && file.name && /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(file.name)
}

// 判断是否为PDF
const isPdf = (file) => {
  return file && file.name && /\.pdf$/i.test(file.name)
}

// 下载文件
const downloadFile = (file) => {
  if (file && file.url) {
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 手动上传方法
const submit = () => {
  if (uploadRef.value) {
    uploadRef.value.submit()
  }
}

// 清空上传列表
const clearFiles = () => {
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
    fileList.value = []
    emit('update:files', fileList.value)
  }
}

// 暴露方法给父组件
defineExpose({
  submit,
  clearFiles,
  uploadRef
})
</script>

<style scoped>
.file-upload-container {
  width: 100%;
}

.file-uploader {
  width: 100%;
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-height: 70vh;
  overflow: auto;
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
}

.preview-pdf {
  width: 100%;
  height: 60vh;
}

.preview-other {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.file-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.el-upload__tip {
  line-height: 1.5;
  margin-top: 8px;
  color: #909399;
}
</style>
