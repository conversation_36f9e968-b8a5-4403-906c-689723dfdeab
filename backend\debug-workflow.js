/**
 * Debug script for workflow API
 * Run with: node debug-workflow.js
 */

const { WorkflowTemplate, WorkflowNode, WorkflowTransition, WorkflowInstance, WorkflowTask, WorkflowTaskHistory, User, sequelize } = require('./src/models');

// Set up a test user
const testUser = {
  id: 1,
  username: 'admin',
  roles: ['admin']
};

// Test data for workflow instance creation
const testData = {
  title: 'Debug Test Workflow Instance',
  form_data: {
    // Add sample form data based on your form structure
    test_field: 'Test value',
    test_textarea: 'Test description',
    test_select: 'option1'
  }
};

// Debug function to simulate the startWorkflowInstance controller
async function debugStartWorkflowInstance(workflowId, data, user) {
  try {
    console.log(`Starting workflow instance for workflow ID: ${workflowId}`);
    console.log('Request data:', JSON.stringify(data, null, 2));

    // Find the workflow template
    const workflowTemplate = await WorkflowTemplate.findByPk(workflowId, {
      include: [
        {
          model: WorkflowNode,
          as: 'nodes',
          where: { node_type: 'start' },
          required: false
        }
      ]
    });

    if (!workflowTemplate) {
      console.error('Workflow template not found');
      return;
    }

    console.log('Found workflow template:', workflowTemplate.title);

    if (workflowTemplate.status !== 'published') {
      console.error('Workflow template is not published');
      return;
    }

    // Find the start node
    const startNode = workflowTemplate.nodes.find(node => node.node_type === 'start');
    if (!startNode) {
      console.error('Start node not found in workflow template');
      return;
    }

    console.log('Found start node:', startNode.name);

    // Create workflow instance
    const workflowInstance = await WorkflowInstance.create({
      workflow_template_id: workflowTemplate.id,
      title: data.title || `${workflowTemplate.title} - ${new Date().toISOString()}`,
      initiator_id: user.id,
      current_node_id: startNode.id,
      status: 'running',
      form_data: data.form_data
    });

    console.log('Created workflow instance:', workflowInstance.id);

    // Find next transitions
    const nextTransitions = await WorkflowTransition.findAll({
      where: {
        workflow_template_id: workflowTemplate.id,
        source_node_id: startNode.id
      },
      include: [
        {
          model: WorkflowNode,
          as: 'targetNode'
        }
      ]
    });

    console.log(`Found ${nextTransitions.length} next transitions`);

    // Create start task
    const startTask = await WorkflowTask.create({
      workflow_instance_id: workflowInstance.id,
      node_id: startNode.id,
      assignee_id: user.id,
      status: 'completed',
      completed_at: new Date()
    });

    console.log('Created start task:', startTask.id);

    // Create task history
    await WorkflowTaskHistory.create({
      workflow_instance_id: workflowInstance.id,
      task_id: startTask.id,
      node_id: startNode.id,
      operator_id: user.id,
      operation: 'start',
      comments: 'Starting workflow for debugging',
      form_data: data.form_data
    });

    console.log('Created task history record');

    // Process next nodes
    if (nextTransitions.length > 0) {
      for (const transition of nextTransitions) {
        const targetNode = transition.targetNode;
        console.log('Processing next node:', targetNode.name);

        // Determine assignee
        let assigneeId = null;
        let assigneeRoleId = null;
        let assigneeDepartmentId = null;

        if (targetNode.config) {
          const config = targetNode.config;

          if (config.assignee_type === 'user' && config.assignee_id) {
            assigneeId = config.assignee_id;
          } else if (config.assignee_type === 'role' && config.assignee_role_id) {
            assigneeRoleId = config.assignee_role_id;
          } else if (config.assignee_type === 'department' && config.assignee_department_id) {
            assigneeDepartmentId = config.assignee_department_id;
          }
        }

        // Create task for next node
        await WorkflowTask.create({
          workflow_instance_id: workflowInstance.id,
          node_id: targetNode.id,
          assignee_id: assigneeId,
          assignee_role_id: assigneeRoleId,
          assignee_department_id: assigneeDepartmentId,
          status: 'pending',
          due_date: targetNode.config && targetNode.config.due_days ?
            new Date(Date.now() + targetNode.config.due_days * 24 * 60 * 60 * 1000) : null,
          priority: targetNode.config && targetNode.config.priority ? targetNode.config.priority : 'normal'
        });

        console.log('Created task for next node');

        // Update workflow instance current node
        await workflowInstance.update({
          current_node_id: targetNode.id
        });

        console.log('Updated workflow instance current node');
      }
    }

    console.log('Workflow instance started successfully');
    return workflowInstance;
  } catch (error) {
    console.error('Error starting workflow instance:', error);
    throw error;
  }
}

// Main function to run the debug
async function main() {
  try {
    // Get all workflow templates
    const workflows = await WorkflowTemplate.findAll({
      where: { status: 'published' },
      include: [
        {
          model: WorkflowNode,
          as: 'nodes'
        }
      ]
    });

    if (workflows.length === 0) {
      console.log('No published workflows found');
      return;
    }

    console.log(`Found ${workflows.length} published workflows`);

    // Print details about each workflow and its nodes
    for (const workflow of workflows) {
      console.log(`\nWorkflow ID: ${workflow.id}, Title: ${workflow.title}`);
      console.log(`Nodes (${workflow.nodes.length}):`);

      for (const node of workflow.nodes) {
        console.log(`  - Node ID: ${node.id}, Key: ${node.node_key}, Type: ${node.node_type}, Name: ${node.name}`);
      }

      // Check if workflow has a start node
      const hasStartNode = workflow.nodes.some(node => node.node_type === 'start');
      console.log(`  Has start node: ${hasStartNode}`);
    }

    // Find a workflow with a start node
    let workflowId = null;
    for (const workflow of workflows) {
      if (workflow.nodes.some(node => node.node_type === 'start')) {
        workflowId = workflow.id;
        break;
      }
    }

    if (!workflowId) {
      console.log('No workflow with a start node found');
      return;
    }

    console.log(`\nUsing workflow ID ${workflowId} for testing`);

    // Start a workflow instance
    await debugStartWorkflowInstance(workflowId, testData, testUser);

    console.log('Debug completed successfully');
  } catch (error) {
    console.error('Debug failed:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the main function
main();
