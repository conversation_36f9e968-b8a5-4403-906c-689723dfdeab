<template>
  <div class="workflow-form-container">
    <el-row :gutter="20" class="mb-4">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>工作流填写</span>
              <el-button
                type="primary"
                @click="handleCreateInstance">
                发起工作流
              </el-button>
            </div>
          </template>

          <el-tabs v-model="activeTab">
            <el-tab-pane label="可发起的工作流" name="available">
              <el-table
                :data="availableWorkflows"
                border
                v-loading="loading">
                <el-table-column prop="title" label="工作流名称" width="180" />
                <el-table-column prop="description" label="描述" />
                <el-table-column label="关联表单" width="150">
                  <template #default="scope">
                    {{ scope.row.formTemplate ? scope.row.formTemplate.title : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="创建者" width="120">
                  <template #default="scope">
                    {{ scope.row.creator ? scope.row.creator.full_name : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="version" label="版本" width="80" />
                <el-table-column label="更新时间" width="180">
                  <template #default="scope">
                    {{ formatDate(scope.row.updated_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                  <template #default="scope">
                    <el-button
                      size="small"
                      @click="handleStartWorkflow(scope.row)"
                      type="primary">
                      发起
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>

            <el-tab-pane label="我发起的工作流" name="initiated">
              <el-table
                :data="initiatedInstances"
                border
                v-loading="initiatedLoading">
                <el-table-column prop="title" label="工作流标题" width="180" />
                <el-table-column label="工作流类型" width="150">
                  <template #default="scope">
                    {{ scope.row.workflowTemplate ? scope.row.workflowTemplate.title : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="当前节点" width="120">
                  <template #default="scope">
                    {{ scope.row.currentNode ? scope.row.currentNode.name :
                       (scope.row.current_node_id ? '处理中' : '-') }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                      {{ getStatusText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="发起时间" width="180">
                  <template #default="scope">
                    {{ formatDate(scope.row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                  <template #default="scope">
                    <el-button
                      size="small"
                      @click.stop.prevent="() => handleViewInstance(scope.row)"
                      type="primary">
                      查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <!-- 发起工作流对话框 -->
    <el-dialog
      v-model="startDialogVisible"
      title="发起工作流"
      width="80%"
      :close-on-click-modal="false"
      @closed="handleDialogClosed">
      <el-skeleton :rows="10" animated v-if="formLoading" />
      <div v-else-if="currentWorkflow && currentForm">
        <h2>{{ currentWorkflow.title }}</h2>
        <p v-if="currentWorkflow.description">{{ currentWorkflow.description }}</p>

        <el-alert
          v-if="formLoadError"
          title="表单加载出错"
          type="error"
          description="获取表单数据时发生错误，请刷新页面重试"
          show-icon
          :closable="false"
          class="mb-4"
        />

        <el-form
          :model="formData"
          :rules="formRules"
          ref="formRef"
          label-width="120px">
          <el-form-item label="工作流标题" prop="title">
            <el-input v-model="formData.title" placeholder="请输入工作流标题"></el-input>
          </el-form-item>

          <el-divider>表单内容</el-divider>

          <div v-if="formFields.length === 0" class="empty-form-message">
            <el-empty description="没有表单字段" />
            <p class="text-center">此工作流没有定义表单字段，只需填写标题即可提交</p>
          </div>

          <template v-for="field in formFields" :key="field.field_key">
            <el-form-item
              :label="field.label"
              :prop="'form_data.' + field.field_key"
              :rules="getFieldRules(field)">

              <!-- 文本输入框 -->
              <div v-if="field.field_type === 'input'">
                <el-input
                  v-model="formData.form_data[field.field_key]"
                  :placeholder="field.placeholder || '请输入' + field.label"></el-input>
                <div v-if="field.description" class="field-help-text">{{ field.description }}</div>
              </div>

              <!-- 文本域 -->
              <div v-else-if="field.field_type === 'textarea'">
                <el-input
                  v-model="formData.form_data[field.field_key]"
                  type="textarea"
                  :rows="3"
                  :placeholder="field.placeholder || '请输入' + field.label"></el-input>
                <div v-if="field.description" class="field-help-text">{{ field.description }}</div>
              </div>

              <!-- 数字输入框 -->
              <div v-else-if="field.field_type === 'number'">
                <el-input-number
                  v-model="formData.form_data[field.field_key]"
                  :min="field.validation_rules?.min"
                  :max="field.validation_rules?.max"
                  :step="field.validation_rules?.step || 1"
                  :precision="field.validation_rules?.precision"
                  :placeholder="field.placeholder || '请输入' + field.label"></el-input-number>
                <div v-if="field.description" class="field-help-text">{{ field.description }}</div>
              </div>

              <!-- 单选框组 -->
              <el-radio-group
                v-else-if="field.field_type === 'radio'"
                v-model="formData.form_data[field.field_key]">
                <el-radio
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.value">
                  {{ option.label }}
                </el-radio>
              </el-radio-group>

              <!-- 复选框组 -->
              <el-checkbox-group
                v-else-if="field.field_type === 'checkbox'"
                v-model="formData.form_data[field.field_key]">
                <el-checkbox
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.value">
                  {{ option.label }}
                </el-checkbox>
              </el-checkbox-group>

              <!-- 下拉选择框 -->
              <div v-else-if="field.field_type === 'select'">
                <el-select
                  v-model="formData.form_data[field.field_key]"
                  :placeholder="field.placeholder || '请选择' + field.label"
                  style="width: 100%"
                  :clearable="!field.is_required"
                  :filterable="field.options && field.options.length > 10">
                  <el-option
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"></el-option>
                </el-select>
                <div v-if="field.description" class="field-help-text">{{ field.description }}</div>
              </div>

              <!-- 日期选择器 -->
              <div v-else-if="field.field_type === 'date'">
                <el-date-picker
                  v-model="formData.form_data[field.field_key]"
                  type="date"
                  :placeholder="field.placeholder || '请选择日期'"
                  :clearable="!field.is_required"
                  :disabled-date="field.validation_rules?.disabledDate ?
                    (time => new Function('time', field.validation_rules.disabledDate)(time)) : undefined"
                  style="width: 100%"></el-date-picker>
                <div v-if="field.description" class="field-help-text">{{ field.description }}</div>
              </div>

              <!-- 时间选择器 -->
              <el-time-picker
                v-else-if="field.field_type === 'time'"
                v-model="formData.form_data[field.field_key]"
                :placeholder="field.placeholder || '请选择时间'"
                style="width: 100%"></el-time-picker>

              <!-- 日期时间选择器 -->
              <el-date-picker
                v-else-if="field.field_type === 'datetime'"
                v-model="formData.form_data[field.field_key]"
                type="datetime"
                :placeholder="field.placeholder || '请选择日期时间'"
                style="width: 100%"></el-date-picker>

              <!-- 开关 -->
              <el-switch
                v-else-if="field.field_type === 'switch'"
                v-model="formData.form_data[field.field_key]"></el-switch>

              <!-- 滑块 -->
              <el-slider
                v-else-if="field.field_type === 'slider'"
                v-model="formData.form_data[field.field_key]"></el-slider>

              <!-- 评分 -->
              <el-rate
                v-else-if="field.field_type === 'rate'"
                v-model="formData.form_data[field.field_key]"></el-rate>

              <!-- 文件上传 -->
              <div v-else-if="field.field_type === 'file'">
                <FileUpload
                  ref="fileUploadRef"
                  :action="getFileUploadUrl()"
                  :accept="field.accept || '.jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx'"
                  :multiple="field.multiple || false"
                  :limit="field.limit || 5"
                  :max-size="field.max_size || 10"
                  :auto-upload="false"
                  :drag="true"
                  :button-text="'选择文件'"
                  :tip-text="field.description || '支持上传各种文件格式，单个文件不超过10MB'"
                  @update:files="handleFileListChange(field.field_key, $event)"
                />
              </div>
            </el-form-item>
          </template>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancelWorkflow">取消</el-button>
          <el-button type="primary" @click="submitWorkflow" :loading="submitting">
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看工作流实例对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="工作流详情"
      width="80%"
      destroy-on-close>
      <div v-if="currentInstance">
        <h2>{{ currentInstance.title || '无标题' }}</h2>

        <el-divider>基本信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工作流类型">{{ currentInstance.workflowTemplate ? currentInstance.workflowTemplate.title : '-' }}</el-descriptions-item>
          <el-descriptions-item label="当前节点">{{ currentInstance.currentNode ? currentInstance.currentNode.name : '-' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentInstance.status)">
              {{ getStatusText(currentInstance.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发起人">{{ currentInstance.initiator ? currentInstance.initiator.full_name : '-' }}</el-descriptions-item>
          <el-descriptions-item label="发起时间">{{ formatDate(currentInstance.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ currentInstance.completed_at ? formatDate(currentInstance.completed_at) : '-' }}</el-descriptions-item>
        </el-descriptions>

        <el-tabs class="workflow-detail-tabs" style="margin-top: 20px;">
          <el-tab-pane label="表单数据">
            <FormDataDisplay
              :form-data="currentInstance.form_data"
              :schema="currentInstance.form_schema || currentInstance.workflow_schema"
            />
          </el-tab-pane>
          <el-tab-pane label="流程图">
            <WorkflowVisualizer :workflow-instance="currentInstance" />
          </el-tab-pane>
          <el-tab-pane label="统计信息">
            <WorkflowMetrics :workflow-instance="currentInstance" />
          </el-tab-pane>
        </el-tabs>

        <el-divider>流转记录</el-divider>
        <div v-if="currentInstance && currentInstance.taskHistories" class="timeline-container">
          <div v-if="Array.isArray(currentInstance.taskHistories) && currentInstance.taskHistories.length > 0">
            <div class="safe-timeline">
              <div class="timeline-item" v-for="(history, index) in safeTaskHistories" :key="index">
                <div class="timeline-marker" :class="['marker-' + (history.operation ? safeTimelineType(history.operation) : 'info')]"></div>
                <div class="timeline-content">
                  <div class="timeline-time">{{ history.created_at ? formatDate(history.created_at) : '未知时间' }}</div>
                  <h4 class="timeline-title">
                    {{ history.node_name || '未知节点' }} - {{ history.operation_text || '未知操作' }}
                  </h4>
                  <p v-if="history.operator_name">操作人: {{ history.operator_name }}</p>
                  <p v-if="history.comments">备注: {{ history.comments }}</p>
                </div>
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无流转记录"></el-empty>
        </div>
      </div>
      <div v-else class="loading-message">
        <el-empty description="加载中..."></el-empty>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { workflowApi, formApi, userApi } from '@/api'
import FileUpload from '@/components/FileUpload.vue'
import WorkflowVisualizer from '@/components/WorkflowVisualizer.vue'
import WorkflowMetrics from '@/components/WorkflowMetrics.vue'
import FormDataDisplay from '@/components/FormDataDisplay.vue'

// 数据
const activeTab = ref('available')
const availableWorkflows = ref([])
const initiatedInstances = ref([])
const loading = ref(false)
const initiatedLoading = ref(false)
const startDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const submitting = ref(false)
const formLoading = ref(false)
const formLoadError = ref(false)
const currentWorkflow = ref(null)
const currentForm = ref(null)
const formFields = ref([])
const currentInstance = ref(null)
const usersCache = reactive({})

// 表单数据
const formRef = ref(null)
const formData = reactive({
  title: '',
  form_data: {}
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入工作流标题', trigger: 'blur' }
  ]
}

// 初始化
onMounted(async () => {
  await fetchAvailableWorkflows()
  await fetchInitiatedInstances()
})

// 获取可发起的工作流列表
const fetchAvailableWorkflows = async () => {
  loading.value = true
  try {
    const result = await workflowApi.getAllWorkflows()
    if (result.success) {
      // 只显示已发布的工作流
      availableWorkflows.value = result.data.filter(workflow => workflow.status === 'published')
    }
  } catch (error) {
    console.error('获取可发起的工作流列表失败:', error)
    ElMessage.error('获取可发起的工作流列表失败')
  } finally {
    loading.value = false
  }
}

// 获取我发起的工作流实例
const fetchInitiatedInstances = async () => {
  initiatedLoading.value = true
  try {
    console.log('开始获取我发起的工作流实例...')
    // 调用获取用户发起的工作流实例的API
    const result = await workflowApi.getUserInitiatedInstances()
    console.log('获取到的工作流实例数据:', result)

    if (result.success) {
      // 处理可能的不同数据结构
      if (Array.isArray(result.data)) {
        // 直接是数组
        initiatedInstances.value = result.data
      } else if (result.data && Array.isArray(result.data.instances)) {
        // 有instances属性的对象
        initiatedInstances.value = result.data.instances
      } else if (result.data && result.data.total !== undefined && Array.isArray(result.data.data)) {
        // 分页对象
        initiatedInstances.value = result.data.data
      } else if (result.data) {
        // 尝试将data作为对象数组处理
        initiatedInstances.value = Array.isArray(result.data) ? result.data : [result.data]
      } else {
        // 无法识别的格式，初始化为空数组
        console.warn('无法识别的工作流实例数据格式')
        initiatedInstances.value = []
      }

      console.log('处理后的工作流实例数组:', initiatedInstances.value)
      console.log('实例数量:', initiatedInstances.value.length)

      // 检查每个实例是否有id
      initiatedInstances.value.forEach((instance, index) => {
        if (!instance.id) {
          console.warn(`警告: 索引 ${index} 的实例缺少ID`, instance)
        }
      })
    } else {
      console.error('获取工作流实例失败:', result.message)
      ElMessage.error(result.message || '获取我发起的工作流实例失败')
      initiatedInstances.value = []
    }
  } catch (error) {
    console.error('获取我发起的工作流实例失败:', error)
    console.error('错误详情:', error.response ? error.response.data : error.message)
    ElMessage.error('获取我发起的工作流实例失败')
    initiatedInstances.value = [] // 确保失败时也有初始值
  } finally {
    initiatedLoading.value = false
  }
}

// 打开发起工作流对话框
const handleCreateInstance = () => {
  if (availableWorkflows.value.length === 0) {
    ElMessage.warning('没有可发起的工作流')
    return
  }

  ElMessageBox.prompt('请选择要发起的工作流', '发起工作流', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'select',
    inputValue: availableWorkflows.value[0].id,
    inputPlaceholder: '请选择工作流',
    inputOptions: availableWorkflows.value.map(workflow => ({
      label: workflow.title,
      value: workflow.id
    }))
  }).then(({ value }) => {
    const workflow = availableWorkflows.value.find(w => w.id === value)
    if (workflow) {
      handleStartWorkflow(workflow)
    }
  }).catch(() => {
    // 取消选择
  })
}

// 开始发起工作流
const handleStartWorkflow = async (workflow) => {
  currentWorkflow.value = workflow
  formLoading.value = true
  formLoadError.value = false
  startDialogVisible.value = true

  // 重置表单数据
  formData.title = `${workflow.title} - ${new Date().toLocaleString()}`
  formData.form_data = {}

  try {
    // 获取关联表单详情
    if (workflow.form_template_id) {
      const result = await formApi.getFormById(workflow.form_template_id)
      if (result.success) {
        currentForm.value = result.data
        formFields.value = result.data.fields || []

        // 初始化表单数据
        formFields.value.forEach(field => {
          if (field.default_value !== undefined && field.default_value !== null) {
            // 确保默认值类型正确
            if (field.field_type === 'number' && typeof field.default_value === 'string') {
              formData.form_data[field.field_key] = Number(field.default_value)
            } else if (field.field_type === 'checkbox' && !Array.isArray(field.default_value)) {
              // 如果默认值不是数组，但字段类型是复选框，则转换为数组
              formData.form_data[field.field_key] = [field.default_value]
            } else if (field.field_type === 'file' && !Array.isArray(field.default_value)) {
              // 如果默认值不是数组，但字段类型是文件上传，则转换为数组
              formData.form_data[field.field_key] = Array.isArray(field.default_value) ? field.default_value : []
            } else {
              formData.form_data[field.field_key] = field.default_value
            }
          } else if (field.field_type === 'checkbox') {
            formData.form_data[field.field_key] = []
          } else if (field.field_type === 'switch') {
            formData.form_data[field.field_key] = false
          } else if (field.field_type === 'number') {
            formData.form_data[field.field_key] = null
          } else if (field.field_type === 'file') {
            formData.form_data[field.field_key] = []
          } else {
            formData.form_data[field.field_key] = undefined
          }

          // 记录到控制台以便调试
          console.log(`初始化字段 ${field.field_key}:`, formData.form_data[field.field_key])
        })
      } else {
        formLoadError.value = true
        console.error('获取表单详情失败:', result.message)
        ElMessage.error(result.message || '获取表单详情失败')
      }
    } else {
      formLoadError.value = true
      ElMessage.error('该工作流未关联表单')
    }
  } catch (error) {
    formLoadError.value = true
    console.error('获取表单详情失败:', error)
    ElMessage.error(error.response?.data?.message || '获取表单详情失败')
  } finally {
    formLoading.value = false
  }
}

// 提交工作流
const submitWorkflow = async () => {
  if (!formRef.value) {
    console.error('表单引用不存在')
    ElMessage.error('表单引用不存在，请刷新页面重试')
    return
  }

  // 记录表单数据，用于调试
  console.log('提交的表单数据:', JSON.stringify(formData))

  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      submitting.value = true
      try {
        // 检查必要的数据是否存在
        if (!currentWorkflow.value || !currentWorkflow.value.id) {
          throw new Error('工作流信息不完整')
        }

        // 检查表单数据是否为空
        if (Object.keys(formData.form_data).length === 0) {
          throw new Error('表单数据为空')
        }

        // 先上传文件
        const filesUploaded = await uploadFiles()
        if (!filesUploaded) {
          submitting.value = false
          return
        }

        console.log('准备发起工作流:', {
          workflowId: currentWorkflow.value.id,
          title: formData.title,
          hasFormData: !!formData.form_data,
          formDataKeys: Object.keys(formData.form_data)
        })

        try {
          const result = await workflowApi.startWorkflowInstance(currentWorkflow.value.id, formData)

          if (result.success) {
            console.log('工作流发起成功:', result.data?.id)
            ElMessage.success('工作流发起成功')
            startDialogVisible.value = false

            // 刷新我发起的工作流列表
            await fetchInitiatedInstances()

            // 切换到我发起的工作流标签
            activeTab.value = 'initiated'
          } else {
            console.error('工作流发起失败:', result)
            ElMessage.error(result.message || '工作流发起失败')
          }
        } catch (apiError) {
          console.error('API调用失败:', apiError)

          // 检查是否是任务历史记录错误
          if (apiError.response?.status === 500 &&
              apiError.response?.data?.error?.includes('WorkflowTaskHistory')) {
            ElMessage.error('工作流发起过程中出现问题，正在尝试重新提交...')

            // 添加跳过任务历史记录的标志
            const modifiedData = {
              ...formData,
              skip_task_history: true
            }

            try {
              // 重新提交请求
              const retryResult = await workflowApi.startWorkflowInstance(
                currentWorkflow.value.id,
                modifiedData
              )

              if (retryResult.success) {
                console.log('工作流重试发起成功:', retryResult.data?.id)
                ElMessage.success('工作流发起成功')
                startDialogVisible.value = false

                // 刷新我发起的工作流列表
                await fetchInitiatedInstances()

                // 切换到我发起的工作流标签
                activeTab.value = 'initiated'
              } else {
                console.error('工作流重试发起失败:', retryResult)
                ElMessage.error(retryResult.message || '工作流发起失败')
              }
            } catch (retryError) {
              console.error('工作流重试发起失败:', retryError)
              ElMessage.error('尝试重新提交失败，请稍后再试')
            }
          } else {
            throw apiError // 重新抛出错误，让外层catch处理
          }
        }
      } catch (error) {
        console.error('发起工作流失败:', error)

        // 提供更详细的错误信息
        let errorMessage = '发起工作流失败'

        if (error.response) {
          if (error.response.status === 400) {
            errorMessage = '表单数据验证失败: ' + (error.response.data?.message || '请检查表单数据')
          } else if (error.response.status === 401) {
            errorMessage = '未授权操作，请重新登录'
          } else if (error.response.status === 404) {
            errorMessage = '工作流模板不存在'
          } else if (error.response.status === 500) {
            errorMessage = '服务器错误: ' + (error.response.data?.message || '请联系管理员')
          } else {
            errorMessage = error.response.data?.message || errorMessage
          }
        } else if (error.message) {
          errorMessage = error.message
        }

        ElMessage.error(errorMessage)
      } finally {
        submitting.value = false
      }
    } else {
      console.error('表单验证失败:', fields)
      ElMessage.error('表单验证失败，请检查输入')
    }
  })
}

// 查看工作流实例
const handleViewInstance = async (instance) => {
  // 首先确保currentInstance有一个初始状态
  currentInstance.value = null;

  try {
    // 记录调试信息
    console.log('查看工作流实例被触发', {
      instanceType: typeof instance,
      hasInstance: !!instance,
      instanceId: instance?.id,
      idType: typeof instance?.id
    });

    // 防止事件冒泡和默认行为
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    // 参数验证
    if (!instance) {
      ElMessage.error('无效的工作流实例');
      return;
    }

    if (!instance.id) {
      ElMessage.error('工作流实例缺少ID');
      return;
    }

    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在加载工作流详情...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      console.log(`开始获取工作流实例详情，ID: ${instance.id}`);

      // 调用API获取实例详情
      let result = null;
      try {
        result = await workflowApi.getWorkflowInstance(instance.id);
      } catch (apiError) {
        console.error('API调用失败:', apiError);
        throw new Error(`获取工作流实例详情失败: ${apiError.message || '未知错误'}`);
      }

      // 验证API返回结果
      if (!result) {
        throw new Error('API返回结果为空');
      }

      console.log('API返回结果:', result);

      if (!result.success) {
        throw new Error(result.message || 'API返回失败状态');
      }

      if (!result.data) {
        throw new Error('API返回的数据为空');
      }

      // 设置当前实例数据（确保它是一个新对象以避免引用问题）
      currentInstance.value = { ...result.data };

      // 进行数据清理和初始化
      if (!currentInstance.value.taskHistories) {
        currentInstance.value.taskHistories = [];
      }

      if (!currentInstance.value.form_data) {
        currentInstance.value.form_data = {};
      }

      // 优先尝试从直接嵌入的 workflowTemplate.formTemplate.fields 获取表单字段信息
      if (currentInstance.value.workflowTemplate?.formTemplate?.fields &&
          Array.isArray(currentInstance.value.workflowTemplate.formTemplate.fields) &&
          currentInstance.value.workflowTemplate.formTemplate.fields.length > 0) {
        formFields.value = currentInstance.value.workflowTemplate.formTemplate.fields;
        console.log('从嵌入的 workflowTemplate.formTemplate.fields 加载表单字段');
      }
      // 如果直接嵌入的字段不可用，并且存在 form_template_id，则回退到 API 调用
      else if (currentInstance.value.workflowTemplate?.form_template_id) {
        console.log('嵌入字段不可用，尝试通过 form_template_id API 调用获取表单字段');
        try {
          const formResult = await formApi.getFormById(
            currentInstance.value.workflowTemplate.form_template_id
          );

          if (formResult.success && formResult.data?.fields) {
            formFields.value = formResult.data.fields;
          } else {
            formFields.value = [];
            console.warn('通过 API 获取表单字段信息失败或表单无字段:', formResult?.message);
          }
        } catch (formError) {
          formFields.value = [];
          console.warn('通过 API 获取表单字段信息失败（非致命错误）:', formError);
        }
      } else {
         formFields.value = [];
         console.warn('当前工作流实例未关联表单模板，或缺少 form_template_id，且未直接嵌入表单字段');
      }

      // 在数据准备好后再打开对话框
      console.log('工作流实例数据已准备好，准备打开对话框');

      // 使用nextTick确保DOM更新后再打开对话框
      nextTick(() => {
        viewDialogVisible.value = true;
        console.log('查看对话框已打开');
      });
    } finally {
      // 确保无论成功还是失败都关闭loading
      loading.close();
    }
  } catch (error) {
    // 处理任何未捕获的错误
    console.error('查看工作流实例时出错:', error);
    ElMessage.error(error.message || '查看工作流实例失败');

    // 确保实例数据被重置
    currentInstance.value = null;
  }
}

// 获取字段验证规则
const getFieldRules = (field) => {
  const rules = []

  // 必填验证
  if (field.is_required) {
    const isSelectionType = ['select', 'date', 'time', 'datetime', 'radio', 'checkbox'].includes(field.field_type)
    rules.push({
      required: true,
      message: `请${isSelectionType ? '选择' : '输入'}${field.label}`,
      trigger: isSelectionType ? 'change' : 'blur'
    })
  }

  // 根据字段类型添加特定验证
  switch (field.field_type) {
    case 'number':
      rules.push({
        type: 'number',
        message: `${field.label}必须是数字`,
        trigger: 'blur'
      })

      // 如果有最小值/最大值限制
      if (field.validation_rules && field.validation_rules.min !== undefined) {
        rules.push({
          type: 'number',
          min: field.validation_rules.min,
          message: `${field.label}不能小于${field.validation_rules.min}`,
          trigger: 'blur'
        })
      }

      if (field.validation_rules && field.validation_rules.max !== undefined) {
        rules.push({
          type: 'number',
          max: field.validation_rules.max,
          message: `${field.label}不能大于${field.validation_rules.max}`,
          trigger: 'blur'
        })
      }
      break

    case 'checkbox':
      // 如果是必填的复选框，确保至少选择一项
      if (field.is_required) {
        rules.push({
          type: 'array',
          min: 1,
          message: `请至少选择一个${field.label}`,
          trigger: 'change'
        })
      }
      break

    case 'file':
      // 文件上传验证
      if (field.is_required) {
        rules.push({
          validator: (rule, value, callback) => {
            if (!value || (Array.isArray(value) && value.length === 0)) {
              callback(new Error(`请上传${field.label}`))
            } else {
              callback()
            }
          },
          trigger: 'change'
        })
      }

      // 文件类型验证
      if (field.accept) {
        rules.push({
          validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
              callback()
              return
            }

            const acceptTypes = field.accept.split(',').map(type => type.trim().toLowerCase())
            const invalidFiles = value.filter(file => {
              const fileExt = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
              const fileType = file.type ? file.type.toLowerCase() : ''

              return !acceptTypes.some(type => {
                return type === fileExt ||
                       type === fileType ||
                       (type.startsWith('.') && file.name.toLowerCase().endsWith(type)) ||
                       (type.includes('/*') && fileType.startsWith(type.split('/*')[0]))
              })
            })

            if (invalidFiles.length > 0) {
              callback(new Error(`只能上传 ${field.accept} 格式的文件`))
            } else {
              callback()
            }
          },
          trigger: 'change'
        })
      }

      // 文件大小验证
      if (field.max_size) {
        rules.push({
          validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
              callback()
              return
            }

            const maxSizeBytes = field.max_size * 1024 * 1024
            const oversizedFiles = value.filter(file => file.size > maxSizeBytes)

            if (oversizedFiles.length > 0) {
              callback(new Error(`文件大小不能超过 ${field.max_size}MB`))
            } else {
              callback()
            }
          },
          trigger: 'change'
        })
      }
      break
  }

  // 其他验证规则
  if (field.validation_rules && field.validation_rules.type) {
    const type = field.validation_rules.type
    const message = field.validation_rules.message || `${field.label}格式不正确`

    switch (type) {
      case 'email':
        rules.push({
          type: 'email',
          message,
          trigger: 'blur'
        })
        break
      case 'phone':
        rules.push({
          pattern: /^1[3-9]\d{9}$/,
          message,
          trigger: 'blur'
        })
        break
      case 'url':
        rules.push({
          type: 'url',
          message,
          trigger: 'blur'
        })
        break
      case 'number':
        rules.push({
          type: 'number',
          message,
          trigger: 'blur'
        })
        break
      case 'integer':
        rules.push({
          type: 'integer',
          message,
          trigger: 'blur'
        })
        break
      case 'regexp':
        if (field.validation_rules.pattern) {
          try {
            const pattern = new RegExp(field.validation_rules.pattern)
            rules.push({
              pattern,
              message,
              trigger: 'blur'
            })
          } catch (error) {
            console.error(`正则表达式 ${field.validation_rules.pattern} 无效:`, error)
          }
        }
        break
      case 'length':
        // 字符串长度验证
        if (field.validation_rules.min !== undefined) {
          rules.push({
            min: field.validation_rules.min,
            message: `${field.label}长度不能小于${field.validation_rules.min}个字符`,
            trigger: 'blur'
          })
        }
        if (field.validation_rules.max !== undefined) {
          rules.push({
            max: field.validation_rules.max,
            message: `${field.label}长度不能超过${field.validation_rules.max}个字符`,
            trigger: 'blur'
          })
        }
        break
    }
  }

  // 记录生成的验证规则，用于调试
  console.log(`字段 ${field.field_key} 的验证规则:`, rules)

  return rules
}

// 获取字段标签
const getFieldLabel = (key) => {
  // 安全检查
  if (!key) return '未知字段';

  try {
    // 首先在表单字段中查找
    if (formFields.value && Array.isArray(formFields.value) && formFields.value.length > 0) {
      const field = formFields.value.find(f => f && f.field_key === key);
      if (field && field.label) {
        return field.label;
      }
    }

    // 其次，尝试从当前表单实例中查找字段信息
    if (currentInstance.value &&
        currentInstance.value.workflowTemplate) {

      // 如果工作流模板包含form_template对象
      if (currentInstance.value.workflowTemplate.form_template &&
          currentInstance.value.workflowTemplate.form_template.fields &&
          Array.isArray(currentInstance.value.workflowTemplate.form_template.fields)) {
        const field = currentInstance.value.workflowTemplate.form_template.fields.find(
          f => f && f.field_key === key
        );
        if (field && field.label) {
          return field.label;
        }
      }

      // 尝试从模板schema中查找
      if (currentInstance.value.workflowTemplate.schema &&
          currentInstance.value.workflowTemplate.schema.form_template &&
          currentInstance.value.workflowTemplate.schema.form_template.fields &&
          Array.isArray(currentInstance.value.workflowTemplate.schema.form_template.fields)) {
        const field = currentInstance.value.workflowTemplate.schema.form_template.fields.find(
          f => f && f.field_key === key
        );
        if (field && field.label) {
          return field.label;
        }
      }
    }

    // 最后，尝试格式化key本身作为标签
    // 将下划线替换为空格，并首字母大写
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  } catch (error) {
    console.error('获取字段标签时出错:', error);
    return key; // 出错时返回原始key
  }
}

// 格式化字段值
const formatFieldValue = (key, value) => {
  // 处理空值
  if (value === null || value === undefined) return '-';

  // --- BEGIN USER FIELD HANDLING ---
  const field = formFields.value && Array.isArray(formFields.value) ? formFields.value.find(f => f && f.field_key === key) : null;
  const isUserFieldType = field && field.field_type === 'user_select'; // Assuming 'user_select' is the type for user fields
  const knownUserKey = ['assignee_id', 'approver_id', 'operator_id', 'delegator_id', 'creator_id', 'user_id'].includes(key);
  const genericUserKey = key.endsWith('_user_id');

  const isUserContext = isUserFieldType || knownUserKey || genericUserKey;

  if (isUserContext) {
    let userIds = [];
    if (typeof value === 'number' && !isNaN(value)) {
      userIds.push(value);
    } else if (typeof value === 'string' && !isNaN(Number(value))) {
      userIds.push(Number(value));
    } else if (Array.isArray(value) && value.length > 0) {
      value.forEach(item => {
        const numItem = Number(item); // Handles "1" and 1
        if (!isNaN(numItem)) {
          userIds.push(numItem);
        } else if (item && usersCache[item]) { // If item itself is a pre-loaded name (less likely for raw ID fields)
           // This case is less likely if 'value' is expected to be IDs
        }
      });
    }

    if (userIds.length > 0) {
      const names = userIds.map(userId => {
        if (usersCache[userId] && usersCache[userId] !== 'loading') {
          return usersCache[userId];
        }
        if (usersCache[userId] === 'loading') {
          return `${userId} (加载中...)`;
        }

        // Not in cache, fetch user info
        usersCache[userId] = 'loading'; // Mark as loading
        userApi.getUserById(userId)
          .then(result => {
            if (result.success && result.data && result.data.full_name) {
              usersCache[userId] = result.data.full_name;
            } else {
              usersCache[userId] = userId; // Store ID if name not found to prevent re-fetch
              console.warn(`获取用户 ${userId} 信息失败:`, result.message || '用户不存在或缺少full_name');
            }
          })
          .catch(error => {
            usersCache[userId] = userId; // Store ID on error to prevent re-fetch
            console.error(`获取用户 ${userId} API调用失败:`, error);
          });
        return `${userId} (加载中...)`; // Return ID while loading
      });
      return names.join(', ');
    } else if (Array.isArray(value) && value.every(item => typeof item === 'string' && isNaN(Number(item)))) {
      // If it's an array of non-numeric strings, it might be pre-formatted names or other data
      // Let it pass to subsequent formatting logic.
    } else if (userIds.length === 0 && !Array.isArray(value) && value !== null && value !== undefined) {
      // If it was a user context but no numeric IDs were extracted, and it's not an array of strings,
      // it could be a single non-numeric string. Let it pass.
    }
  }
  // --- END USER FIELD HANDLING ---

  try {
    // 如果是选项类型的字段，尝试将值转换为标签
    const currentField = field || (formFields.value && Array.isArray(formFields.value) ? formFields.value.find(f => f && f.field_key === key) : null);

    if (currentField && currentField.options && Array.isArray(currentField.options)) {
      // 多选
      if (Array.isArray(value)) {
        return value.map(v => {
          try {
            const option = currentField.options.find(opt => opt && opt.value == v);
            return option ? option.label : v;
          } catch (e) {
            console.warn(`格式化多选值时出错(${key}):`, e);
            return v;
          }
        }).filter(v => v !== null && v !== undefined).join(', ') || '-';
      }

      // 单选
      try {
        const option = currentField.options.find(opt => opt && opt.value == value);
        if (option) return option.label;
      } catch (e) {
        console.warn(`格式化单选值时出错(${key}):`, e);
      }
    }

    // 日期类型
    if (currentField && (currentField.field_type === 'date' || currentField.field_type === 'datetime')) {
      try {
        return formatDate(value);
      } catch (e) {
        console.warn(`格式化日期时出错(${key}):`, e);
      }
    }

    // 文件类型
    if (currentField && currentField.field_type === 'file') {
      if (Array.isArray(value)) {
        try {
          return value.map(file =>
            typeof file === 'object' ? (file.name || JSON.stringify(file)) : String(file)
          ).join(', ') || '-';
        } catch (e) {
          console.warn(`格式化文件数组时出错(${key}):`, e);
        }
      }
    }

    // 布尔类型
    if (currentField && currentField.field_type === 'switch') {
      return value === true ? '是' : '否';
    }

    // 如果是对象或数组，转为JSON字符串
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value);
      } catch (e) {
        console.warn(`JSON序列化对象时出错(${key}):`, e);
        return '[复杂对象]';
      }
    }

    // 最后的安全返回
    return String(value);
  } catch (error) {
    console.error(`格式化字段值时出错(${key}):`, error);
    return String(value || '-');
  }
}

// 获取节点名称 - 完全重写的安全实现
const getNodeName = (nodeId) => {
  // 基本安全检查
  if (!nodeId) {
    return '未知节点';
  }

  // 如果当前实例不存在，返回安全值
  if (!currentInstance || !currentInstance.value) {
    return `节点${nodeId}`;
  }

  // 尝试从不同来源获取节点名称，每一步都添加安全检查
  try {
    // 1. 从当前节点检查
    if (currentInstance.value.currentNode &&
        currentInstance.value.currentNode.id === nodeId &&
        currentInstance.value.currentNode.name) {
      return currentInstance.value.currentNode.name;
    }

    // 2. 从任务历史记录中查找节点
    if (currentInstance.value.taskHistories) {
      // 避免使用find方法，改用循环遍历
      const histories = currentInstance.value.taskHistories;
      if (Array.isArray(histories)) {
        for (let i = 0; i < histories.length; i++) {
          const history = histories[i];
          if (history &&
              history.node_id === nodeId &&
              history.node &&
              history.node.name) {
            return history.node.name;
          }
        }
      }
    }

    // 3. 从任务列表中查找节点
    if (currentInstance.value.tasks) {
      const tasks = currentInstance.value.tasks;
      if (Array.isArray(tasks)) {
        for (let i = 0; i < tasks.length; i++) {
          const task = tasks[i];
          if (task &&
              task.node_id === nodeId &&
              task.node &&
              task.node.name) {
            return task.node.name;
          }
        }
      }
    }

    // 4. 从模板的节点定义中查找
    if (currentInstance.value.workflowTemplate &&
        currentInstance.value.workflowTemplate.nodes) {
      const nodes = currentInstance.value.workflowTemplate.nodes;
      if (Array.isArray(nodes)) {
        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i];
          if (node && node.id === nodeId && node.name) {
            return node.name;
          }
        }
      }
    }

    // 5. 如果模板有schema且包含节点定义
    if (currentInstance.value.workflowTemplate &&
        currentInstance.value.workflowTemplate.schema &&
        currentInstance.value.workflowTemplate.schema.nodes) {
      const schemaNodes = currentInstance.value.workflowTemplate.schema.nodes;
      if (Array.isArray(schemaNodes)) {
        for (let i = 0; i < schemaNodes.length; i++) {
          const node = schemaNodes[i];
          if (node &&
              (node.id === nodeId || node.node_key === nodeId) &&
              node.name) {
            return node.name;
          }
        }
      }
    }

    // 如果所有尝试都失败，返回一个默认值
    return `节点${nodeId}`;
  } catch (error) {
    console.error('获取节点名称时出错:', error);
    return `节点${nodeId}`; // 出错时提供默认值
  }
}

// 获取操作文本
const getOperationText = (operation) => {
  // 安全性检查
  if (!operation || typeof operation !== 'string') {
    return '未知操作';
  }

  try {
    const operationMap = {
      'submit': '提交',
      'approve': '同意',
      'reject': '拒绝',
      'return': '退回',
      'withdraw': '撤回',
      'transfer': '转交',
      'create': '创建',
      'complete': '完成',
      'start': '发起'
    };

    return operationMap[operation] || operation;
  } catch (error) {
    console.error('获取操作文本时出错:', error);
    return String(operation); // 返回操作的字符串形式
  }
}

// 获取时间线项目类型
const getTimelineItemType = (operation) => {
  // 安全性检查
  if (!operation || typeof operation !== 'string') {
    return 'info';
  }

  try {
    const typeMap = {
      'submit': 'primary',
      'approve': 'success',
      'reject': 'danger',
      'return': 'warning',
      'withdraw': 'info',
      'transfer': 'info',
      'create': 'primary',
      'complete': 'success',
      'start': 'primary'
    };

    return typeMap[operation] || 'info';
  } catch (error) {
    console.error('获取时间线项目类型时出错:', error);
    return 'info'; // 出错时默认返回info类型
  }
}

// 获取状态类型
const getStatusType = (status) => {
  if (!status) return 'info';

  const typeMap = {
    'draft': 'info',
    'running': 'primary',
    'completed': 'success',
    'rejected': 'danger',
    'terminated': 'warning'
  };

  return typeMap[status] || 'info';
}

// 获取状态文本
const getStatusText = (status) => {
  if (!status) return '未知状态';

  const textMap = {
    'draft': '草稿',
    'running': '进行中',
    'completed': '已完成',
    'rejected': '已拒绝',
    'terminated': '已终止'
  };

  return textMap[status] || status;
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-';

  let dateObj;

  try {
    // 处理不同类型的日期输入
    if (date instanceof Date) {
      dateObj = date;
    } else if (typeof date === 'number') {
      // 时间戳
      dateObj = new Date(date);
    } else if (typeof date === 'string') {
      // ISO日期字符串或其他日期字符串
      dateObj = new Date(date);
    } else {
      return '-';
    }

    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      return '-';
    }

    // 格式化为本地日期时间字符串
    const options = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    };

    return dateObj.toLocaleString('zh-CN', options).replace(/\//g, '-');
  } catch (error) {
    console.error('日期格式化错误:', error, date);
    return date.toString();
  }
}

// 处理取消工作流
const handleCancelWorkflow = () => {
  // 检查表单是否有数据
  const hasFormData = formData.title || Object.values(formData.form_data).some(value => {
    if (Array.isArray(value)) {
      return value.length > 0
    }
    return value !== undefined && value !== null && value !== ''
  })

  if (hasFormData) {
    ElMessageBox.confirm(
      '确定要取消填写工作流吗？已填写的数据将会丢失。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '继续填写',
        type: 'warning'
      }
    ).then(() => {
      startDialogVisible.value = false
    }).catch(() => {
      // 用户选择继续填写，不做任何操作
    })
  } else {
    startDialogVisible.value = false
  }
}

// 获取文件上传URL
const getFileUploadUrl = () => {
  if (!currentWorkflow.value || !currentWorkflow.value.id) {
    return '/api/workflows/attachments/temp'
  }
  return `/api/workflows/${currentWorkflow.value.id}/attachments`
}

// 处理文件列表变更
const handleFileListChange = (fieldKey, fileList) => {
  // 将文件列表保存到表单数据中
  formData.form_data[fieldKey] = fileList.map(file => ({
    name: file.name,
    url: file.url,
    uid: file.uid,
    size: file.size,
    type: file.type || file.raw?.type
  }))

  console.log(`字段 ${fieldKey} 的文件列表已更新:`, formData.form_data[fieldKey])
}

// 上传文件
const uploadFiles = async () => {
  console.log('开始处理文件上传')

  // 查找所有文件上传组件
  const fileUploadRefs = document.querySelectorAll('.file-uploader')
  if (!fileUploadRefs || fileUploadRefs.length === 0) {
    console.log('没有找到文件上传组件，跳过文件上传')
    return true // 没有文件上传组件，直接返回成功
  }

  try {
    // 遍历所有文件字段
    for (const field of formFields.value) {
      if (field.field_type === 'file') {
        const fieldKey = field.field_key
        const files = formData.form_data[fieldKey]

        console.log(`处理文件字段 ${fieldKey}:`, {
          hasFiles: !!files,
          fileCount: files?.length || 0,
          isRequired: field.is_required
        })

        if (!files || files.length === 0) {
          // 如果是必填字段但没有文件，返回失败
          if (field.is_required) {
            console.error(`必填字段 ${fieldKey} 没有上传文件`)
            ElMessage.error(`请上传${field.label}`)
            return false
          }
          continue // 跳过非必填的空文件字段
        }

        // 检查是否有未上传的文件
        const unsavedFiles = files.filter(file => !file.url || file.url.startsWith('blob:'))
        const hasUnsavedFiles = unsavedFiles.length > 0

        console.log(`字段 ${fieldKey} 的未保存文件:`, {
          hasUnsavedFiles,
          unsavedCount: unsavedFiles.length
        })

        if (hasUnsavedFiles) {
          try {
            // 创建FormData对象
            const formDataObj = new FormData()

            // 添加文件
            let fileAdded = false
            for (const file of files) {
              if (file.raw) {
                console.log(`添加文件到FormData: ${file.name} (${formatFileSize(file.size || 0)})`)
                formDataObj.append('files', file.raw)
                fileAdded = true
              }
            }

            if (!fileAdded) {
              console.warn(`字段 ${fieldKey} 没有可上传的文件，跳过上传`)
              continue
            }

            // 上传文件
            console.log(`开始上传字段 ${fieldKey} 的文件`)
            const result = await workflowApi.uploadAttachment(currentWorkflow.value.id, formDataObj)

            if (!result.success) {
              console.error(`上传字段 ${fieldKey} 的文件失败:`, result)
              ElMessage.error(`上传${field.label}失败: ${result.message || '未知错误'}`)
              return false
            }

            console.log(`字段 ${fieldKey} 的文件上传成功:`, result.data)

            // 更新文件URL
            formData.form_data[fieldKey] = result.data.map(fileInfo => ({
              name: fileInfo.file_name,
              url: fileInfo.file_url,
              size: fileInfo.file_size,
              type: fileInfo.file_type
            }))

            console.log(`字段 ${fieldKey} 的文件信息已更新:`, formData.form_data[fieldKey])
          } catch (uploadError) {
            console.error(`上传字段 ${fieldKey} 的文件时发生错误:`, uploadError)
            ElMessage.error(`上传${field.label}失败: ${uploadError.message || '未知错误'}`)
            return false
          }
        } else {
          console.log(`字段 ${fieldKey} 的文件已经上传，无需重新上传`)
        }
      }
    }

    console.log('所有文件处理完成')
    return true // 所有文件上传成功
  } catch (error) {
    console.error('文件上传过程中发生错误:', error)
    ElMessage.error('文件上传失败: ' + (error.message || '未知错误'))
    return false
  }
}

// 处理对话框关闭
const handleDialogClosed = () => {
  // 重置表单数据
  formData.title = ''
  formData.form_data = {}

  // 重置表单状态
  formLoading.value = false
  formLoadError.value = false
  currentWorkflow.value = null
  currentForm.value = null
  formFields.value = []

  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields()
  }

  console.log('表单对话框已关闭，状态已重置')
}

// 安全的时间线类型函数
const safeTimelineType = (operation) => {
  if (!operation) return 'info';

  const typeMap = {
    'submit': 'primary',
    'approve': 'success',
    'reject': 'danger',
    'return': 'warning',
    'withdraw': 'info',
    'transfer': 'info',
    'create': 'primary',
    'complete': 'success',
    'start': 'primary'
  };

  return typeMap[operation] || 'info';
};

// 安全预处理过的任务历史记录
const safeTaskHistories = computed(() => {
  if (!currentInstance.value ||
      !currentInstance.value.taskHistories ||
      !Array.isArray(currentInstance.value.taskHistories)) {
    return [];
  }

  try {
    // 预处理所有历史记录，避免在模板中进行复杂处理
    return currentInstance.value.taskHistories
      .filter(history => history) // 过滤掉null/undefined
      .map(history => {
        let nodeName = '未知节点';
        let operatorName = null;
        let operationText = '未知操作';

        // 安全地获取节点名称
        if (history.node_id) {
          try {
            if (history.node && history.node.name) {
              nodeName = history.node.name;
            } else if (currentInstance.value.currentNode &&
                       currentInstance.value.currentNode.id === history.node_id) {
              nodeName = currentInstance.value.currentNode.name;
            } else {
              // 简单返回节点ID
              nodeName = `节点${history.node_id}`;
            }
          } catch (e) {
            console.warn('处理节点名称时出错:', e);
            nodeName = `节点${history.node_id}`;
          }
        }

        // 安全地获取操作人名称
        if (history.operator) {
          try {
            if (history.operator.full_name) {
              operatorName = history.operator.full_name;
            } else if (history.operator.username) {
              operatorName = history.operator.username;
            } else if (history.operator.id) {
              operatorName = `用户${history.operator.id}`;
            } else {
              operatorName = '未知用户';
            }
          } catch (e) {
            console.warn('处理操作人名称时出错:', e);
            operatorName = '未知用户';
          }
        }

        // 安全地获取操作文本
        if (history.operation) {
          try {
            const opMap = {
              'submit': '提交',
              'approve': '同意',
              'reject': '拒绝',
              'return': '退回',
              'withdraw': '撤回',
              'transfer': '转交',
              'create': '创建',
              'complete': '完成',
              'start': '发起'
            };
            operationText = opMap[history.operation] || history.operation;
          } catch (e) {
            console.warn('处理操作文本时出错:', e);
            operationText = history.operation || '未知操作';
          }
        }

        // 返回安全处理过的数据
        return {
          ...history,
          node_name: nodeName,
          operator_name: operatorName,
          operation_text: operationText
        };
      });
  } catch (e) {
    console.error('预处理任务历史记录时出错:', e);
    return []; // 出错时返回空数组
  }
});
</script>

<style scoped>
.workflow-form-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-center {
  text-align: center;
}

.empty-form-message {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 20px;
}

/* 表单字段提示样式 */
.field-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

/* 表单验证错误提示样式 */
:deep(.el-form-item.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c inset;
}

/* 表单加载状态样式 */
:deep(.el-skeleton) {
  padding: 20px;
}

/* 文件列表样式 */
:deep(.file-list) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.file-item) {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

:deep(.file-link) {
  color: #409EFF;
  text-decoration: none;
  margin-right: 8px;
}

:deep(.file-link:hover) {
  text-decoration: underline;
}

:deep(.file-size) {
  color: #909399;
  font-size: 12px;
}

/* 自定义时间线样式 */
.timeline-container {
  margin: 20px 0;
}

.safe-timeline {
  position: relative;
  padding-left: 28px;
}

.timeline-item {
  position: relative;
  padding-bottom: 20px;
  padding-left: 20px;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-marker {
  position: absolute;
  top: 5px;
  left: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #E4E7ED;
  z-index: 2;
}

.timeline-marker::before {
  content: "";
  position: absolute;
  top: 16px;
  left: 7px;
  width: 2px;
  height: calc(100% + 20px);
  background-color: #DCDFE6;
  z-index: 1;
}

.timeline-item:last-child .timeline-marker::before {
  display: none;
}

.timeline-content {
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.timeline-title {
  margin: 5px 0;
  font-size: 16px;
  font-weight: 500;
}

/* 时间线标记颜色 */
.marker-primary {
  background-color: #409EFF;
}

.marker-success {
  background-color: #67C23A;
}

.marker-warning {
  background-color: #E6A23C;
}

.marker-danger {
  background-color: #F56C6C;
}

.marker-info {
  background-color: #909399;
}

.workflow-detail-tabs {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  padding: 20px;
}

.workflow-detail-tabs :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

.workflow-detail-tabs :deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 20px;
}
</style>
