const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - username
 *         - password
 *         - email
 *         - full_name
 *       properties:
 *         id:
 *           type: integer
 *           description: 用户ID
 *           example: 1
 *         username:
 *           type: string
 *           description: 用户名，唯一标识
 *           example: "johndoe"
 *         password:
 *           type: string
 *           description: 用户密码（已加密）
 *           example: "$2a$10$..."
 *         email:
 *           type: string
 *           format: email
 *           description: 用户邮箱，唯一标识
 *           example: "<EMAIL>"
 *         full_name:
 *           type: string
 *           description: 用户全名
 *           example: "John Doe"
 *         phone:
 *           type: string
 *           description: 用户电话
 *           example: "13800138000"
 *         avatar:
 *           type: string
 *           description: 用户头像URL
 *           example: "/uploads/avatars/user1.jpg"
 *         status:
 *           type: string
 *           description: 用户状态
 *           enum: [active, inactive, locked]
 *           example: "active"
 *         last_login:
 *           type: string
 *           format: date-time
 *           description: 最后登录时间
 *           example: "2023-06-01T10:00:00Z"
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 *     UserResponse:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 用户ID
 *           example: 1
 *         username:
 *           type: string
 *           description: 用户名
 *           example: "johndoe"
 *         email:
 *           type: string
 *           format: email
 *           description: 用户邮箱
 *           example: "<EMAIL>"
 *         full_name:
 *           type: string
 *           description: 用户全名
 *           example: "John Doe"
 *         phone:
 *           type: string
 *           description: 用户电话
 *           example: "13800138000"
 *         avatar:
 *           type: string
 *           description: 用户头像URL
 *           example: "/uploads/avatars/user1.jpg"
 *         status:
 *           type: string
 *           description: 用户状态
 *           example: "active"
 *         roles:
 *           type: array
 *           description: 用户角色
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *                 example: 1
 *               name:
 *                 type: string
 *                 example: "admin"
 *               display_name:
 *                 type: string
 *                 example: "管理员"
 *         departments:
 *           type: array
 *           description: 用户所属部门
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *                 example: 1
 *               name:
 *                 type: string
 *                 example: "研发部"
 *               code:
 *                 type: string
 *                 example: "RD001"
 *     UserLogin:
 *       type: object
 *       required:
 *         - username
 *         - password
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名或邮箱
 *           example: "johndoe"
 *         password:
 *           type: string
 *           description: 用户密码
 *           example: "password123"
 *     UserRegister:
 *       type: object
 *       required:
 *         - username
 *         - password
 *         - email
 *         - full_name
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名
 *           example: "johndoe"
 *         password:
 *           type: string
 *           description: 用户密码
 *           example: "password123"
 *         email:
 *           type: string
 *           format: email
 *           description: 用户邮箱
 *           example: "<EMAIL>"
 *         full_name:
 *           type: string
 *           description: 用户全名
 *           example: "John Doe"
 *         phone:
 *           type: string
 *           description: 用户电话
 *           example: "13800138000"
 *     LoginResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "登录成功"
 *         data:
 *           type: object
 *           properties:
 *             token:
 *               type: string
 *               description: JWT令牌
 *               example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *             user:
 *               $ref: '#/components/schemas/UserResponse'
 */

module.exports = (sequelize, DataTypes) => {
  class User extends Model {
    static associate(models) {
      // 用户所属部门
      User.belongsToMany(models.Department, {
        through: 'department_users',
        foreignKey: 'user_id',
        otherKey: 'department_id',
        as: 'departments'
      });

      // 用户管理的部门
      User.hasMany(models.Department, {
        foreignKey: 'manager_id',
        as: 'managedDepartments'
      });

      // 用户角色
      User.belongsToMany(models.Role, {
        through: 'user_roles',
        foreignKey: 'user_id',
        otherKey: 'role_id',
        as: 'roles'
      });
    }

    // 验证密码
    async validatePassword(password) {
      return bcrypt.compare(password, this.password);
    }
  }

  User.init({
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    full_name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    avatar: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'active'
    },
    last_login: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'User',
    tableName: 'users',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    hooks: {
      beforeCreate: async (user) => {
        if (user.password) {
          user.password = await bcrypt.hash(user.password, 10);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, 10);
        }
      }
    }
  });

  return User;
};
