/**
 * Test runner script for workflow system E2E tests
 * Run with: node run-tests.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const TEST_GROUPS = {
  basic: ['login.spec.js', 'home.spec.js'],
  core: ['department.spec.js', 'form-design.spec.js', 'workflow-design.spec.js', 'workflow-form.spec.js', 'workflow-process.spec.js'],
  advanced: ['permissions.spec.js', 'form-validation.spec.js', 'workflow-lifecycle.spec.js'],
  quality: ['error-handling.spec.js', 'performance.spec.js', 'responsive.spec.js']
};

// Parse command line arguments
const args = process.argv.slice(2);
const runGroup = args[0] || 'all'; // Default to 'all'
const runHeaded = args.includes('--headed');
const runDebug = args.includes('--debug');
const runUI = args.includes('--ui');

// Determine which tests to run
let testsToRun = [];

if (runGroup === 'all') {
  // Run all tests
  Object.values(TEST_GROUPS).forEach(group => {
    testsToRun = [...testsToRun, ...group];
  });
} else if (TEST_GROUPS[runGroup]) {
  // Run specific group
  testsToRun = TEST_GROUPS[runGroup];
} else {
  // Assume it's a specific test file
  testsToRun = [runGroup];
}

// Ensure tests exist
const testsDir = path.join(__dirname, 'tests');
const validTests = testsToRun.filter(test => {
  const testPath = path.join(testsDir, test);
  return fs.existsSync(testPath);
});

if (validTests.length === 0) {
  console.error('No valid tests found to run!');
  process.exit(1);
}

// Build command
let command = 'npx playwright test';

// Add test files
if (runGroup !== 'all' && validTests.length > 0) {
  command += ' ' + validTests.map(test => `tests/${test}`).join(' ');
}

// Add options
if (runHeaded) {
  command += ' --headed';
}

if (runDebug) {
  command += ' --debug';
}

if (runUI) {
  command += ' --ui';
}

// Run tests
console.log(`Running tests: ${validTests.join(', ')}`);
console.log(`Command: ${command}`);

try {
  execSync(command, { stdio: 'inherit' });
  console.log('Tests completed successfully!');
} catch (error) {
  console.error('Tests failed with error:', error.message);
  process.exit(1);
}
