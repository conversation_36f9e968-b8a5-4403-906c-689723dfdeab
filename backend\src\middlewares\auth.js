const jwt = require('jsonwebtoken');
const { User, Role } = require('../models');

// 验证JWT令牌
exports.authenticate = async (req, res, next) => {
  try {
    console.log('认证中间件被调用，路径:', req.path);
    console.log('请求头:', req.headers);

    // 排除登录和注册路由
    const publicPaths = ['/api/users/login', '/api/users/register'];
    if (publicPaths.includes(req.path)) {
      console.log('公开路由，跳过认证:', req.path);
      return next();
    }

    // 获取令牌
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // 对于开发环境，如果没有令牌，可以使用默认用户（仅用于测试路由）
      if (process.env.NODE_ENV === 'development') {
        console.log('开发环境：使用默认管理员用户');
        req.user = {
          id: 1,
          username: 'admin',
          roles: ['admin']
        };
        return next();
      }

      return res.status(401).json({
        success: false,
        message: '未提供授权令牌'
      });
    }

    const token = authHeader.split(' ')[1];
    console.log('获取到令牌:', token);

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('解码的令牌数据:', decoded);
    req.user = decoded.user;
    console.log('设置的用户数据:', req.user);

    next();
  } catch (error) {
    console.error('身份验证错误:', error);
    res.status(401).json({
      success: false,
      message: '无效的令牌',
      error: error.message
    });
  }
};

// 检查角色权限
exports.authorize = (roles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未授权'
      });
    }

    // 对于开发环境的测试路由，可以跳过权限检查
    if (process.env.NODE_ENV === 'development' && req.path === '/test') {
      console.log('开发环境测试路由：跳过权限检查');
      return next();
    }

    const userRoles = req.user.roles || [];

    // 如果没有指定角色，或者用户拥有指定的角色之一
    if (roles.length === 0 || roles.some(role => userRoles.includes(role))) {
      return next();
    }

    res.status(403).json({
      success: false,
      message: '没有权限执行此操作'
    });
  };
};
