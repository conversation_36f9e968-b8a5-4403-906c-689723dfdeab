/**
 * Fix user_roles table script
 * Run with: node fix-user-roles-table.js
 */

const { sequelize } = require('./src/models');

async function fixUserRolesTable() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    console.log('Adding updated_at column to user_roles table...');
    
    // Check if the column exists
    const [results] = await sequelize.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'user_roles' AND column_name = 'updated_at';
    `);
    
    if (results.length === 0) {
      // Add updated_at column if it doesn't exist
      await sequelize.query(`
        ALTER TABLE user_roles 
        ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
      `);
      console.log('Added updated_at column to user_roles table.');
    } else {
      console.log('updated_at column already exists in user_roles table.');
    }
    
    console.log('User_roles table updated successfully.');
  } catch (error) {
    console.error('Error fixing user_roles table:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

fixUserRolesTable();
