<template>
  <div class="workflow-process-container">
    <el-row :gutter="20" class="mb-4">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>工作流处理</span>
            </div>
          </template>

          <el-tabs v-model="activeTab">
            <el-tab-pane label="待办任务" name="todo">
              <el-table
                :data="todoTasks"
                border
                v-loading="todoLoading">
                <el-table-column prop="id" label="任务ID" width="80" />
                <el-table-column label="工作流标题" width="180">
                  <template #default="scope">
                    {{ scope.row.workflowInstance ? scope.row.workflowInstance.title : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="工作流类型" width="150">
                  <template #default="scope">
                    {{ scope.row.workflowInstance && scope.row.workflowInstance.workflowTemplate ?
                      scope.row.workflowInstance.workflowTemplate.title : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="当前节点" width="120">
                  <template #default="scope">
                    {{ scope.row.node ? scope.row.node.name : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="发起人" width="120">
                  <template #default="scope">
                    {{ scope.row.workflowInstance && scope.row.workflowInstance.initiator ?
                      scope.row.workflowInstance.initiator.full_name : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="priority" label="优先级" width="100">
                  <template #default="scope">
                    <el-tag :type="getPriorityType(scope.row.priority)">
                      {{ getPriorityText(scope.row.priority) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="到期时间" width="180">
                  <template #default="scope">
                    <span :class="{ 'text-danger': isOverdue(scope.row.due_date) }">
                      {{ formatDate(scope.row.due_date) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="创建时间" width="180">
                  <template #default="scope">
                    {{ formatDate(scope.row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                  <template #default="scope">
                    <el-button
                      size="small"
                      @click="handleProcessTask(scope.row)"
                      type="primary">
                      处理
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>

            <el-tab-pane label="已办任务" name="done">
              <el-table
                :data="doneTasks"
                border
                v-loading="doneLoading">
                <el-table-column label="工作流标题" width="180">
                  <template #default="scope">
                    {{ scope.row.workflowInstance ? scope.row.workflowInstance.title : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="工作流类型" width="150">
                  <template #default="scope">
                    {{ scope.row.workflowInstance && scope.row.workflowInstance.workflowTemplate ?
                      scope.row.workflowInstance.workflowTemplate.title : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="节点名称" width="120">
                  <template #default="scope">
                    {{ scope.row.node ? scope.row.node.name : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="发起人" width="120">
                  <template #default="scope">
                    {{ scope.row.workflowInstance && scope.row.workflowInstance.initiator ?
                      scope.row.workflowInstance.initiator.full_name : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="operation" label="操作类型" width="100">
                  <template #default="scope">
                    <el-tag :type="getOperationTagType(scope.row.operation)">
                      {{ getOperationText(scope.row.operation) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="处理时间" width="180">
                  <template #default="scope">
                    {{ formatDate(scope.row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                  <template #default="scope">
                    <el-button
                      size="small"
                      @click="handleViewInstance(scope.row.workflowInstance)"
                      type="primary">
                      查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <!-- 处理任务对话框 -->
    <el-dialog
      v-model="processDialogVisible"
      title="处理任务"
      width="80%">
      <div v-if="currentTask && currentInstance">
        <h2>{{ currentInstance.title }}</h2>

        <el-divider>基本信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工作流类型">{{ currentInstance.workflowTemplate ? currentInstance.workflowTemplate.title : '-' }}</el-descriptions-item>
          <el-descriptions-item label="当前节点">{{ currentTask.node ? currentTask.node.name : '-' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentInstance.status)">
              {{ getStatusText(currentInstance.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发起人">{{ currentInstance.initiator ? currentInstance.initiator.full_name : '-' }}</el-descriptions-item>
          <el-descriptions-item label="发起时间">{{ formatDate(currentInstance.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(currentTask.priority)">
              {{ getPriorityText(currentTask.priority) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <el-tabs class="workflow-detail-tabs" style="margin-top: 20px;">
          <el-tab-pane label="表单数据">
            <FormDataDisplay
              :form-data="{
                ...currentInstance.form_data,
                workflowTemplate: currentInstance.workflowTemplate,
                fields: formFields
              }"
              :schema="currentInstance.form_schema || currentInstance.workflow_schema"
            />
          </el-tab-pane>
          <el-tab-pane label="流程图">
            <WorkflowVisualizer :workflow-instance="currentInstance" />
          </el-tab-pane>
          <el-tab-pane label="统计信息">
            <WorkflowMetrics :workflow-instance="currentInstance" />
          </el-tab-pane>
        </el-tabs>

        <el-divider>流转记录</el-divider>
        <el-timeline>
          <el-timeline-item
            v-for="(history, index) in currentInstance.taskHistories"
            :key="index"
            :timestamp="formatDate(history.created_at)"
            :type="getTimelineItemType(history.operation)">
            <h4>{{ getNodeName(history.node_id) }} - {{ getOperationText(history.operation) }}</h4>
            <p v-if="history.operator">操作人: {{ history.operator.full_name }}</p>
            <p v-if="history.comments">备注: {{ history.comments }}</p>
          </el-timeline-item>
        </el-timeline>

        <el-divider>处理意见</el-divider>
        <el-form :model="processForm" ref="processFormRef" label-width="100px">
          <el-form-item label="处理类型" prop="operation">
            <el-radio-group v-model="processForm.operation">
              <el-radio label="approve">同意</el-radio>
              <el-radio label="reject">拒绝</el-radio>
              <el-radio label="return">退回</el-radio>
              <el-radio label="transfer">转交</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="转交人" v-if="processForm.operation === 'transfer'">
            <el-select
              v-model="processForm.transfer_to_user_id"
              placeholder="请选择转交人"
              style="width: 100%">
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.full_name"
                :value="user.id"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="处理意见">
            <el-input
              v-model="processForm.comments"
              type="textarea"
              :rows="3"
              placeholder="请输入处理意见"></el-input>
          </el-form-item>

          <!-- 表单修改区域，仅在某些节点类型允许修改表单 -->
          <template v-if="canEditForm && formFields.length > 0">
            <el-divider>表单修改</el-divider>

            <template v-for="field in formFields" :key="field.field_key">
              <el-form-item
                :label="field.label"
                :prop="'form_data.' + field.field_key">

                <!-- 文本输入框 -->
                <el-input
                  v-if="field.field_type === 'input'"
                  v-model="processForm.form_data[field.field_key]"
                  :placeholder="field.placeholder || '请输入' + field.label"></el-input>

                <!-- 文本域 -->
                <el-input
                  v-else-if="field.field_type === 'textarea'"
                  v-model="processForm.form_data[field.field_key]"
                  type="textarea"
                  :rows="3"
                  :placeholder="field.placeholder || '请输入' + field.label"></el-input>

                <!-- 数字输入框 -->
                <el-input-number
                  v-else-if="field.field_type === 'number'"
                  v-model="processForm.form_data[field.field_key]"
                  :placeholder="field.placeholder || '请输入' + field.label"></el-input-number>

                <!-- 单选框组 -->
                <el-radio-group
                  v-else-if="field.field_type === 'radio'"
                  v-model="processForm.form_data[field.field_key]">
                  <el-radio
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.value">
                    {{ option.label }}
                  </el-radio>
                </el-radio-group>

                <!-- 复选框组 -->
                <el-checkbox-group
                  v-else-if="field.field_type === 'checkbox'"
                  v-model="processForm.form_data[field.field_key]">
                  <el-checkbox
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.value">
                    {{ option.label }}
                  </el-checkbox>
                </el-checkbox-group>

                <!-- 下拉选择框 -->
                <el-select
                  v-else-if="field.field_type === 'select'"
                  v-model="processForm.form_data[field.field_key]"
                  :placeholder="field.placeholder || '请选择' + field.label"
                  style="width: 100%">
                  <el-option
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"></el-option>
                </el-select>

                <!-- 日期选择器 -->
                <el-date-picker
                  v-else-if="field.field_type === 'date'"
                  v-model="processForm.form_data[field.field_key]"
                  type="date"
                  :placeholder="field.placeholder || '请选择日期'"
                  style="width: 100%"></el-date-picker>

                <!-- 时间选择器 -->
                <el-time-picker
                  v-else-if="field.field_type === 'time'"
                  v-model="processForm.form_data[field.field_key]"
                  :placeholder="field.placeholder || '请选择时间'"
                  style="width: 100%"></el-time-picker>

                <!-- 日期时间选择器 -->
                <el-date-picker
                  v-else-if="field.field_type === 'datetime'"
                  v-model="processForm.form_data[field.field_key]"
                  type="datetime"
                  :placeholder="field.placeholder || '请选择日期时间'"
                  style="width: 100%"></el-date-picker>

                <!-- 开关 -->
                <el-switch
                  v-else-if="field.field_type === 'switch'"
                  v-model="processForm.form_data[field.field_key]"></el-switch>

                <!-- 滑块 -->
                <el-slider
                  v-else-if="field.field_type === 'slider'"
                  v-model="processForm.form_data[field.field_key]"></el-slider>

                <!-- 评分 -->
                <el-rate
                  v-else-if="field.field_type === 'rate'"
                  v-model="processForm.form_data[field.field_key]"></el-rate>
              </el-form-item>
            </template>
          </template>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="processDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitProcess" :loading="submitting">
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看工作流实例对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="工作流详情"
      width="80%">
      <div v-if="currentInstance">
        <h2>{{ currentInstance.title }}</h2>

        <el-divider>基本信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工作流类型">{{ currentInstance.workflowTemplate ? currentInstance.workflowTemplate.title : '-' }}</el-descriptions-item>
          <el-descriptions-item label="当前节点">{{ currentInstance.currentNode ? currentInstance.currentNode.name : '-' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentInstance.status)">
              {{ getStatusText(currentInstance.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发起人">{{ currentInstance.initiator ? currentInstance.initiator.full_name : '-' }}</el-descriptions-item>
          <el-descriptions-item label="发起时间">{{ formatDate(currentInstance.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ currentInstance.completed_at ? formatDate(currentInstance.completed_at) : '-' }}</el-descriptions-item>
        </el-descriptions>

        <el-tabs class="workflow-detail-tabs" style="margin-top: 20px;">
          <el-tab-pane label="表单数据">
            <FormDataDisplay
              :form-data="{
                ...currentInstance.form_data,
                workflowTemplate: currentInstance.workflowTemplate,
                fields: formFields
              }"
              :schema="currentInstance.form_schema || currentInstance.workflow_schema"
            />
          </el-tab-pane>
          <el-tab-pane label="流程图">
            <WorkflowVisualizer :workflow-instance="currentInstance" />
          </el-tab-pane>
          <el-tab-pane label="统计信息">
            <WorkflowMetrics :workflow-instance="currentInstance" />
          </el-tab-pane>
        </el-tabs>

        <el-divider>流转记录</el-divider>
        <el-timeline>
          <el-timeline-item
            v-for="(history, index) in currentInstance.taskHistories"
            :key="index"
            :timestamp="formatDate(history.created_at)"
            :type="getTimelineItemType(history.operation)">
            <h4>{{ getNodeName(history.node_id) }} - {{ getOperationText(history.operation) }}</h4>
            <p v-if="history.operator">操作人: {{ history.operator.full_name }}</p>
            <p v-if="history.comments">备注: {{ history.comments }}</p>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { workflowApi, formApi, userApi } from '@/api'
import WorkflowVisualizer from '@/components/WorkflowVisualizer.vue'
import WorkflowMetrics from '@/components/WorkflowMetrics.vue'
import FormDataDisplay from '@/components/FormDataDisplay.vue'

// 数据
const activeTab = ref('todo')
const todoTasks = ref([])
const doneTasks = ref([])
const users = ref([])
const todoLoading = ref(false)
const doneLoading = ref(false)
const processDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const submitting = ref(false)
const currentTask = ref(null)
const currentInstance = ref(null)
const formFields = ref([])

// 处理表单
const processFormRef = ref(null)
const processForm = reactive({
  operation: 'approve',
  comments: '',
  transfer_to_user_id: null,
  form_data: {}
})

// 是否可以编辑表单
const canEditForm = computed(() => {
  if (!currentTask.value || !currentTask.value.node) return false

  // 根据节点类型或配置判断是否可以编辑表单
  // 这里假设任务节点可以编辑表单
  return currentTask.value.node.node_type === 'task'
})

// 初始化
onMounted(async () => {
  await fetchTodoTasks()
  await fetchDoneTasks()
  await fetchUsers()
})

// 获取待办任务列表
const fetchTodoTasks = async () => {
  todoLoading.value = true
  try {
    const result = await workflowApi.getUserTodoTasks()
    if (result.success) {
      // 确保我们获取的是任务数组，而不是包含分页信息的对象
      todoTasks.value = result.data.tasks || []
    }
  } catch (error) {
    console.error('获取待办任务列表失败:', error)
    ElMessage.error('获取待办任务列表失败')
  } finally {
    todoLoading.value = false
  }
}

// 获取已办任务列表
const fetchDoneTasks = async () => {
  doneLoading.value = true
  try {
    const result = await workflowApi.getUserDoneTasks()
    if (result.success) {
      // 确保我们获取的是任务历史数组，而不是包含分页信息的对象
      doneTasks.value = result.data.taskHistories || []
    }
  } catch (error) {
    console.error('获取已办任务列表失败:', error)
    ElMessage.error('获取已办任务列表失败')
  } finally {
    doneLoading.value = false
  }
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    const result = await userApi.getAllUsers()
    if (result.success) {
      users.value = result.data
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 处理任务
const handleProcessTask = async (task) => {
  currentTask.value = task

  try {
    // 获取工作流实例详情
    const result = await workflowApi.getWorkflowInstance(task.workflow_instance_id)
    if (result.success) {
      currentInstance.value = result.data

      // 重置处理表单
      processForm.operation = 'approve'
      processForm.comments = ''
      processForm.transfer_to_user_id = null
      processForm.form_data = { ...currentInstance.value.form_data }

      // 获取表单字段
      if (currentInstance.value.workflowTemplate && currentInstance.value.workflowTemplate.form_template_id) {
        const formResult = await formApi.getFormById(currentInstance.value.workflowTemplate.form_template_id)
        if (formResult.success) {
          formFields.value = formResult.data.fields || []
        }
      }

      processDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取工作流实例详情失败:', error)
    ElMessage.error('获取工作流实例详情失败')
  }
}

// 提交处理结果
const submitProcess = async () => {
  // 验证转交操作必须选择转交人
  if (processForm.operation === 'transfer' && !processForm.transfer_to_user_id) {
    ElMessage.error('请选择转交人')
    return
  }

  submitting.value = true
  try {
    const data = {
      operation: processForm.operation,
      comments: processForm.comments,
      form_data: processForm.form_data
    }

    // 如果是转交操作，添加转交人ID
    if (processForm.operation === 'transfer') {
      data.transfer_to_user_id = processForm.transfer_to_user_id
    }

    const result = await workflowApi.handleWorkflowTask(
      currentInstance.value.id,
      currentTask.value.id,
      data
    )

    if (result.success) {
      ElMessage.success('处理成功')
      processDialogVisible.value = false

      // 刷新任务列表
      await fetchTodoTasks()
      await fetchDoneTasks()
    } else {
      ElMessage.error(result.message || '处理失败')
    }
  } catch (error) {
    console.error('处理任务失败:', error)
    ElMessage.error(error.response?.data?.message || '处理失败')
  } finally {
    submitting.value = false
  }
}

// 查看工作流实例
const handleViewInstance = async (instance) => {
  try {
    const result = await workflowApi.getWorkflowInstance(instance.id)
    if (result.success) {
      currentInstance.value = result.data

      // 获取表单字段
      if (currentInstance.value.workflowTemplate && currentInstance.value.workflowTemplate.form_template_id) {
        const formResult = await formApi.getFormById(currentInstance.value.workflowTemplate.form_template_id)
        if (formResult.success) {
          formFields.value = formResult.data.fields || []
        }
      }

      viewDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取工作流实例详情失败:', error)
    ElMessage.error('获取工作流实例详情失败')
  }
}

// 获取字段标签
const getFieldLabel = (key) => {
  if (!currentInstance.value || !formFields.value.length) return key

  const field = formFields.value.find(f => f.field_key === key)
  return field ? field.label : key
}

// 格式化字段值
const formatFieldValue = (key, value) => {
  if (!currentInstance.value || !formFields.value.length) return value

  const field = formFields.value.find(f => f.field_key === key)
  if (!field) return value

  // 处理不同类型的字段值
  switch (field.field_type) {
    case 'radio':
    case 'select':
      if (field.options) {
        const option = field.options.find(opt => opt.value === value)
        return option ? option.label : value
      }
      return value
    case 'checkbox':
      if (Array.isArray(value) && field.options) {
        return value.map(v => {
          const option = field.options.find(opt => opt.value === v)
          return option ? option.label : v
        }).join(', ')
      }
      return value
    case 'date':
    case 'datetime':
      return value ? formatDate(value) : '-'
    case 'switch':
      return value ? '是' : '否'
    default:
      return value
  }
}

// 获取节点名称
const getNodeName = (nodeId) => {
  if (!currentInstance.value || !currentInstance.value.workflowTemplate || !currentInstance.value.workflowTemplate.nodes) return '-'

  // 假设工作流实例中包含节点信息
  const node = currentInstance.value.workflowTemplate.nodes.find(n => n.id === nodeId)
  return node ? node.name : '-'
}

// 获取操作文本
const getOperationText = (operation) => {
  switch (operation) {
    case 'start':
      return '发起'
    case 'approve':
      return '同意'
    case 'reject':
      return '拒绝'
    case 'return':
      return '退回'
    case 'transfer':
      return '转交'
    default:
      return operation
  }
}

// 获取操作标签类型
const getOperationTagType = (operation) => {
  switch (operation) {
    case 'start':
      return 'primary'
    case 'approve':
      return 'success'
    case 'reject':
      return 'danger'
    case 'return':
      return 'warning'
    case 'transfer':
      return 'info'
    default:
      return ''
  }
}

// 获取时间线项类型
const getTimelineItemType = (operation) => {
  return getOperationTagType(operation)
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'running':
      return 'primary'
    case 'completed':
      return 'success'
    case 'rejected':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'running':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'rejected':
      return '已拒绝'
    default:
      return status
  }
}

// 获取优先级类型
const getPriorityType = (priority) => {
  switch (priority) {
    case 'high':
      return 'danger'
    case 'normal':
      return 'primary'
    case 'low':
      return 'info'
    default:
      return 'info'
  }
}

// 获取优先级文本
const getPriorityText = (priority) => {
  switch (priority) {
    case 'high':
      return '高'
    case 'normal':
      return '中'
    case 'low':
      return '低'
    default:
      return priority
  }
}

// 检查是否过期
const isOverdue = (dueDate) => {
  if (!dueDate) return false

  const now = new Date()
  const due = new Date(dueDate)
  return due < now
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString()
}
</script>

<style scoped>
.workflow-process-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.workflow-detail-tabs {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  padding: 20px;
}

.workflow-detail-tabs :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

.workflow-detail-tabs :deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 20px;
}
</style>
