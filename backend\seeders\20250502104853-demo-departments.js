'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // 先获取用户ID
    const users = await queryInterface.sequelize.query(
      `SELECT id, username FROM users;`,
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const adminId = users.find(user => user.username === 'admin')?.id;
    const managerId = users.find(user => user.username === 'manager')?.id;

    // 插入部门数据
    await queryInterface.bulkInsert('departments', [
      {
        name: '总公司',
        code: 'HQ',
        parent_id: null,
        manager_id: adminId,
        description: '公司总部',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '研发部',
        code: 'RD',
        parent_id: 1, // 总公司的ID
        manager_id: managerId,
        description: '负责产品研发',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '市场部',
        code: 'MKT',
        parent_id: 1, // 总公司的ID
        manager_id: managerId,
        description: '负责市场营销',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: '人事部',
        code: 'HR',
        parent_id: 1, // 总公司的ID
        manager_id: managerId,
        description: '负责人力资源管理',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});

    // 插入部门用户关联数据
    const employeeId = users.find(user => user.username === 'employee')?.id;

    if (employeeId) {
      await queryInterface.bulkInsert('department_users', [
        {
          department_id: 2, // 研发部
          user_id: employeeId,
          is_primary: true,
          position: '开发工程师',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          department_id: 1, // 总公司
          user_id: adminId,
          is_primary: true,
          position: '总经理',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          department_id: 2, // 研发部
          user_id: managerId,
          is_primary: true,
          position: '研发经理',
          created_at: new Date(),
          updated_at: new Date()
        }
      ], {});
    }
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.bulkDelete('department_users', null, {});
    await queryInterface.bulkDelete('departments', null, {});
  }
};
