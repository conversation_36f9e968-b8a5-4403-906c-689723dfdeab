name: E2E Tests

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    - cron: '0 0 * * *'  # Run daily at midnight UTC
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of tests to run'
        required: true
        default: 'smoke'
        type: choice
        options:
          - smoke
          - regression
          - api
          - a11y
          - security
          - visual
          - all
      parallel:
        description: 'Number of parallel workers'
        required: false
        default: '2'
        type: string
      retries:
        description: 'Number of retries for failed tests'
        required: false
        default: '1'
        type: string

env:
  NODE_VERSION: '20'
  PLAYWRIGHT_VERSION: '1.40.0'
  DB_HOST: localhost
  DB_PORT: 5432
  DB_USER: postgres
  DB_PASSWORD: postgres
  DB_NAME: workflow_test

jobs:
  setup:
    name: Setup Environment
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Set test matrix
        id: set-matrix
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            TEST_TYPE="${{ github.event.inputs.test_type }}"
          elif [ "${{ github.event_name }}" == "schedule" ]; then
            TEST_TYPE="regression"
          else
            TEST_TYPE="smoke"
          fi

          if [ "$TEST_TYPE" == "all" ]; then
            echo "matrix={\"shard\":[\"1/3\",\"2/3\",\"3/3\"]}" >> $GITHUB_OUTPUT
          else
            echo "matrix={\"shard\":[\"1/1\"]}" >> $GITHUB_OUTPUT
          fi

          echo "Test type: $TEST_TYPE"
          echo "Matrix: $(cat $GITHUB_OUTPUT)"

  test:
    name: Run E2E Tests (Shard ${{ matrix.shard }})
    needs: setup
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.setup.outputs.matrix) }}

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: ${{ env.DB_USER }}
          POSTGRES_PASSWORD: ${{ env.DB_PASSWORD }}
          POSTGRES_DB: ${{ env.DB_NAME }}
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd frontend && npm ci
          cd ../backend && npm ci
          cd ../e2e && npm ci

      - name: Install Playwright
        run: cd e2e && npx playwright install --with-deps chromium

      - name: Setup database
        run: |
          cd backend
          # Add database setup commands here
          # For example:
          npm run db:setup

      - name: Start backend server
        run: |
          cd backend
          npm run start:test &
          echo "Waiting for backend to start..."
          npx wait-on http://localhost:5273/api/health -t 60000

      - name: Start frontend server
        run: |
          cd frontend
          npm run serve &
          echo "Waiting for frontend to start..."
          npx wait-on http://localhost:5273 -t 60000

      - name: Determine test type
        id: test-type
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "type=${{ github.event.inputs.test_type }}" >> $GITHUB_OUTPUT
            echo "parallel=${{ github.event.inputs.parallel }}" >> $GITHUB_OUTPUT
            echo "retries=${{ github.event.inputs.retries }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.event_name }}" == "schedule" ]; then
            echo "type=regression" >> $GITHUB_OUTPUT
            echo "parallel=2" >> $GITHUB_OUTPUT
            echo "retries=2" >> $GITHUB_OUTPUT
          else
            echo "type=smoke" >> $GITHUB_OUTPUT
            echo "parallel=1" >> $GITHUB_OUTPUT
            echo "retries=1" >> $GITHUB_OUTPUT
          fi

      - name: Run tests
        id: run-tests
        run: |
          cd e2e
          node ci-test.js --${{ steps.test-type.outputs.type }} --shard=${{ matrix.shard }} --parallel=${{ steps.test-type.outputs.parallel }} --retries=${{ steps.test-type.outputs.retries }} --reporter=list,github,html

      - name: Analyze test results
        if: always()
        run: |
          cd e2e
          node analyze-results.js --html --json

      - name: Upload test reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-reports-${{ matrix.shard }}
          path: |
            e2e/ci-reports/
            e2e/playwright-report/
          retention-days: 30

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results-${{ matrix.shard }}
          path: |
            e2e/test-results/
          retention-days: 30

  report:
    name: Generate Combined Report
    needs: test
    if: always()
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Download all artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts

      - name: Install dependencies
        run: cd e2e && npm ci

      - name: Merge reports
        run: |
          mkdir -p e2e/merged-reports
          npx playwright merge-reports --reporter=html,markdown artifacts/test-results-*

      - name: Upload merged report
        uses: actions/upload-artifact@v3
        with:
          name: merged-report
          path: e2e/merged-reports
          retention-days: 30

      - name: Create GitHub summary
        run: |
          echo "# E2E Test Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          cat e2e/merged-reports/report.md >> $GITHUB_STEP_SUMMARY

      - name: Notify on failure
        if: failure()
        uses: actions/github-script@v6
        with:
          script: |
            const { repo, owner } = context.repo;
            const run_id = context.runId;
            const run_number = context.runNumber;

            const issue = await github.rest.issues.create({
              owner,
              repo,
              title: `❌ E2E Tests failed in run #${run_number}`,
              body: `E2E tests failed in [workflow run #${run_number}](https://github.com/${owner}/${repo}/actions/runs/${run_id}).\n\nPlease check the logs for more details.`,
              labels: ['bug', 'test-failure']
            });

            console.log(`Created issue #${issue.data.number}`);
