/**
 * Security Tests for API Endpoints
 * Tests for common API security vulnerabilities
 */

const { test, expect } = require('@playwright/test');
const ApiTestClient = require('../../utils/api-test-client');

test.describe('API Security Tests', () => {
  let apiClient;
  let authToken;

  test.beforeAll(async () => {
    // Login to get auth token
    apiClient = new ApiTestClient();
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };

    const loginResponse = await apiClient.post('/users/login', loginData);
    authToken = loginResponse.data.data.token;
    apiClient.setAuthToken(authToken);
  });

  test('should require authentication for protected endpoints', async () => {
    // Create a new client without auth token
    const unauthenticatedClient = new ApiTestClient();
    
    // List of protected endpoints to test
    const protectedEndpoints = [
      { method: 'get', endpoint: '/users/me' },
      { method: 'get', endpoint: '/departments' },
      { method: 'get', endpoint: '/forms' },
      { method: 'get', endpoint: '/workflows' },
      { method: 'get', endpoint: '/workflows/tasks/todo' }
    ];
    
    // Test each endpoint
    for (const { method, endpoint } of protectedEndpoints) {
      const response = await unauthenticatedClient[method](endpoint);
      
      // Verify authentication is required
      expect(response.status, `Expected ${endpoint} to require authentication`).toBe(401);
    }
  });

  test('should validate input data', async () => {
    // Test with invalid data
    const invalidDataTests = [
      {
        endpoint: '/departments',
        data: { name: 'Test' }, // Missing required code field
        expectedStatus: 400
      },
      {
        endpoint: '/forms',
        data: { description: 'Test Form' }, // Missing required title field
        expectedStatus: 400
      },
      {
        endpoint: '/workflows',
        data: { title: 'Test Workflow' }, // Missing required form_template_id field
        expectedStatus: 400
      }
    ];
    
    // Test each case
    for (const { endpoint, data, expectedStatus } of invalidDataTests) {
      const response = await apiClient.post(endpoint, data);
      
      // Verify validation is working
      expect(response.status, `Expected ${endpoint} to validate input data`).toBe(expectedStatus);
    }
  });

  test('should prevent path traversal attacks', async () => {
    // Test path traversal attempts
    const traversalAttempts = [
      '/departments/../users',
      '/departments/../../etc/passwd',
      '/forms/%2e%2e/users',
      '/workflows/..%2f..%2f/config'
    ];
    
    // Test each attempt
    for (const path of traversalAttempts) {
      const response = await apiClient.get(path);
      
      // Verify path traversal is prevented
      expect(response.status, `Path traversal should be prevented for ${path}`).not.toBe(200);
    }
  });

  test('should prevent mass assignment', async () => {
    // Create a department
    const departmentData = {
      name: `Security Test Department ${Date.now()}`,
      code: `SEC_TEST_${Date.now()}`,
      description: 'Department created for security testing',
      status: 'active',
      // Try to set fields that should be protected
      id: 999999,
      created_at: '2000-01-01T00:00:00Z',
      is_admin: true,
      admin_access: true
    };

    const response = await apiClient.post('/departments', departmentData);
    
    // Verify department was created
    expect(response.status).toBe(201);
    
    // Verify protected fields were not set
    expect(response.data.data.id).not.toBe(departmentData.id);
    
    if (response.data.data.created_at) {
      expect(response.data.data.created_at).not.toBe(departmentData.created_at);
    }
    
    // Verify other potential protected fields
    expect(response.data.data).not.toHaveProperty('is_admin');
    expect(response.data.data).not.toHaveProperty('admin_access');
  });

  test('should prevent unauthorized access to other users data', async ({ browser }) => {
    // Create a context for regular user
    const userContext = await browser.newContext();
    const userPage = await userContext.newPage();
    
    // Login as regular user
    await userPage.goto('/login');
    await userPage.fill('input[placeholder="用户名"]', 'user');
    await userPage.fill('input[placeholder="密码"]', 'user123');
    await userPage.click('.login-button');
    await userPage.waitForURL(/.*\//);
    
    // Get token from localStorage
    const userToken = await userPage.evaluate(() => localStorage.getItem('token'));
    
    // Skip if no token found
    test.skip(!userToken, 'No user token found');
    
    // Create API client with user token
    const userClient = new ApiTestClient();
    userClient.setAuthToken(userToken);
    
    // Try to access admin-only endpoints
    const adminEndpoints = [
      { method: 'get', endpoint: '/users' }, // List all users
      { method: 'get', endpoint: '/users/1' }, // Get user by ID
      { method: 'get', endpoint: '/admin/stats' } // Admin statistics
    ];
    
    // Test each endpoint
    for (const { method, endpoint } of adminEndpoints) {
      const response = await userClient[method](endpoint);
      
      // Verify access is denied or endpoint doesn't exist
      expect(response.status, `Regular user should not access ${endpoint}`).not.toBe(200);
    }
    
    // Close user context
    await userContext.close();
  });

  test('should prevent XSS in API responses', async () => {
    // Create a department with potential XSS payload
    const xssPayload = '<script>alert("XSS")</script>';
    const departmentData = {
      name: `XSS Test ${xssPayload}`,
      code: `XSS_TEST_${Date.now()}`,
      description: `Description with XSS payload: ${xssPayload}`,
      status: 'active'
    };

    const createResponse = await apiClient.post('/departments', departmentData);
    
    // Skip if creation failed
    test.skip(!createResponse.success, 'Department creation failed');
    
    const departmentId = createResponse.data.data.id;
    
    // Get the department
    const getResponse = await apiClient.get(`/departments/${departmentId}`);
    
    // Verify XSS payload is encoded or sanitized
    const departmentName = getResponse.data.data.name;
    const departmentDescription = getResponse.data.data.description;
    
    // Check if the payload is encoded or sanitized
    // This is a basic check - in a real app, you'd need to check how the data is rendered in the UI
    expect(departmentName).not.toBe(departmentData.name);
    expect(departmentDescription).not.toBe(departmentData.description);
    
    // Clean up
    await apiClient.delete(`/departments/${departmentId}`);
  });
});
