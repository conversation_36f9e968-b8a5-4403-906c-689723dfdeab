const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     WorkflowAttachment:
 *       type: object
 *       required:
 *         - workflow_instance_id
 *         - file_name
 *         - file_path
 *         - file_size
 *         - file_type
 *         - uploader_id
 *       properties:
 *         id:
 *           type: integer
 *           description: 附件ID
 *           example: 1
 *         workflow_instance_id:
 *           type: integer
 *           description: 工作流实例ID
 *           example: 1
 *         task_history_id:
 *           type: integer
 *           description: 任务历史ID
 *           example: 1
 *         file_name:
 *           type: string
 *           description: 文件名
 *           example: "请假申请.pdf"
 *         file_path:
 *           type: string
 *           description: 文件路径
 *           example: "/uploads/workflow/1/请假申请.pdf"
 *         file_size:
 *           type: integer
 *           description: 文件大小（字节）
 *           example: 1024000
 *         file_type:
 *           type: string
 *           description: 文件类型
 *           example: "application/pdf"
 *         uploader_id:
 *           type: integer
 *           description: 上传者ID
 *           example: 1
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class WorkflowAttachment extends Model {
    static associate(models) {
      // 工作流实例
      WorkflowAttachment.belongsTo(models.WorkflowInstance, {
        foreignKey: 'workflow_instance_id',
        as: 'workflowInstance'
      });

      // 任务历史
      WorkflowAttachment.belongsTo(models.WorkflowTaskHistory, {
        foreignKey: 'task_history_id',
        as: 'taskHistory'
      });

      // 上传者
      WorkflowAttachment.belongsTo(models.User, {
        foreignKey: 'uploader_id',
        as: 'uploader'
      });
    }
  }

  WorkflowAttachment.init({
    workflow_instance_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_instances',
        key: 'id'
      }
    },
    task_history_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'workflow_task_histories',
        key: 'id'
      }
    },
    file_name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    file_path: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    file_size: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    file_type: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    uploader_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'WorkflowAttachment',
    tableName: 'workflow_attachments',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false
  });

  return WorkflowAttachment;
};
