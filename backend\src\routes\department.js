const express = require('express');
const router = express.Router();
const departmentController = require('../controllers/department');

/**
 * @swagger
 * tags:
 *   name: Departments
 *   description: 部门管理API
 */

/**
 * @swagger
 * /departments:
 *   get:
 *     summary: 获取所有部门
 *     tags: [Departments]
 *     responses:
 *       200:
 *         description: 成功获取部门列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: "技术部"
 *                       description:
 *                         type: string
 *                         example: "负责公司技术研发"
 *                       parent_id:
 *                         type: integer
 *                         nullable: true
 *                         example: null
 *       500:
 *         description: 服务器错误
 */
router.get('/', departmentController.getAllDepartments);

/**
 * @swagger
 * /departments/{id}:
 *   get:
 *     summary: 获取单个部门信息
 *     tags: [Departments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 部门ID
 *     responses:
 *       200:
 *         description: 成功获取部门信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     name:
 *                       type: string
 *                       example: "技术部"
 *                     description:
 *                       type: string
 *                       example: "负责公司技术研发"
 *                     parent_id:
 *                       type: integer
 *                       nullable: true
 *                       example: null
 *       404:
 *         description: 部门不存在
 *       500:
 *         description: 服务器错误
 */
router.get('/:id', departmentController.getDepartmentById);

/**
 * @swagger
 * /departments:
 *   post:
 *     summary: 创建新部门
 *     tags: [Departments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 example: "市场部"
 *               description:
 *                 type: string
 *                 example: "负责公司市场营销"
 *               parent_id:
 *                 type: integer
 *                 example: 1
 *     responses:
 *       201:
 *         description: 部门创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 2
 *                     name:
 *                       type: string
 *                       example: "市场部"
 *                     description:
 *                       type: string
 *                       example: "负责公司市场营销"
 *                     parent_id:
 *                       type: integer
 *                       example: 1
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/', departmentController.createDepartment);

// 更新部门
router.put('/:id', departmentController.updateDepartment);

// 删除部门
router.delete('/:id', departmentController.deleteDepartment);

// 获取部门成员
router.get('/:id/members', departmentController.getDepartmentMembers);

// 添加部门成员
router.post('/:id/members', departmentController.addDepartmentMember);

// 更新部门成员
router.put('/:id/members/:userId', departmentController.updateDepartmentMember);

// 移除部门成员
router.delete('/:id/members/:userId', departmentController.removeDepartmentMember);

module.exports = router;
