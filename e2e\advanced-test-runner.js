/**
 * Advanced Test Runner
 * Runs tests with advanced features like monitoring, reporting, and integrations
 * 
 * Usage:
 *   node advanced-test-runner.js [options]
 * 
 * Options:
 *   --type=TYPE       Type of tests to run (smoke, regression, api, a11y, security, visual, all)
 *   --parallel=N      Run N workers in parallel (default: 2)
 *   --retries=N       Retry failed tests N times (default: 1)
 *   --timeout=N       Set test timeout to N milliseconds (default: 60000)
 *   --project=NAME    Run specific project (admin, user, mobile)
 *   --reporter=NAME   Set reporter (default: list,html)
 *   --monitor         Enable performance monitoring
 *   --report          Generate detailed HTML and JSON reports
 *   --notify          Send notifications to configured integrations
 *   --clean           Clean up test data after tests
 *   --debug           Run in debug mode
 *   --headed          Run in headed mode
 */

const { spawnSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const TestDataAPI = require('./utils/test-data-api');
const TestMonitor = require('./utils/test-monitor');
const TestReporter = require('./utils/test-reporter');
const TestIntegrations = require('./utils/test-integrations');

// Parse command line arguments
const args = process.argv.slice(2);

// Helper function to get parameter value
const getParamValue = (paramName, defaultValue) => {
  const param = args.find(arg => arg.startsWith(`--${paramName}=`));
  if (param) {
    const value = param.split('=')[1];
    return isNaN(parseInt(value)) ? value : parseInt(value);
  }
  return defaultValue;
};

// Test options
const testType = getParamValue('type', 'smoke');
const parallelWorkers = getParamValue('parallel', 2);
const retryCount = getParamValue('retries', 1);
const testTimeout = getParamValue('timeout', 60000);
const projectName = getParamValue('project', null);
const reporterName = getParamValue('reporter', 'list,html');

// Feature flags
const enableMonitoring = args.includes('--monitor');
const generateReports = args.includes('--report');
const sendNotifications = args.includes('--notify');
const cleanupData = args.includes('--clean');
const debugMode = args.includes('--debug');
const headedMode = args.includes('--headed');

// Generate test run ID
const testRunId = Date.now().toString(36) + Math.random().toString(36).substring(2);

// Create timestamp for reports
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

// Create directory for reports
const reportsDir = path.join(__dirname, 'advanced-reports', timestamp);
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
}

// Initialize services
const testDataApi = new TestDataAPI({
  testRunId
});

const testMonitor = enableMonitoring ? new TestMonitor({
  testRunId,
  logDir: path.join(reportsDir, 'logs')
}) : null;

const testReporter = generateReports ? new TestReporter({
  testRunId,
  reportDir: reportsDir
}) : null;

// Load integration configurations
const integrationConfig = (() => {
  try {
    const configPath = path.join(__dirname, 'config', 'integrations.json');
    if (fs.existsSync(configPath)) {
      return JSON.parse(fs.readFileSync(configPath, 'utf8'));
    }
  } catch (error) {
    console.error('Failed to load integration config:', error.message);
  }
  return {};
})();

const testIntegrations = sendNotifications ? new TestIntegrations(integrationConfig) : null;

// Log system info
console.log('=== System Information ===');
console.log(`OS: ${process.platform} ${process.arch}`);
console.log(`Node.js: ${process.version}`);
console.log(`Test Run ID: ${testRunId}`);
console.log('=========================\n');

// Log test configuration
console.log('=== Test Configuration ===');
console.log(`Test type: ${testType}`);
console.log(`Parallel workers: ${parallelWorkers}`);
console.log(`Retry count: ${retryCount}`);
console.log(`Test timeout: ${testTimeout}ms`);
if (projectName) console.log(`Project: ${projectName}`);
console.log(`Reporter: ${reporterName}`);
console.log(`Monitoring: ${enableMonitoring}`);
console.log(`Reporting: ${generateReports}`);
console.log(`Notifications: ${sendNotifications}`);
console.log(`Cleanup: ${cleanupData}`);
console.log(`Debug mode: ${debugMode}`);
console.log(`Headed mode: ${headedMode}`);
console.log('=========================\n');

/**
 * Run tests
 */
async function runTests() {
  try {
    // Start monitoring if enabled
    if (testMonitor) {
      testMonitor.startMonitoring();
    }
    
    // Set up test data
    console.log('Setting up test data...');
    await setupTestData();
    
    // Build command
    let command = 'npx playwright test';
    
    // Add test type
    switch (testType) {
      case 'smoke':
        command += ' tests/login.spec.js tests/home.spec.js tests/workflow-form.spec.js';
        break;
      case 'regression':
        command += ' tests/department.spec.js tests/form-design.spec.js tests/workflow-design.spec.js tests/workflow-process.spec.js tests/permissions.spec.js tests/form-validation.spec.js tests/error-handling.spec.js';
        break;
      case 'api':
        command += ' tests/api/';
        break;
      case 'a11y':
        command += ' tests/accessibility/';
        break;
      case 'security':
        command += ' tests/security/';
        break;
      case 'visual':
        command += ' tests/visual-regression.spec.js';
        break;
      case 'all':
        // Don't specify any files to run all tests
        break;
      default:
        command += ` tests/${testType}`;
    }
    
    // Add options
    command += ` --workers=${parallelWorkers}`;
    command += ` --retries=${retryCount}`;
    command += ` --timeout=${testTimeout}`;
    command += ` --reporter=${reporterName}`;
    
    // Add project if specified
    if (projectName) {
      command += ` --project=${projectName}`;
    }
    
    // Add debug mode flag if specified
    if (debugMode) {
      command += ' --debug';
    }
    
    // Add headed mode flag if specified
    if (headedMode) {
      command += ' --headed';
    }
    
    // Add output directory for reports
    command += ` --output=${reportsDir}`;
    
    console.log(`Running command: ${command}`);
    
    // Start time
    const startTime = Date.now();
    
    // Run tests
    const result = spawnSync(command, { shell: true, stdio: 'inherit' });
    
    // End time
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Parse test results
    const testResults = parseTestResults(reportsDir, duration, result.status === 0);
    
    // Stop monitoring if enabled
    if (testMonitor) {
      testMonitor.stopMonitoring();
    }
    
    // Generate reports if enabled
    if (testReporter) {
      testReporter.setTestResults(testResults);
      
      if (testMonitor) {
        testReporter.setMonitor(testMonitor);
      }
      
      const reports = testReporter.generateReports();
      console.log(`HTML report generated: ${reports.html.path}`);
      console.log(`JSON report generated: ${reports.json.path}`);
    }
    
    // Send notifications if enabled
    if (testIntegrations && sendNotifications) {
      await sendNotifications(testResults);
    }
    
    // Clean up test data if enabled
    if (cleanupData) {
      console.log('Cleaning up test data...');
      await testDataApi.cleanup();
    }
    
    // Log results
    console.log('\n=== Test Results ===');
    console.log(`Status: ${result.status === 0 ? 'Success' : 'Failure'}`);
    console.log(`Duration: ${(duration / 1000).toFixed(2)} seconds`);
    if (testResults) {
      console.log(`Total: ${testResults.total}`);
      console.log(`Passed: ${testResults.passed}`);
      console.log(`Failed: ${testResults.failed}`);
      console.log(`Skipped: ${testResults.skipped}`);
    }
    console.log('===================\n');
    
    // Exit with the same code as the tests
    process.exit(result.status);
  } catch (error) {
    console.error('Error running tests:', error.message);
    
    // Stop monitoring if enabled
    if (testMonitor) {
      testMonitor.stopMonitoring();
    }
    
    process.exit(1);
  }
}

/**
 * Set up test data
 */
async function setupTestData() {
  try {
    // Login to get auth token
    const loginResponse = await testDataApi.request('post', '/users/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    if (!loginResponse.success) {
      throw new Error('Failed to login');
    }
    
    const authToken = loginResponse.data.data.token;
    testDataApi.setAuthToken(authToken);
    
    // Create test data
    const department = await testDataApi.createDepartment({
      name: `Advanced Test Department ${Date.now()}`,
      code: `ADV_TEST_${Date.now()}`
    });
    console.log(`Created department: ${department.name} (ID: ${department.id})`);
    
    const form = await testDataApi.createForm({
      title: `Advanced Test Form ${Date.now()}`
    });
    console.log(`Created form: ${form.title} (ID: ${form.id})`);
    
    const workflow = await testDataApi.createWorkflow({
      title: `Advanced Test Workflow ${Date.now()}`,
      form_template_id: form.id
    });
    console.log(`Created workflow: ${workflow.title} (ID: ${workflow.id})`);
    
    // Create a test user
    const user = await testDataApi.createUser({
      username: `adv_test_user_${Date.now()}`,
      password: 'Test123!',
      full_name: 'Advanced Test User'
    });
    console.log(`Created user: ${user.username} (ID: ${user.id})`);
    
    return {
      department,
      form,
      workflow,
      user
    };
  } catch (error) {
    console.error('Error setting up test data:', error.message);
    throw error;
  }
}

/**
 * Parse test results
 * @param {string} reportsDir - Reports directory
 * @param {number} duration - Test duration
 * @param {boolean} success - Test success
 * @returns {object} - Test results
 */
function parseTestResults(reportsDir, duration, success) {
  try {
    // Try to parse Playwright results
    const resultsPath = path.join(reportsDir, 'results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      // Count test results
      let passed = 0;
      let failed = 0;
      let skipped = 0;
      const failedTests = [];
      const specs = [];
      const files = {};
      
      // Process suites recursively
      const processSuite = (suite) => {
        // Process specs
        if (suite.specs) {
          suite.specs.forEach(spec => {
            specs.push({
              title: spec.title,
              file: suite.file,
              status: spec.ok ? 'passed' : (spec.skipped ? 'skipped' : 'failed'),
              duration: spec.duration,
              error: spec.error ? {
                message: spec.error.message,
                stack: spec.error.stack
              } : null
            });
            
            if (spec.ok) {
              passed++;
            } else if (spec.skipped) {
              skipped++;
            } else {
              failed++;
              failedTests.push({
                title: spec.title,
                file: suite.file,
                error: spec.error ? spec.error.message : 'Unknown error'
              });
            }
            
            // Update file stats
            if (suite.file) {
              if (!files[suite.file]) {
                files[suite.file] = { passed: 0, failed: 0, skipped: 0, total: 0 };
              }
              
              files[suite.file].total++;
              
              if (spec.ok) {
                files[suite.file].passed++;
              } else if (spec.skipped) {
                files[suite.file].skipped++;
              } else {
                files[suite.file].failed++;
              }
            }
          });
        }
        
        // Process child suites
        if (suite.suites) {
          suite.suites.forEach(processSuite);
        }
      };
      
      // Process all suites
      results.suites.forEach(processSuite);
      
      return {
        success,
        duration,
        total: passed + failed + skipped,
        passed,
        failed,
        skipped,
        failedTests,
        specs,
        files
      };
    }
  } catch (error) {
    console.error('Error parsing test results:', error.message);
  }
  
  // Return basic results if parsing failed
  return {
    success,
    duration,
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    failedTests: []
  };
}

/**
 * Send notifications
 * @param {object} testResults - Test results
 */
async function sendNotifications(testResults) {
  try {
    // Generate report URL (this would be a real URL in a CI environment)
    const reportUrl = `file://${path.join(reportsDir, 'report.html')}`;
    
    // Send to Slack if configured
    if (integrationConfig.slack) {
      console.log('Sending results to Slack...');
      await testIntegrations.sendToSlack(testResults, {
        reportUrl,
        title: `Test Run Results: ${testType}`
      });
    }
    
    // Send to Teams if configured
    if (integrationConfig.teams) {
      console.log('Sending results to Microsoft Teams...');
      await testIntegrations.sendToTeams(testResults, {
        reportUrl,
        title: `Test Run Results: ${testType}`
      });
    }
    
    // Send to Jira if configured
    if (integrationConfig.jira) {
      console.log('Sending results to Jira...');
      await testIntegrations.sendToJira(testResults, {
        summary: `Test Run Results: ${testType} - ${testResults.success ? 'Success' : 'Failure'}`,
        reportPath: path.join(reportsDir, 'report.html')
      });
    }
    
    // Send email if configured
    if (integrationConfig.email) {
      console.log('Sending results via email...');
      await testIntegrations.sendEmail(testResults, {
        subject: `Test Run Results: ${testType} - ${testResults.success ? 'Success' : 'Failure'}`,
        reportPath: path.join(reportsDir, 'report.html')
      });
    }
  } catch (error) {
    console.error('Error sending notifications:', error.message);
  }
}

// Run tests
runTests();
