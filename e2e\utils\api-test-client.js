/**
 * API Test Client
 * Utility for testing backend APIs directly
 */

const axios = require('axios');
const { expect } = require('@playwright/test');

class ApiTestClient {
  constructor(config = {}) {
    this.baseURL = config.baseURL || 'http://localhost:5273/api';
    this.authToken = config.authToken || '';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  /**
   * Set auth token for API requests
   * @param {string} token - JWT token
   */
  setAuthToken(token) {
    this.authToken = token;
  }

  /**
   * Get request headers including auth token if available
   * @returns {object} - Headers object
   */
  getHeaders() {
    const headers = { ...this.defaultHeaders };
    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }
    return headers;
  }

  /**
   * Make an API request
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {object} data - Request data
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Response data
   */
  async request(method, endpoint, data = null, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      method,
      url,
      headers: this.getHeaders(),
      ...options
    };

    if (data) {
      config.data = data;
    }

    try {
      const response = await axios(config);
      return {
        status: response.status,
        data: response.data,
        headers: response.headers,
        success: true
      };
    } catch (error) {
      if (error.response) {
        return {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers,
          success: false,
          error: error.message
        };
      }
      throw error;
    }
  }

  /**
   * Make a GET request
   * @param {string} endpoint - API endpoint
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Response data
   */
  async get(endpoint, options = {}) {
    return this.request('get', endpoint, null, options);
  }

  /**
   * Make a POST request
   * @param {string} endpoint - API endpoint
   * @param {object} data - Request data
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Response data
   */
  async post(endpoint, data, options = {}) {
    return this.request('post', endpoint, data, options);
  }

  /**
   * Make a PUT request
   * @param {string} endpoint - API endpoint
   * @param {object} data - Request data
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Response data
   */
  async put(endpoint, data, options = {}) {
    return this.request('put', endpoint, data, options);
  }

  /**
   * Make a DELETE request
   * @param {string} endpoint - API endpoint
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Response data
   */
  async delete(endpoint, options = {}) {
    return this.request('delete', endpoint, null, options);
  }

  /**
   * Verify response status code
   * @param {object} response - API response
   * @param {number} expectedStatus - Expected status code
   */
  verifyStatus(response, expectedStatus = 200) {
    expect(response.status, `Expected status ${expectedStatus}, got ${response.status}`).toBe(expectedStatus);
  }

  /**
   * Verify response has success flag
   * @param {object} response - API response
   */
  verifySuccess(response) {
    expect(response.data.success, `Expected success: true, got ${response.data.success}`).toBe(true);
  }

  /**
   * Verify response has error flag
   * @param {object} response - API response
   */
  verifyError(response) {
    expect(response.data.success, `Expected success: false, got ${response.data.success}`).toBe(false);
  }

  /**
   * Verify response has specific data property
   * @param {object} response - API response
   * @param {string} property - Property name
   * @param {any} expectedValue - Expected value (optional)
   */
  verifyProperty(response, property, expectedValue = undefined) {
    expect(response.data).toHaveProperty(property);
    if (expectedValue !== undefined) {
      expect(response.data[property]).toEqual(expectedValue);
    }
  }

  /**
   * Verify response data matches schema
   * @param {object} response - API response
   * @param {Array<string>} requiredProperties - List of required properties
   */
  verifySchema(response, requiredProperties) {
    for (const prop of requiredProperties) {
      expect(response.data).toHaveProperty(prop);
    }
  }

  /**
   * Verify response time is within acceptable range
   * @param {number} startTime - Start time in milliseconds
   * @param {number} maxTime - Maximum acceptable time in milliseconds
   */
  verifyResponseTime(startTime, maxTime = 1000) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    expect(responseTime, `Response time ${responseTime}ms exceeds maximum ${maxTime}ms`).toBeLessThanOrEqual(maxTime);
  }
}

module.exports = ApiTestClient;
