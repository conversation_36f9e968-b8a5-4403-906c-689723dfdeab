const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class WorkflowTemplateVersion extends Model {
    static associate(models) {
      // 工作流模板
      WorkflowTemplateVersion.belongsTo(models.WorkflowTemplate, {
        foreignKey: 'workflow_template_id',
        as: 'workflowTemplate'
      });

      // 创建者
      WorkflowTemplateVersion.belongsTo(models.User, {
        foreignKey: 'creator_id',
        as: 'creator'
      });
    }
  }

  WorkflowTemplateVersion.init({
    workflow_template_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_templates',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    version: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    schema: {
      type: DataTypes.JSONB,
      allowNull: false
    },
    creator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'WorkflowTemplateVersion',
    tableName: 'workflow_template_versions',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
    indexes: [
      {
        unique: true,
        fields: ['workflow_template_id', 'version']
      }
    ]
  });

  return WorkflowTemplateVersion;
};
