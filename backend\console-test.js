/**
 * Console test
 * Run with: node console-test.js
 */

// 使用console.log
console.log('使用console.log打印消息');
console.log('这是第二行消息');
console.log('这是第三行消息');

// 使用console.error
console.error('使用console.error打印错误消息');
console.error('这是第二行错误消息');
console.error('这是第三行错误消息');

// 使用console.info
console.info('使用console.info打印信息消息');
console.info('这是第二行信息消息');
console.info('这是第三行信息消息');

// 使用console.warn
console.warn('使用console.warn打印警告消息');
console.warn('这是第二行警告消息');
console.warn('这是第三行警告消息');

// 使用console.debug
console.debug('使用console.debug打印调试消息');
console.debug('这是第二行调试消息');
console.debug('这是第三行调试消息');

// 使用console.table
console.table([
  { name: '张三', age: 20, gender: '男' },
  { name: '李四', age: 30, gender: '男' },
  { name: '王五', age: 40, gender: '女' }
]);

// 使用console.dir
console.dir({ name: '张三', age: 20, gender: '男', address: { city: '北京', district: '海淀' } });

// 使用console.time和console.timeEnd
console.time('计时器');
for (let i = 0; i < 1000000; i++) {
  // 空循环
}
console.timeEnd('计时器');

// 使用console.trace
function a() {
  b();
}
function b() {
  c();
}
function c() {
  console.trace('调用栈跟踪');
}
a();
