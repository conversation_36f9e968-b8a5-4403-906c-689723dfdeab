'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('workflow_instances', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_templates',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      initiator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      title: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'running'
      },
      form_data: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      current_node_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'workflow_nodes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 添加索引
    await queryInterface.addIndex('workflow_instances', ['workflow_template_id']);
    await queryInterface.addIndex('workflow_instances', ['initiator_id']);
    await queryInterface.addIndex('workflow_instances', ['status']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('workflow_instances');
  }
};
