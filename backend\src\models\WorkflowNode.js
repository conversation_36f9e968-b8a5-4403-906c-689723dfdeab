const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     WorkflowNode:
 *       type: object
 *       required:
 *         - workflow_template_id
 *         - node_key
 *         - node_type
 *         - name
 *       properties:
 *         id:
 *           type: integer
 *           description: 节点ID
 *           example: 1
 *         workflow_template_id:
 *           type: integer
 *           description: 所属工作流模板ID
 *           example: 1
 *         node_key:
 *           type: string
 *           description: 节点唯一标识
 *           example: "start_node"
 *         node_type:
 *           type: string
 *           description: 节点类型
 *           enum: [start, end, approval, task, condition]
 *           example: "approval"
 *         name:
 *           type: string
 *           description: 节点名称
 *           example: "部门经理审批"
 *         description:
 *           type: string
 *           description: 节点描述
 *           example: "由部门经理进行审批"
 *         config:
 *           type: object
 *           description: 节点配置
 *           example: {
 *             "assignee_type": "role",
 *             "assignee_role_id": 2,
 *             "due_days": 3,
 *             "priority": "high"
 *           }
 *         position_x:
 *           type: integer
 *           description: 节点X坐标
 *           example: 300
 *         position_y:
 *           type: integer
 *           description: 节点Y坐标
 *           example: 200
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class WorkflowNode extends Model {
    static associate(models) {
      // 工作流模板
      WorkflowNode.belongsTo(models.WorkflowTemplate, {
        foreignKey: 'workflow_template_id',
        as: 'workflowTemplate'
      });

      // 只有当 WorkflowTransition 模型存在时才建立关联
      if (models.WorkflowTransition) {
        // 转换关系 - 作为源节点
        WorkflowNode.hasMany(models.WorkflowTransition, {
          foreignKey: 'source_node_id',
          as: 'outgoingTransitions'
        });

        // 转换关系 - 作为目标节点
        WorkflowNode.hasMany(models.WorkflowTransition, {
          foreignKey: 'target_node_id',
          as: 'incomingTransitions'
        });
      }
    }
  }

  WorkflowNode.init({
    workflow_template_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_templates',
        key: 'id'
      }
    },
    node_key: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    node_type: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    config: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    position_x: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    position_y: {
      type: DataTypes.INTEGER,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'WorkflowNode',
    tableName: 'workflow_nodes',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return WorkflowNode;
};
