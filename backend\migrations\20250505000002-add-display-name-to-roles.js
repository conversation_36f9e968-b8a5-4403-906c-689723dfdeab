'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 添加 display_name 列到 roles 表
    await queryInterface.addColumn('roles', 'display_name', {
      type: Sequelize.STRING(100),
      allowNull: true // 初始设置为可空，以便现有记录不会出错
    });

    // 更新现有记录，将 name 的值复制到 display_name
    await queryInterface.sequelize.query(`
      UPDATE roles 
      SET display_name = name 
      WHERE display_name IS NULL
    `);

    // 将 display_name 列设置为非空
    await queryInterface.changeColumn('roles', 'display_name', {
      type: Sequelize.STRING(100),
      allowNull: false
    });
  },

  down: async (queryInterface, Sequelize) => {
    // 删除 display_name 列
    await queryInterface.removeColumn('roles', 'display_name');
  }
};
