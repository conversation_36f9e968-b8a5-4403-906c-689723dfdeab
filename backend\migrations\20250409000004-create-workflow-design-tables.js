'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 创建工作流模板表
    await queryInterface.createTable('workflow_templates', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      title: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      form_template_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'form_templates',
          key: 'id'
        },
        onDelete: 'SET NULL'
      },
      creator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      version: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1
      },
      status: {
        type: Sequelize.STRING(20),
        defaultValue: 'draft'
      },
      schema: {
        type: Sequelize.JSONB,
        allowNull: false
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 创建工作流模板版本表
    await queryInterface.createTable('workflow_template_versions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_templates',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      version: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      schema: {
        type: Sequelize.JSONB,
        allowNull: false
      },
      creator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 添加唯一约束
    await queryInterface.addConstraint('workflow_template_versions', {
      fields: ['workflow_template_id', 'version'],
      type: 'unique',
      name: 'unique_workflow_template_version'
    });

    // 创建工作流节点表
    await queryInterface.createTable('workflow_nodes', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_templates',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      node_key: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      node_type: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      config: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      position_x: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      position_y: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 添加唯一约束
    await queryInterface.addConstraint('workflow_nodes', {
      fields: ['workflow_template_id', 'node_key'],
      type: 'unique',
      name: 'unique_workflow_node_key'
    });

    // 创建工作流转换表
    await queryInterface.createTable('workflow_transitions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_templates',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      source_node_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_nodes',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      target_node_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_nodes',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      condition: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 创建索引
    await queryInterface.addIndex('workflow_templates', ['form_template_id'], {
      name: 'idx_workflow_templates_form_template_id'
    });
    await queryInterface.addIndex('workflow_templates', ['creator_id'], {
      name: 'idx_workflow_templates_creator_id'
    });
    await queryInterface.addIndex('workflow_templates', ['status'], {
      name: 'idx_workflow_templates_status'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('workflow_transitions');
    await queryInterface.dropTable('workflow_nodes');
    await queryInterface.dropTable('workflow_template_versions');
    await queryInterface.dropTable('workflow_templates');
  }
};
