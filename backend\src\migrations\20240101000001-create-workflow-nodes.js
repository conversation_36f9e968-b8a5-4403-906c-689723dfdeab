'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('workflow_nodes', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_templates',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      node_key: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      node_type: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      config: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      position_x: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      position_y: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 添加索引
    await queryInterface.addIndex('workflow_nodes', ['workflow_template_id']);
    await queryInterface.addIndex('workflow_nodes', ['node_key']);
    await queryInterface.addIndex('workflow_nodes', ['node_type']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('workflow_nodes');
  }
};
