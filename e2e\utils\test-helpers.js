/**
 * Helper functions for E2E tests
 */

// Test user credentials
const TEST_USERS = {
  admin: {
    username: 'admin',
    password: 'admin123',
    fullName: '系统管理员'
  },
  user: {
    username: 'user',
    password: 'user123',
    fullName: '普通用户'
  }
};

/**
 * Login with the specified user
 * @param {import('@playwright/test').Page} page - Playwright page
 * @param {string} userType - Type of user (admin or user)
 */
async function login(page, userType = 'admin') {
  const user = TEST_USERS[userType];

  // Navigate to login page
  await page.goto('/login');

  // Fill login form
  await page.fill('input[placeholder="用户名"]', user.username);
  await page.fill('input[placeholder="密码"]', user.password);

  // Click login button
  await page.click('.login-button');

  // Wait for navigation to complete
  await page.waitForURL(/.*\//);
}

/**
 * Navigate to a specific module from the home page
 * @param {import('@playwright/test').Page} page - Playwright page
 * @param {string} moduleName - Name of the module to navigate to
 */
async function navigateToModule(page, moduleName) {
  // Go to home page
  await page.goto('/');

  // Wait for the page to load completely
  await page.waitForLoadState('networkidle');

  // Log available modules for debugging
  console.log('Available modules:');
  const modules = await page.locator('h3').allTextContents();
  console.log(modules);

  // Try to find the module by heading first
  try {
    const moduleHeading = await page.locator('h3').filter({ hasText: moduleName }).first();
    if (await moduleHeading.isVisible()) {
      // Find the closest "进入模块" button
      const button = await moduleHeading.locator('xpath=./following::button[contains(., "进入模块")][1]');
      await button.click();
      return;
    }
  } catch (error) {
    console.log(`Could not find module heading for ${moduleName}:`, error);
  }

  // If heading approach fails, try direct navigation based on URL
  try {
    // Map module names to their respective routes
    const moduleRoutes = {
      '工作流填写': '/workflow-form',
      '工作流设计': '/workflow-design',
      '工作流流转': '/workflow-process',
      '表单设计器': '/form-design',
      '部门配置': '/department'
    };

    if (moduleRoutes[moduleName]) {
      await page.goto(moduleRoutes[moduleName]);
      return;
    }
  } catch (error) {
    console.log(`Direct navigation failed for ${moduleName}:`, error);
  }

  // As a last resort, try clicking the button directly
  try {
    await page.locator('button:has-text("进入模块")').filter({ hasText: new RegExp(moduleName) }).click();
  } catch (error) {
    console.error(`Failed to navigate to module ${moduleName}:`, error);
    throw new Error(`Navigation to module ${moduleName} failed`);
  }
}

module.exports = {
  TEST_USERS,
  login,
  navigateToModule
};
