/**
 * 直接控制台日志中间件
 * 使用最简单的方式打印请求和响应信息
 */
function directConsoleLogger(req, res, next) {
  // 记录请求开始时间
  const start = Date.now();
  
  // 直接打印请求信息
  console.log(`\n>>> 请求: ${req.method} ${req.url}`);
  console.log(`>>> 请求体: ${JSON.stringify(req.body)}`);
  
  // 保存原始的json方法
  const originalJson = res.json;
  
  // 重写json方法
  res.json = function(data) {
    // 计算响应时间
    const duration = Date.now() - start;
    
    // 直接打印响应信息
    console.log(`<<< 响应: 状态码 ${res.statusCode} (${duration}ms)`);
    console.log(`<<< 响应数据: ${JSON.stringify(data)}`);
    
    // 调用原始方法
    return originalJson.call(this, data);
  };
  
  next();
}

module.exports = directConsoleLogger;
