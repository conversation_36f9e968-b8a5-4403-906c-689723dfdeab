const { test, expect } = require('@playwright/test');
const { login, navigateToModule } = require('../utils/test-helpers');

test.describe('Error Handling Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await login(page, 'admin');
  });

  test('should handle invalid form input gracefully', async ({ page }) => {
    // Navigate to form design page
    await navigateToModule(page, '表单设计器');
    
    // Click on create form button
    await page.click('button:has-text("创建表单")');
    
    // Try to save without required fields
    await page.click('button:has-text("保存")');
    
    // Verify validation errors are shown
    await expect(page.locator('.el-form-item__error')).toBeVisible();
    
    // Verify no crash occurred
    await expect(page.locator('.form-canvas')).toBeVisible();
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Navigate to department page
    await navigateToModule(page, '部门配置');
    
    // Simulate offline mode
    await page.context().setOffline(true);
    
    // Try to add a department
    await page.click('button:has-text("添加部门")');
    
    // Fill department form
    await page.fill('input[placeholder="请输入部门名称"]', '测试部门');
    await page.fill('input[placeholder="请输入部门代码"]', 'TEST' + Date.now());
    
    // Submit form
    await page.click('.el-dialog__footer button:has-text("确定")');
    
    // Verify error message is shown
    await expect(page.locator('.el-message--error')).toBeVisible();
    
    // Restore online mode
    await page.context().setOffline(false);
  });

  test('should handle session timeout gracefully', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Clear localStorage to simulate session timeout
    await page.evaluate(() => {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    });
    
    // Try to navigate to a protected page
    await page.goto('/department');
    
    // Verify redirect to login page
    await expect(page).toHaveURL(/.*login/);
  });

  test('should handle invalid URLs gracefully', async ({ page }) => {
    // Try to navigate to a non-existent page
    await page.goto('/non-existent-page');
    
    // Verify either redirect to home/404 page or error message
    try {
      // Check if redirected to home page
      const currentUrl = page.url();
      if (currentUrl.endsWith('/')) {
        // Successfully redirected to home
        await expect(page.locator('h1')).toContainText('欢迎使用工作流系统');
      } else if (currentUrl.includes('404')) {
        // Redirected to 404 page
        await expect(page.locator('body')).toContainText('404');
      } else {
        // Check if error message is shown
        const errorVisible = await page.locator('.error-page, .not-found').isVisible();
        expect(errorVisible).toBeTruthy();
      }
    } catch (error) {
      // If none of the above, the application should still be functional
      // Try to navigate to home page
      await page.goto('/');
      await expect(page.locator('h1')).toBeVisible();
    }
  });

  test('should handle large data sets without crashing', async ({ page }) => {
    // Navigate to workflow form page
    await navigateToModule(page, '工作流填写');
    
    // Check if there are available workflows
    const hasWorkflows = await page.locator('.el-table__row').count() > 0;
    
    if (!hasWorkflows) {
      test.skip('No available workflows to test');
      return;
    }
    
    // Click on start button of the first workflow
    await page.locator('.el-table__row').first().locator('button:has-text("发起")').click();
    
    // Fill workflow title
    const title = '大数据测试' + Date.now();
    await page.fill('input[placeholder="请输入工作流标题"]', title);
    
    // Fill form fields with large data
    // For text inputs
    const textInputs = await page.locator('input[type="text"]:not([placeholder="请输入工作流标题"])').all();
    for (const input of textInputs) {
      await input.fill('A'.repeat(1000)); // Try with a large string
    }
    
    // For textareas
    const textareas = await page.locator('textarea').all();
    for (const textarea of textareas) {
      await textarea.fill('B'.repeat(5000)); // Try with a very large string
    }
    
    // Submit the form
    await page.click('button:has-text("提交")');
    
    // Verify either success or validation error message
    await expect(page.locator('.el-message--success, .el-message--error, .el-form-item__error')).toBeVisible();
    
    // Verify the page is still responsive
    await page.goto('/');
    await expect(page.locator('h1')).toBeVisible();
  });
});
