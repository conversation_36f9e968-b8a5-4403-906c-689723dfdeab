<template>
  <div class="form-data-display">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="表单数据" name="values">
        <div v-if="formattedData && formattedData.length > 0" class="form-data-container">
          <div class="form-item" v-for="item in paginatedData" :key="item.key">
            <div class="form-label">{{ item.label }}</div>
            <div class="form-value">{{ item.value }}</div>
          </div>

          <!-- 分页组件 -->
          <div class="pagination-container" v-if="formattedData && formattedData.length > pageSize">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[5, 10, 20, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="formattedData.length"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              background
            />
          </div>
        </div>
        <el-empty v-else description="无表单数据"></el-empty>
      </el-tab-pane>
      <el-tab-pane label="表单架构" name="schema">
        <div v-if="formSchema">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="表单字段">
              <el-table :data="formFields" border style="width: 100%">
                <el-table-column prop="field_key" label="字段键名" width="180" />
                <el-table-column prop="label" label="字段标签" width="180" />
                <el-table-column prop="field_type" label="字段类型">
                  <template #default="scope">
                    <el-tag>{{ getFieldTypeText(scope.row.field_type) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="选项" v-if="hasOptionsFields">
                  <template #default="scope">
                    <div v-if="scope.row.options && scope.row.options.length > 0">
                      <el-tag
                        v-for="(option, index) in scope.row.options"
                        :key="index"
                        size="small"
                        style="margin-right: 5px; margin-bottom: 5px;">
                        {{ option.label }}
                      </el-tag>
                    </div>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-descriptions-item>
            <el-descriptions-item label="表单属性" v-if="formSchema && formSchema.formProps">
              <div>
                <p><strong>标签宽度:</strong> {{ formSchema.formProps.labelWidth || '-' }}</p>
                <p><strong>标签位置:</strong> {{ getLabelPositionText(formSchema.formProps.labelPosition) }}</p>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <el-empty v-else description="无表单架构"></el-empty>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  // 表单数据，可以是对象或者包含values和schema的对象
  formData: {
    type: Object,
    default: () => ({})
  },
  // 表单架构，如果formData中已包含schema则可选
  schema: {
    type: Object,
    default: null
  }
})

const activeTab = ref('values')

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(10)

// 处理页码变化
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

// 处理每页条数变化
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  // 当每页条数变化时，如果当前页码超出范围，重置为第一页
  const totalPages = Math.ceil(formattedData.value.length / pageSize.value)
  if (totalPages > 0 && currentPage.value > totalPages) {
    currentPage.value = 1
  }
}

// 计算当前页的数据
const paginatedData = computed(() => {
  if (!formattedData?.value || formattedData.value.length === 0) {
    return []
  }
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return formattedData.value.slice(start, end)
})

// 获取表单值
const formValues = computed(() => {
  if (!props.formData) return {}

  // 如果formData包含values属性，说明是格式化后的数据
  if (props.formData.values) {
    return props.formData.values
  }

  // 如果formData是工作流实例，尝试获取form_data
  if (props.formData.form_data) {
    // 如果form_data包含values属性
    if (props.formData.form_data.values) {
      return props.formData.form_data.values
    }
    // 否则整个form_data就是值对象
    return props.formData.form_data
  }

  // 否则整个formData就是值对象
  return props.formData
})

// 获取表单架构
const formSchema = computed(() => {
  // 1. 首先检查直接传入的schema
  if (props.schema) {
    return props.schema
  }

  // 2. 检查formData中的schema
  if (props.formData && props.formData.schema) {
    return props.formData.schema
  }

  // 3. 检查工作流实例中的form_data.schema
  if (props.formData && props.formData.form_data && props.formData.form_data.schema) {
    return props.formData.form_data.schema
  }

  // 4. 检查工作流实例中的form_schema
  if (props.formData && props.formData.form_schema) {
    return props.formData.form_schema
  }

  // 5. 检查工作流实例中的workflow_schema
  if (props.formData && props.formData.workflow_schema) {
    return props.formData.workflow_schema
  }

  return null
})

// 获取表单字段
const formFields = computed(() => {
  // 尝试从不同来源获取表单字段

  // 1. 检查schema中的字段
  if (formSchema.value && formSchema.value.fields && Array.isArray(formSchema.value.fields)) {
    return formSchema.value.fields
  }

  // 2. 检查工作流实例中的表单模板字段
  if (props.formData && props.formData.workflowTemplate &&
      props.formData.workflowTemplate.formTemplate &&
      props.formData.workflowTemplate.formTemplate.fields) {
    return props.formData.workflowTemplate.formTemplate.fields
  }

  // 3. 检查直接传入的字段
  if (props.formData && props.formData.fields && Array.isArray(props.formData.fields)) {
    return props.formData.fields
  }

  return []
})

// 检查是否有包含选项的字段
const hasOptionsFields = computed(() => {
  if (!formFields.value || formFields.value.length === 0) return false

  return formFields.value.some(field =>
    field.field_type === 'select' ||
    field.field_type === 'radio' ||
    field.field_type === 'checkbox'
  )
})

// 格式化表单数据，将键值对转换为带标签的数据
const formattedData = computed(() => {
  const values = formValues.value
  if (!values || Object.keys(values).length === 0) return []

  // 尝试从不同来源获取表单字段定义
  const getFieldDefinitions = () => {
    // 1. 首先检查直接传入的表单字段
    if (formFields.value && formFields.value.length > 0) {
      return formFields.value
    }

    // 2. 检查表单架构中的字段
    if (formSchema.value && formSchema.value.fields && Array.isArray(formSchema.value.fields)) {
      return formSchema.value.fields
    }

    // 3. 检查工作流实例中的表单模板字段
    if (props.formData && props.formData.workflowTemplate &&
        props.formData.workflowTemplate.formTemplate &&
        props.formData.workflowTemplate.formTemplate.fields) {
      return props.formData.workflowTemplate.formTemplate.fields
    }

    return []
  }

  const allFields = getFieldDefinitions()

  try {
    return Object.entries(values).map(([key, value]) => {
      // 查找字段定义以获取标签
      let label = key
      let formattedValue = formatValue(key, value)

      if (allFields.length > 0) {
        // 尝试通过field_key查找字段定义
        const fieldDef = allFields.find(field => field.field_key === key)
        if (fieldDef) {
          // 确保使用字段标签而不是字段键名
          label = fieldDef.label || key
          formattedValue = formatValueByType(value, fieldDef)
        } else {
          // 如果找不到字段定义，尝试将字段键名格式化为更友好的标签
          label = formatKeyToLabel(key)
        }
      } else {
        // 如果没有字段定义，尝试将字段键名格式化为更友好的标签
        label = formatKeyToLabel(key)
      }

      return {
        key,
        label,
        value: formattedValue
      }
    })
  } catch (error) {
    console.error('格式化表单数据时出错:', error)
    return []
  }
})

// 监听数据变化，重置为第一页
watch(() => formattedData.value, () => {
  currentPage.value = 1
})

// 根据字段类型格式化值
const formatValueByType = (value, fieldDef) => {
  if (value === null || value === undefined) return '-'
  if (!fieldDef || !fieldDef.field_type) return String(value)

  const type = fieldDef.field_type

  try {
    // 处理选择类型字段
    if ((type === 'select' || type === 'radio') && fieldDef.options) {
      const option = fieldDef.options.find(opt => opt && opt.value == value)
      if (option && option.label) return option.label
    }

    // 处理多选字段
    if (type === 'checkbox' && Array.isArray(value) && fieldDef.options) {
      return value.map(v => {
        const option = fieldDef.options.find(opt => opt && opt.value == v)
        return option && option.label ? option.label : v
      }).join(', ')
    }

    // 处理日期字段
    if (type === 'date' && value) {
      try {
        return new Date(value).toLocaleDateString()
      } catch (e) {
        return String(value)
      }
    }

    // 处理日期时间字段
    if (type === 'datetime' && value) {
      try {
        return new Date(value).toLocaleString()
      } catch (e) {
        return String(value)
      }
    }

    // 处理开关字段
    if (type === 'switch') {
      return value === true ? '是' : '否'
    }

    return String(value)
  } catch (error) {
    console.error('格式化字段值时出错:', error)
    return String(value)
  }
}

// 基本格式化
const formatValue = (_, value) => {
  if (value === null || value === undefined) return '-'

  try {
    // 处理数组
    if (Array.isArray(value)) {
      return value.join(', ')
    }

    // 处理对象
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value)
      } catch (e) {
        return '[复杂对象]'
      }
    }

    return String(value)
  } catch (error) {
    console.error('基本格式化值时出错:', error)
    return '-'
  }
}

// 获取字段类型文本
const getFieldTypeText = (type) => {
  const typeMap = {
    'input': '文本输入',
    'textarea': '多行文本',
    'number': '数字输入',
    'select': '下拉选择',
    'radio': '单选框组',
    'checkbox': '复选框组',
    'date': '日期选择',
    'datetime': '日期时间',
    'switch': '开关',
    'upload': '文件上传'
  }

  return typeMap[type] || type
}

// 获取标签位置文本
const getLabelPositionText = (position) => {
  const positionMap = {
    'left': '左对齐',
    'right': '右对齐',
    'top': '顶部对齐'
  }

  return positionMap[position] || position || '-'
}

// 将字段键名格式化为更友好的标签
const formatKeyToLabel = (key) => {
  if (!key) return '-'

  // 处理特殊字段键名
  const specialFieldMap = {
    'test_field': '数字输入',
    'test_select': '下拉选择',
    'test_textarea': '单选框组',
    'field_1746972411639_246': '数字输入',
    'field_1746972421061_664': '下拉选择',
    'field_1746972422576_437': '单选框组'
  }

  // 如果是特殊字段，直接返回映射的标签
  if (specialFieldMap[key]) {
    return specialFieldMap[key]
  }

  // 移除前缀，如 field_1234567890_123
  let label = key
  if (key.startsWith('field_') && key.includes('_')) {
    const parts = key.split('_')
    if (parts.length >= 3) {
      // 尝试获取最后一部分作为标签
      label = parts.slice(2).join('_')
    }
  }

  // 将下划线替换为空格，并首字母大写
  return label
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}
</script>

<style scoped>
.form-data-display {
  margin: 20px 0;
}

.form-data-container {
  width: 100%;
}

.form-item {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  padding: 12px 0;
  line-height: 24px;
}

.form-label {
  width: 180px;
  color: #606266;
  font-weight: 500;
  text-align: right;
  padding-right: 12px;
  box-sizing: border-box;
}

.form-value {
  flex: 1;
  color: #303133;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
