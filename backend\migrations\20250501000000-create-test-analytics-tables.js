'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create test_runs table
    await queryInterface.createTable('test_runs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      test_run_id: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true
      },
      test_type: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      success: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      duration: {
        type: Sequelize.FLOAT,
        allowNull: true
      },
      test_stats: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      failed_tests: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      config: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Create test_results table
    await queryInterface.createTable('test_results', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      test_run_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'test_runs',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      file: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      title: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false
      },
      duration: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      error: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Create test_metrics table
    await queryInterface.createTable('test_metrics', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      test_run_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'test_runs',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      metric_type: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      timestamp: {
        type: Sequelize.DATE,
        allowNull: false
      },
      value: {
        type: Sequelize.FLOAT,
        allowNull: false
      },
      unit: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('test_runs', ['test_type']);
    await queryInterface.addIndex('test_runs', ['success']);
    await queryInterface.addIndex('test_runs', ['created_at']);
    await queryInterface.addIndex('test_results', ['test_run_id']);
    await queryInterface.addIndex('test_results', ['status']);
    await queryInterface.addIndex('test_metrics', ['test_run_id']);
    await queryInterface.addIndex('test_metrics', ['metric_type']);
    await queryInterface.addIndex('test_metrics', ['timestamp']);
  },

  async down(queryInterface, Sequelize) {
    // Drop tables in reverse order
    await queryInterface.dropTable('test_metrics');
    await queryInterface.dropTable('test_results');
    await queryInterface.dropTable('test_runs');
  }
};
