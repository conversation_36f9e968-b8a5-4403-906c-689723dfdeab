'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 修改 workflow_task_histories 表的 task_id 字段，允许为空
      await queryInterface.changeColumn('workflow_task_histories', 'task_id', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'workflow_tasks',
          key: 'id'
        }
      });
      console.log('Migration completed successfully');
      return Promise.resolve();
    } catch (error) {
      console.error('Migration failed:', error);
      return Promise.reject(error);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 恢复 workflow_task_histories 表的 task_id 字段，不允许为空
      await queryInterface.changeColumn('workflow_task_histories', 'task_id', {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_tasks',
          key: 'id'
        }
      });
      console.log('Rollback completed successfully');
      return Promise.resolve();
    } catch (error) {
      console.error('Rollback failed:', error);
      return Promise.reject(error);
    }
  }
};
