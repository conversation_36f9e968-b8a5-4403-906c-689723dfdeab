/**
 * 简单的请求和响应日志中间件
 */
const simpleLogger = (req, res, next) => {
  // 获取请求开始时间
  const startTime = Date.now();
  
  // 打印请求信息
  console.log('\n==============================');
  console.log('请求信息:');
  console.log(`${req.method} ${req.originalUrl || req.url}`);
  console.log('请求体:', req.body);
  console.log('查询参数:', req.query);
  console.log('路由参数:', req.params);
  
  // 保存原始的方法
  const originalSend = res.send;
  const originalJson = res.json;
  
  // 重写send方法
  res.send = function(body) {
    // 计算响应时间
    const responseTime = Date.now() - startTime;
    
    // 打印响应信息
    console.log('------------------------------');
    console.log('响应信息:');
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应时间: ${responseTime}ms`);
    console.log('响应数据:', body);
    console.log('==============================\n');
    
    // 调用原始方法
    return originalSend.call(this, body);
  };
  
  // 重写json方法
  res.json = function(body) {
    // 计算响应时间
    const responseTime = Date.now() - startTime;
    
    // 打印响应信息
    console.log('------------------------------');
    console.log('响应信息:');
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应时间: ${responseTime}ms`);
    console.log('响应数据:', body);
    console.log('==============================\n');
    
    // 调用原始方法
    return originalJson.call(this, body);
  };
  
  next();
};

module.exports = simpleLogger;
