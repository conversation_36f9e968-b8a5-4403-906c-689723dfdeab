# 工作流系统用户手册

## 1. 系统简介

工作流系统是一个基于Web的应用程序，用于设计、创建和管理工作流程。系统支持部门配置、表单设计、工作流设计、工作流填写和流转等核心功能，帮助组织实现业务流程的自动化和标准化。

### 1.1 系统特点

- **可视化设计**：通过拖拽式界面设计表单和工作流
- **灵活配置**：支持多种表单组件和工作流节点类型
- **权限控制**：基于角色的权限管理系统
- **版本管理**：表单和工作流支持版本控制
- **移动友好**：响应式设计，支持多种设备访问

### 1.2 系统架构

- **前端**：Vue 3, Element Plus
- **后端**：Node.js, Express
- **数据库**：PostgreSQL

## 2. 开始使用

### 2.1 系统登录

1. 打开浏览器，访问系统登录页面
2. 输入用户名和密码
3. 点击"登录"按钮

![登录界面](../assets/images/login.png)

### 2.2 系统界面

登录成功后，您将看到系统主界面，包括以下部分：

- **顶部导航栏**：显示当前用户信息和系统菜单
- **侧边菜单**：包含系统的主要功能模块
- **内容区域**：显示当前选中功能的详细内容
- **底部状态栏**：显示系统状态和版本信息

## 3. 部门配置

部门配置模块用于管理组织的部门结构和部门成员。

### 3.1 部门管理

#### 3.1.1 查看部门列表

1. 在侧边菜单中点击"部门管理"
2. 系统显示部门列表，包括部门名称、部门编码、上级部门、创建时间等信息

#### 3.1.2 创建部门

1. 在部门列表页面，点击"创建部门"按钮
2. 填写部门信息，包括部门名称、部门编码、上级部门等
3. 点击"保存"按钮完成创建

#### 3.1.3 编辑部门

1. 在部门列表中找到需要编辑的部门
2. 点击"编辑"按钮
3. 修改部门信息
4. 点击"保存"按钮完成编辑

#### 3.1.4 删除部门

1. 在部门列表中找到需要删除的部门
2. 点击"删除"按钮
3. 在确认对话框中点击"确定"完成删除

### 3.2 部门成员管理

#### 3.2.1 查看部门成员

1. 在部门列表中找到目标部门
2. 点击"成员管理"按钮
3. 系统显示该部门的成员列表

#### 3.2.2 添加部门成员

1. 在部门成员页面，点击"添加成员"按钮
2. 在弹出的用户列表中选择要添加的用户
3. 点击"确定"按钮完成添加

#### 3.2.3 移除部门成员

1. 在部门成员列表中找到需要移除的成员
2. 点击"移除"按钮
3. 在确认对话框中点击"确定"完成移除

## 4. 表单设计器

表单设计器模块用于创建和管理表单模板，支持多种表单组件和验证规则。

### 4.1 表单管理

#### 4.1.1 查看表单列表

1. 在侧边菜单中点击"表单设计器"
2. 系统显示表单列表，包括表单名称、描述、创建者、更新时间等信息

#### 4.1.2 创建表单

1. 在表单列表页面，点击"创建表单"按钮
2. 填写表单基本信息，包括表单名称、描述等
3. 点击"进入设计"按钮进入表单设计界面

### 4.2 表单设计

#### 4.2.1 添加表单组件

1. 在表单设计界面左侧的组件面板中，找到需要的组件
2. 将组件拖拽到中间的设计区域
3. 组件将被添加到表单中

#### 4.2.2 配置组件属性

1. 在设计区域中点击选中组件
2. 在右侧的属性面板中配置组件属性，如标签、占位符、默认值、验证规则等
3. 属性修改将实时反映在设计区域中

#### 4.2.3 组件排序和删除

- **排序**：拖拽组件可以调整其在表单中的位置
- **删除**：选中组件后，点击删除按钮或按键盘Delete键可以删除组件

#### 4.2.4 保存表单

1. 完成表单设计后，点击"保存"按钮
2. 系统将保存表单设计并返回表单列表页面

### 4.3 表单预览

1. 在表单列表中找到需要预览的表单
2. 点击"预览"按钮
3. 系统将显示表单的预览效果，可以测试表单的填写和验证功能

### 4.4 表单版本管理

#### 4.4.1 查看版本历史

1. 在表单列表中找到目标表单
2. 点击"版本"按钮
3. 系统显示该表单的版本历史列表

#### 4.4.2 预览历史版本

1. 在版本历史列表中找到需要预览的版本
2. 点击"预览"按钮
3. 系统显示该版本的表单预览

## 5. 工作流设计

工作流设计模块用于创建和管理工作流模板，定义业务流程的节点和流转规则。

### 5.1 工作流管理

#### 5.1.1 查看工作流列表

1. 在侧边菜单中点击"工作流设计"
2. 系统显示工作流列表，包括工作流名称、描述、关联表单、状态等信息

#### 5.1.2 创建工作流

1. 在工作流列表页面，点击"创建工作流"按钮
2. 填写工作流基本信息，包括工作流名称、描述、关联表单等
3. 点击"进入设计"按钮进入工作流设计界面

### 5.2 工作流设计

#### 5.2.1 添加节点

1. 在工作流设计界面左侧的节点面板中，找到需要的节点类型
2. 将节点拖拽到中间的设计区域
3. 节点将被添加到工作流中

#### 5.2.2 配置节点属性

1. 在设计区域中点击选中节点
2. 在右侧的属性面板中配置节点属性，如节点名称、处理人、期限等
3. 属性修改将实时反映在设计区域中

#### 5.2.3 创建节点连线

1. 鼠标悬停在节点上，会显示连线点
2. 从源节点的连线点拖拽到目标节点
3. 释放鼠标，创建连线
4. 点击连线可以配置连线条件

#### 5.2.4 保存工作流

1. 完成工作流设计后，点击"保存"按钮
2. 系统将保存工作流设计并返回工作流列表页面

### 5.3 工作流预览

1. 在工作流列表中找到需要预览的工作流
2. 点击"预览"按钮
3. 系统将显示工作流的预览效果，包括节点信息和流转路径

### 5.4 工作流版本管理

#### 5.4.1 查看版本历史

1. 在工作流列表中找到目标工作流
2. 点击"版本"按钮
3. 系统显示该工作流的版本历史列表

#### 5.4.2 预览历史版本

1. 在版本历史列表中找到需要预览的版本
2. 点击"预览"按钮
3. 系统显示该版本的工作流预览

### 5.5 发布工作流

1. 在工作流列表中找到需要发布的工作流
2. 点击"编辑"按钮
3. 在工作流属性中将状态修改为"已发布"
4. 点击"保存"按钮完成发布

## 6. 工作流填写

工作流填写模块用于发起工作流实例，填写表单数据。

### 6.1 发起工作流

#### 6.1.1 查看可发起的工作流

1. 在侧边菜单中点击"工作流填写"
2. 系统显示可发起的工作流列表，包括工作流名称、描述、关联表单等信息

#### 6.1.2 发起工作流

1. 在可发起的工作流列表中找到需要发起的工作流
2. 点击"发起"按钮
3. 系统显示工作流表单填写界面
4. 填写表单数据
5. 点击"提交"按钮完成发起

### 6.2 查看我发起的工作流

1. 在工作流填写页面，切换到"我发起的工作流"标签
2. 系统显示用户发起的工作流实例列表，包括工作流标题、类型、状态等信息

### 6.3 查看工作流实例详情

1. 在我发起的工作流列表中找到目标实例
2. 点击"查看"按钮
3. 系统显示工作流实例详情，包括基本信息、表单数据和流转记录

## 7. 工作流流转

工作流流转模块用于处理待办任务，推动工作流实例向前流转。

### 7.1 待办任务

#### 7.1.1 查看待办任务

1. 在侧边菜单中点击"工作流处理"
2. 系统显示待办任务列表，包括工作流标题、类型、当前节点、发起人、优先级等信息

#### 7.1.2 处理任务

1. 在待办任务列表中找到需要处理的任务
2. 点击"处理"按钮
3. 系统显示任务处理界面，包括工作流信息、表单数据和流转记录
4. 选择处理类型（同意、拒绝、退回、转交）
5. 填写处理意见
6. 点击"提交"按钮完成处理

### 7.2 已办任务

1. 在工作流处理页面，切换到"已办任务"标签
2. 系统显示用户已处理的任务列表，包括工作流标题、类型、节点名称、操作类型等信息

### 7.3 查看工作流实例详情

1. 在已办任务列表中找到目标实例
2. 点击"查看"按钮
3. 系统显示工作流实例详情，包括基本信息、表单数据和流转记录

## 8. 常见问题

### 8.1 无法登录系统

**问题**：输入用户名和密码后无法登录系统。

**解决方案**：
- 确认用户名和密码是否正确
- 检查账号是否被锁定或禁用
- 清除浏览器缓存后重试
- 联系系统管理员重置密码

### 8.2 无法看到某些功能模块

**问题**：登录后无法看到某些功能模块。

**解决方案**：
- 检查当前用户的权限设置
- 联系系统管理员获取相应权限

### 8.3 表单设计器组件拖拽不生效

**问题**：在表单设计器中拖拽组件不生效。

**解决方案**：
- 确认使用的是支持的浏览器（Chrome, Firefox, Edge等）
- 刷新页面后重试
- 清除浏览器缓存后重试

### 8.4 工作流节点连线创建失败

**问题**：在工作流设计器中无法创建节点连线。

**解决方案**：
- 确保源节点和目标节点都已正确放置在设计区域
- 从源节点的连线点拖拽到目标节点
- 刷新页面后重试

### 8.5 工作流实例卡在某个节点

**问题**：工作流实例长时间停留在某个节点，无法流转。

**解决方案**：
- 检查该节点的处理人是否已收到任务
- 确认节点的条件配置是否正确
- 联系系统管理员检查工作流配置

## 9. 附录

### 9.1 键盘快捷键

| 功能 | 快捷键 |
|------|--------|
| 保存 | Ctrl+S |
| 撤销 | Ctrl+Z |
| 重做 | Ctrl+Y |
| 删除选中项 | Delete |
| 全选 | Ctrl+A |
| 复制 | Ctrl+C |
| 粘贴 | Ctrl+V |
| 剪切 | Ctrl+X |

### 9.2 表单组件类型

| 组件类型 | 说明 |
|----------|------|
| 文本输入框 | 用于输入单行文本 |
| 文本域 | 用于输入多行文本 |
| 数字输入框 | 用于输入数字 |
| 单选框组 | 用于从多个选项中选择一个 |
| 复选框组 | 用于从多个选项中选择多个 |
| 下拉选择框 | 用于从下拉列表中选择一个选项 |
| 日期选择器 | 用于选择日期 |
| 时间选择器 | 用于选择时间 |
| 日期时间选择器 | 用于选择日期和时间 |
| 开关 | 用于表示开关状态 |
| 滑块 | 用于在范围内选择数值 |
| 评分 | 用于评分操作 |

### 9.3 工作流节点类型

| 节点类型 | 说明 |
|----------|------|
| 开始节点 | 工作流的起始节点 |
| 结束节点 | 工作流的结束节点 |
| 审批节点 | 需要审批操作的节点 |
| 任务节点 | 需要执行任务的节点 |
| 条件节点 | 根据条件决定流转路径的节点 |

### 9.4 工作流操作类型

| 操作类型 | 说明 |
|----------|------|
| 发起 | 创建工作流实例 |
| 同意 | 审批通过，流转到下一节点 |
| 拒绝 | 审批不通过，工作流结束 |
| 退回 | 退回到上一节点重新处理 |
| 转交 | 将任务转交给其他人处理 |
