const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     WorkflowInstance:
 *       type: object
 *       required:
 *         - workflow_template_id
 *         - initiator_id
 *         - title
 *       properties:
 *         id:
 *           type: integer
 *           description: 工作流实例ID
 *           example: 1
 *         workflow_template_id:
 *           type: integer
 *           description: 工作流模板ID
 *           example: 1
 *         initiator_id:
 *           type: integer
 *           description: 发起人ID
 *           example: 1
 *         title:
 *           type: string
 *           description: 工作流实例标题
 *           example: "张三的请假申请"
 *         status:
 *           type: string
 *           description: 工作流实例状态
 *           enum: [running, completed, terminated]
 *           example: "running"
 *         form_data:
 *           type: object
 *           description: 表单数据
 *           example: {
 *             "leave_type": "sick",
 *             "start_date": "2023-06-01",
 *             "end_date": "2023-06-03",
 *             "reason": "生病需要休息"
 *           }
 *         current_node_id:
 *           type: integer
 *           description: 当前节点ID
 *           example: 2
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class WorkflowInstance extends Model {
    static associate(models) {
      // 工作流模板
      WorkflowInstance.belongsTo(models.WorkflowTemplate, {
        foreignKey: 'workflow_template_id',
        as: 'workflowTemplate'
      });

      // 发起人
      WorkflowInstance.belongsTo(models.User, {
        foreignKey: 'initiator_id',
        as: 'initiator'
      });

      // 当前节点
      if (models.WorkflowNode) {
        WorkflowInstance.belongsTo(models.WorkflowNode, {
          foreignKey: 'current_node_id',
          as: 'currentNode'
        });
      }

      // 任务
      if (models.WorkflowTask) {
        WorkflowInstance.hasMany(models.WorkflowTask, {
          foreignKey: 'workflow_instance_id',
          as: 'tasks'
        });
      }

      // 任务历史
      if (models.WorkflowTaskHistory) {
        WorkflowInstance.hasMany(models.WorkflowTaskHistory, {
          foreignKey: 'workflow_instance_id',
          as: 'taskHistories'
        });
      }

      // 附件
      if (models.WorkflowAttachment) {
        WorkflowInstance.hasMany(models.WorkflowAttachment, {
          foreignKey: 'workflow_instance_id',
          as: 'attachments'
        });
      }
    }
  }

  WorkflowInstance.init({
    workflow_template_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_templates',
        key: 'id'
      }
    },
    initiator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'running'
    },
    form_data: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    current_node_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'workflow_nodes',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'WorkflowInstance',
    tableName: 'workflow_instances',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return WorkflowInstance;
};
