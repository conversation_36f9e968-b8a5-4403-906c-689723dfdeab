/**
 * Test FormTemplate model
 * Run with: node test-form-template.js
 */

const { FormTemplate, FormField } = require('./src/models');

async function testFormTemplate() {
  try {
    console.log('=== Testing FormTemplate Model ===');
    
    // Get all form templates
    console.log('\n1. Getting all form templates...');
    const formTemplates = await FormTemplate.findAll({
      limit: 5
    });
    
    console.log(`Found ${formTemplates.length} form templates`);
    
    if (formTemplates.length > 0) {
      const firstTemplate = formTemplates[0];
      console.log('\nFirst template:');
      console.log(`- ID: ${firstTemplate.id}`);
      console.log(`- Title: ${firstTemplate.title}`);
      console.log(`- Has schema: ${!!firstTemplate.schema}`);
      
      // Get template by ID
      console.log(`\n2. Getting form template with ID ${firstTemplate.id}...`);
      const template = await FormTemplate.findByPk(firstTemplate.id, {
        include: [
          {
            model: FormField,
            as: 'fields'
          }
        ]
      });
      
      if (template) {
        console.log('Found template:');
        console.log(`- ID: ${template.id}`);
        console.log(`- Title: ${template.title}`);
        console.log(`- Has schema: ${!!template.schema}`);
        
        if (template.schema) {
          console.log('- Schema type:', typeof template.schema);
          console.log('- Schema keys:', Object.keys(template.schema));
        }
        
        if (template.fields && template.fields.length > 0) {
          console.log(`- Fields count: ${template.fields.length}`);
          console.log('- First field:', template.fields[0].field_key, template.fields[0].label);
        } else {
          console.log('- No fields found');
        }
      } else {
        console.log(`Template with ID ${firstTemplate.id} not found`);
      }
      
      // Get template with ID 10 (the one we need for instance 81)
      console.log('\n3. Getting form template with ID 10...');
      const template10 = await FormTemplate.findByPk(10, {
        include: [
          {
            model: FormField,
            as: 'fields'
          }
        ]
      });
      
      if (template10) {
        console.log('Found template:');
        console.log(`- ID: ${template10.id}`);
        console.log(`- Title: ${template10.title}`);
        console.log(`- Has schema: ${!!template10.schema}`);
        
        if (template10.schema) {
          console.log('- Schema type:', typeof template10.schema);
          console.log('- Schema keys:', Object.keys(template10.schema));
        }
        
        if (template10.fields && template10.fields.length > 0) {
          console.log(`- Fields count: ${template10.fields.length}`);
          console.log('- Field keys:', template10.fields.map(f => f.field_key).join(', '));
        } else {
          console.log('- No fields found');
        }
      } else {
        console.log('Template with ID 10 not found');
      }
    }
    
    console.log('\n=== Test completed ===');
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run the test
testFormTemplate(); 