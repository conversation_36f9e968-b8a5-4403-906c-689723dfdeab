/**
 * API Tests for Workflow Endpoints
 */

const { test, expect } = require('@playwright/test');
const ApiTestClient = require('../../utils/api-test-client');
const TestDataManager = require('../../utils/test-data-manager');

test.describe('Workflow API Tests', () => {
  let apiClient;
  let testDataManager;
  let authToken;
  let formId;
  let workflowId;
  let instanceId;

  test.beforeAll(async () => {
    // Login to get auth token
    apiClient = new ApiTestClient();
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };

    const loginResponse = await apiClient.post('/users/login', loginData);
    authToken = loginResponse.data.data.token;
    apiClient.setAuthToken(authToken);
    
    // Create test data manager
    testDataManager = new TestDataManager({
      baseURL: apiClient.baseURL,
      authToken
    });
    
    // Create a form for workflow tests
    try {
      const form = await testDataManager.createForm({
        title: `API Test Form ${Date.now()}`
      });
      formId = form.id;
      console.log(`Created form with ID: ${formId}`);
    } catch (error) {
      console.error('Failed to create form:', error.message);
    }
  });

  test.afterAll(async () => {
    // Clean up test data
    await testDataManager.cleanup();
  });

  test('should get all workflows', async () => {
    const response = await apiClient.get('/workflows');
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify workflows data
    expect(Array.isArray(response.data.data)).toBe(true);
  });

  test('should create a new workflow', async () => {
    // Skip if no form was created
    test.skip(!formId, 'No form created');
    
    const workflowData = {
      title: `API Test Workflow ${Date.now()}`,
      description: 'Workflow created by API test',
      form_template_id: formId,
      nodes: [
        {
          id: 'start_node',
          type: 'start',
          name: 'Start',
          position: { x: 100, y: 100 }
        },
        {
          id: 'approval_node',
          type: 'approval',
          name: 'Approval',
          position: { x: 300, y: 100 },
          config: {
            approver_type: 'specific',
            approver_id: 1 // Admin user ID
          }
        },
        {
          id: 'end_node',
          type: 'end',
          name: 'End',
          position: { x: 500, y: 100 }
        }
      ],
      transitions: [
        {
          source: 'start_node',
          target: 'approval_node'
        },
        {
          source: 'approval_node',
          target: 'end_node'
        }
      ],
      status: 'published'
    };

    const response = await apiClient.post('/workflows', workflowData);
    
    // Verify response
    apiClient.verifyStatus(response, 201);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify workflow data
    expect(response.data.data.title).toBe(workflowData.title);
    expect(response.data.data.form_template_id).toBe(formId);
    
    // Save workflow ID for later tests
    workflowId = response.data.data.id;
  });

  test('should get workflow by ID', async () => {
    // Skip if no workflow was created
    test.skip(!workflowId, 'No workflow created');
    
    const response = await apiClient.get(`/workflows/${workflowId}`);
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify workflow data
    expect(response.data.data.id).toBe(workflowId);
  });

  test('should start a workflow instance', async () => {
    // Skip if no workflow was created
    test.skip(!workflowId, 'No workflow created');
    
    const instanceData = {
      title: `API Test Instance ${Date.now()}`,
      form_data: {
        test_text: 'API Test Value',
        test_textarea: 'API Test Description',
        test_select: 'option1'
      }
    };

    const response = await apiClient.post(`/workflows/${workflowId}/instances`, instanceData);
    
    // Verify response
    apiClient.verifyStatus(response, 201);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify instance data
    expect(response.data.data.title).toBe(instanceData.title);
    expect(response.data.data.workflow_template_id).toBe(workflowId);
    
    // Save instance ID for later tests
    instanceId = response.data.data.id;
  });

  test('should get workflow instance by ID', async () => {
    // Skip if no instance was created
    test.skip(!instanceId, 'No workflow instance created');
    
    const response = await apiClient.get(`/workflows/instances/${instanceId}`);
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify instance data
    expect(response.data.data.id).toBe(instanceId);
  });

  test('should get user todo tasks', async () => {
    const response = await apiClient.get('/workflows/tasks/todo');
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify tasks data
    expect(Array.isArray(response.data.data.tasks)).toBe(true);
  });

  test('should get user done tasks', async () => {
    const response = await apiClient.get('/workflows/tasks/done');
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify tasks data
    expect(Array.isArray(response.data.data.taskHistories)).toBe(true);
  });
});
