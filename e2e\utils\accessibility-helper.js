/**
 * Accessibility Testing Helper
 * Utility for running accessibility tests using <PERSON><PERSON> and Axe
 */

const { expect } = require('@playwright/test');
const AxeBuilder = require('@axe-core/playwright').default;

class AccessibilityHelper {
  /**
   * Run accessibility tests on the current page
   * @param {import('@playwright/test').Page} page - Playwright page
   * @param {object} options - Test options
   * @returns {Promise<object>} - Accessibility violations
   */
  static async runAccessibilityTests(page, options = {}) {
    const {
      includedImpacts = ['critical', 'serious'],
      excludeRules = [],
      scope = null
    } = options;

    // Create Axe builder
    let axeBuilder = new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
      .disableRules(excludeRules);

    // Filter by impact level
    if (includedImpacts.length > 0) {
      axeBuilder = axeBuilder.withOnlyImpacts(includedImpacts);
    }

    // Set scope if provided
    if (scope) {
      axeBuilder = axeBuilder.include(scope);
    }

    // Run analysis
    const results = await axeBuilder.analyze();
    return results;
  }

  /**
   * Verify no accessibility violations
   * @param {import('@playwright/test').Page} page - Playwright page
   * @param {object} options - Test options
   */
  static async expectNoViolations(page, options = {}) {
    const results = await this.runAccessibilityTests(page, options);
    
    // Format violations for better error messages
    if (results.violations.length > 0) {
      const formattedViolations = results.violations.map(violation => {
        return {
          id: violation.id,
          impact: violation.impact,
          description: violation.description,
          help: violation.help,
          helpUrl: violation.helpUrl,
          nodes: violation.nodes.map(node => ({
            html: node.html,
            failureSummary: node.failureSummary
          }))
        };
      });
      
      console.error('Accessibility violations found:', JSON.stringify(formattedViolations, null, 2));
    }
    
    expect(results.violations.length, `Found ${results.violations.length} accessibility violations`).toBe(0);
  }

  /**
   * Generate accessibility report
   * @param {import('@playwright/test').Page} page - Playwright page
   * @param {string} pageName - Name of the page being tested
   * @param {object} options - Test options
   * @returns {Promise<object>} - Accessibility report
   */
  static async generateAccessibilityReport(page, pageName, options = {}) {
    const results = await this.runAccessibilityTests(page, options);
    
    return {
      pageName,
      url: page.url(),
      timestamp: new Date().toISOString(),
      passes: results.passes.length,
      violations: results.violations.length,
      incomplete: results.incomplete.length,
      inapplicable: results.inapplicable.length,
      violationDetails: results.violations.map(violation => ({
        id: violation.id,
        impact: violation.impact,
        description: violation.description,
        help: violation.help,
        helpUrl: violation.helpUrl,
        nodeCount: violation.nodes.length
      }))
    };
  }
}

module.exports = AccessibilityHelper;
