<template>
  <div class="login-container">
    <div class="login-content">
      <div class="login-left">
        <div class="login-banner">
          <img src="../assets/logo.svg" alt="Logo" class="login-logo" />
          <h1 class="login-title">工作流系统</h1>
          <p class="login-subtitle">高效、灵活的业务流程管理平台</p>
          <div class="login-features">
            <div class="feature-item" v-for="(feature, index) in features" :key="index">
              <el-icon :class="feature.color"><component :is="feature.icon" /></el-icon>
              <span>{{ feature.text }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="login-right">
        <el-card class="login-card" shadow="never">
          <h2 class="form-title">欢迎登录</h2>
          <p class="form-subtitle">请输入您的账号和密码</p>

          <el-form
            :model="loginForm"
            :rules="rules"
            ref="loginFormRef"
            label-position="top"
            class="login-form"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="用户名"
                prefix-icon="User"
                size="large"
              ></el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="密码"
                prefix-icon="Lock"
                size="large"
                @keyup.enter="handleLogin"
              ></el-input>
            </el-form-item>
            <div class="form-options">
              <el-checkbox v-model="rememberMe">记住我</el-checkbox>
              <a href="#" class="forgot-password">忘记密码?</a>
            </div>
            <el-form-item>
              <el-button
                type="primary"
                :loading="loading"
                @click="handleLogin"
                class="login-button"
                size="large"
              >登录</el-button>
            </el-form-item>
          </el-form>

          <div class="demo-accounts">
            <p class="demo-title">演示账号</p>
            <div class="account-list">
              <div class="account-item" @click="fillDemoAccount('admin')">
                <span>管理员</span>
                <el-tag size="small" type="danger">admin / admin123</el-tag>
              </div>
              <div class="account-item" @click="fillDemoAccount('user')">
                <span>普通用户</span>
                <el-tag size="small" type="success">user / user123</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <div class="login-footer">
      <p>© 2025 工作流系统 | 版权所有</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const router = useRouter()
const loginFormRef = ref(null)
const loading = ref(false)
const rememberMe = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ]
}

// 登录页面特性
const features = [
  { text: '可视化表单设计', icon: 'Document', color: 'primary' },
  { text: '灵活工作流配置', icon: 'Connection', color: 'success' },
  { text: '高效审批流转', icon: 'Share', color: 'warning' },
  { text: '全面数据统计', icon: 'DataAnalysis', color: 'info' }
]

// 填充演示账号
const fillDemoAccount = (type) => {
  if (type === 'admin') {
    loginForm.username = 'admin'
    loginForm.password = 'admin123'
  } else if (type === 'user') {
    loginForm.username = 'user'
    loginForm.password = 'user123'
  }
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // 调用实际API
        const response = await axios.post('/api/users/login', {
          username: loginForm.username,
          password: loginForm.password
        })

        if (response.data.success) {
          // 保存token和用户信息
          // 注意：后端返回的token直接在response.data.token中，而不是response.data.data.token
          localStorage.setItem('token', response.data.token)
          localStorage.setItem('user', JSON.stringify(response.data.user))

          if (rememberMe.value) {
            localStorage.setItem('remember_username', loginForm.username)
          } else {
            localStorage.removeItem('remember_username')
          }

          // 设置axios默认headers
          axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`

          ElMessage.success('登录成功')
          router.push('/')
        } else {
          ElMessage.error(response.data.message || '登录失败')
        }
      } catch (error) {
        console.error('登录错误:', error)
        ElMessage.error(error.response?.data?.message || '登录失败，请检查网络连接')
      } finally {
        loading.value = false
      }
    }
  })
}

// 检查是否有记住的用户名
const checkRememberedUser = () => {
  const rememberedUsername = localStorage.getItem('remember_username')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    rememberMe.value = true
  }
}

// 页面加载时检查
checkRememberedUser()
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  position: absolute;
  top: 0;
  left: 0;
}

.login-content {
  display: flex;
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.login-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-banner {
  max-width: 500px;
  text-align: center;
}

.login-logo {
  width: 100px;
  height: 100px;
  margin-bottom: 24px;
}

.login-title {
  font-size: 36px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 16px;
}

.login-subtitle {
  font-size: 18px;
  color: var(--text-color-secondary);
  margin-bottom: 40px;
}

.login-features {
  display: flex;
  flex-direction: column;
  gap: 16px;
  text-align: left;
  margin-top: 40px;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.feature-item .el-icon {
  font-size: 24px;
  margin-right: 12px;
}

.primary {
  color: var(--primary-color);
}

.success {
  color: var(--success-color);
}

.warning {
  color: var(--warning-color);
}

.info {
  color: var(--info-color);
}

.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-card {
  width: 400px;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
  padding: 20px;
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 8px;
  color: var(--text-color);
}

.form-subtitle {
  font-size: 14px;
  text-align: center;
  margin-bottom: 32px;
  color: var(--text-color-secondary);
}

.login-form {
  margin-bottom: 24px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.forgot-password {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  border-radius: 4px;
}

.demo-accounts {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px dashed var(--border-color);
}

.demo-title {
  font-size: 14px;
  color: var(--text-color-secondary);
  margin-bottom: 12px;
  text-align: center;
}

.account-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--background-color-light);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.account-item:hover {
  background-color: var(--background-color-dark);
}

.login-footer {
  text-align: center;
  padding: 16px;
  color: var(--text-color-secondary);
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .login-content {
    flex-direction: column;
    padding: 20px;
  }

  .login-left, .login-right {
    padding: 20px 0;
  }

  .login-banner {
    max-width: 100%;
  }

  .login-features {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 24px;
  }

  .feature-item {
    width: 45%;
  }
}

@media (max-width: 576px) {
  .login-card {
    width: 100%;
  }

  .feature-item {
    width: 100%;
  }
}
</style>
