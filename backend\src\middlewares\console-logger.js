/**
 * 控制台日志中间件
 * 直接打印请求和响应信息到控制台
 */
const consoleLogger = (req, res, next) => {
  // 获取请求开始时间
  const startTime = Date.now();
  
  // 打印请求信息
  console.log('\n==============================');
  console.log('请求信息:');
  console.log(`${req.method} ${req.originalUrl || req.url}`);
  console.log('请求体:');
  console.log(req.body);
  
  // 保存原始的end方法
  const originalEnd = res.end;
  
  // 重写end方法
  res.end = function(chunk, encoding) {
    // 计算响应时间
    const responseTime = Date.now() - startTime;
    
    // 打印响应信息
    console.log('------------------------------');
    console.log('响应信息:');
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应时间: ${responseTime}ms`);
    
    // 如果是JSON响应，尝试解析并打印
    const contentType = res.getHeader('content-type');
    if (contentType && contentType.includes('application/json') && chunk) {
      try {
        const body = JSON.parse(chunk.toString());
        console.log('响应数据:');
        console.log(body);
      } catch (e) {
        console.log('响应数据: (无法解析JSON)');
        console.log(chunk && chunk.toString());
      }
    }
    
    console.log('==============================\n');
    
    // 调用原始方法
    return originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

module.exports = consoleLogger;
