/**
 * Test script for the special form data with schema endpoint
 * Run with: node test-special-endpoint.js
 */

const axios = require('axios');

// Configuration
const API_URL = 'http://localhost:3001/api';

// Test function
async function testSpecialEndpoint() {
  try {
    console.log('=== Testing Special Form Data Endpoint ===');
    
    // Step 1: Login to get token
    console.log('\n1. Logging in to get auth token...');
    const loginResponse = await axios.post(`${API_URL}/users/login`, {
      username: 'admin',
      password: 'admin123'
    });
    
    if (!loginResponse.data.success) {
      console.error('Login failed:', loginResponse.data);
      return;
    }
    
    // Extract token from the response
    const token = loginResponse.data.token || (loginResponse.data.data && loginResponse.data.data.token);
    if (!token) {
      console.error('No token found in response:', loginResponse.data);
      return;
    }
    
    console.log('Login successful, token received');
    
    // Step 2: Get the special form data endpoint
    console.log(`\n2. Getting special form data endpoint...`);
    
    const response = await axios.get(
      `${API_URL}/workflows/instances/81/form-data-with-schema`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    if (!response.data.success) {
      console.error('Failed to get special form data:', response.data);
      return;
    }
    
    console.log('Received special form data successfully');
    
    // Step 3: Verify the response structure
    const data = response.data.data;
    
    console.log('\n3. Verifying response structure:');
    console.log('Data properties:', Object.keys(data));
    
    // Check form_data
    console.log('\nForm Data details:');
    if (data.form_data) {
      console.log('form_data type:', typeof data.form_data);
      if (typeof data.form_data === 'object') {
        console.log('form_data has values:', !!data.form_data.values);
        console.log('form_data has schema:', !!data.form_data.schema);
        
        if (data.form_data.values) {
          console.log('form_data.values keys:', Object.keys(data.form_data.values));
        }
        
        if (data.form_data.schema) {
          console.log('form_data.schema keys:', Object.keys(data.form_data.schema));
        }
      }
    } else {
      console.log('No form_data in response');
    }
    
    // Check form_schema
    console.log('\nSchema details:');
    console.log('- Has form_schema:', !!data.form_schema);
    if (data.form_schema) {
      console.log('- form_schema keys:', Object.keys(data.form_schema));
      if (data.form_schema.fields) {
        console.log('- Fields count:', data.form_schema.fields.length);
        console.log('- Field keys:', data.form_schema.fields.map(f => f.field_key).join(', '));
      }
    }
    
    console.log('\n=== Test completed successfully ===');
  } catch (error) {
    console.error('Test error:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
}

// Run the test
testSpecialEndpoint(); 