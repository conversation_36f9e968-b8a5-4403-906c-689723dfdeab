/**
 * 基础日志中间件
 * 直接使用console.log打印请求和响应信息
 */
const basicLogger = (req, res, next) => {
  // 记录请求开始时间
  const start = Date.now();
  
  // 打印请求信息
  console.log(`\n>>> 请求: ${req.method} ${req.url}`);
  console.log(`>>> 请求体: ${JSON.stringify(req.body)}`);
  
  // 保存原始的end方法
  const originalEnd = res.end;
  
  // 重写end方法
  res.end = function(chunk, encoding) {
    // 计算请求处理时间
    const duration = Date.now() - start;
    
    // 打印响应信息
    console.log(`<<< 响应: 状态码 ${res.statusCode} (${duration}ms)`);
    
    // 如果有响应体，尝试打印
    if (chunk) {
      try {
        const body = chunk.toString();
        console.log(`<<< 响应体: ${body}`);
      } catch (e) {
        console.log('<<< 响应体: [无法解析]');
      }
    }
    
    // 调用原始方法
    return originalEnd.apply(this, arguments);
  };
  
  next();
};

module.exports = basicLogger;
