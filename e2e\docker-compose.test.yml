version: '3.8'

services:
  # PostgreSQL database
  postgres:
    image: postgres:14
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: workflow_test
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Backend service
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=test
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_NAME=workflow_test
      - PORT=5273
    ports:
      - "5273:5273"
    depends_on:
      postgres:
        condition: service_healthy
    command: npm run start:test
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5273/api/health"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Frontend service
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=test
      - API_URL=http://backend:5273/api
    ports:
      - "8080:8080"
    depends_on:
      backend:
        condition: service_healthy
    command: npm run serve

  # E2E tests
  e2e:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - BASE_URL=http://frontend:8080
      - API_URL=http://backend:5273/api
      - CI=true
    volumes:
      - ./test-results:/app/test-results
      - ./playwright-report:/app/playwright-report
      - ./ci-reports:/app/ci-reports
    depends_on:
      backend:
        condition: service_healthy
      frontend:
        condition: service_started
    command: >
      sh -c "
        echo 'Waiting for frontend to be ready...' &&
        npx wait-on http://frontend:8080 -t 60000 &&
        echo 'Running tests...' &&
        node ci-test.js --smoke --parallel=2 --retries=1 &&
        node analyze-results.js --html --json
      "

volumes:
  postgres_data:
