const { test, expect } = require('@playwright/test');
const { login, TEST_USERS } = require('../utils/test-helpers');

test.describe('User Permissions Tests', () => {
  test('admin user should have access to all modules', async ({ page }) => {
    // Login as admin
    await login(page, 'admin');
    
    // Navigate to home page
    await page.goto('/');
    
    // Verify all modules are accessible
    const modules = ['部门配置', '表单设计器', '工作流设计', '工作流填写', '工作流流转'];
    for (const moduleName of modules) {
      // Find the module card
      const moduleCard = page.locator('.module-card').filter({ hasText: moduleName });
      
      // Verify module card is visible
      await expect(moduleCard).toBeVisible();
      
      // Click on the module
      await moduleCard.locator('button').click();
      
      // Verify navigation to the module page
      await page.waitForLoadState('networkidle');
      
      // Go back to home page
      await page.goto('/');
    }
  });

  test('regular user should have limited access to modules', async ({ page }) => {
    // Login as regular user
    await login(page, 'user');
    
    // Navigate to home page
    await page.goto('/');
    
    // Verify user can access workflow form and process modules
    const accessibleModules = ['工作流填写', '工作流流转'];
    for (const moduleName of accessibleModules) {
      // Find the module card
      const moduleCard = page.locator('.module-card').filter({ hasText: moduleName });
      
      // Verify module card is visible
      await expect(moduleCard).toBeVisible();
      
      // Click on the module
      await moduleCard.locator('button').click();
      
      // Verify navigation to the module page
      await page.waitForLoadState('networkidle');
      
      // Go back to home page
      await page.goto('/');
    }
    
    // Try to access admin-only modules by direct URL
    const restrictedModules = [
      { path: '/department', title: '部门管理' },
      { path: '/form-design', title: '表单设计' },
      { path: '/workflow-design', title: '工作流设计' }
    ];
    
    for (const module of restrictedModules) {
      // Try to navigate directly to the restricted module
      await page.goto(module.path);
      
      // Verify user is redirected or shown access denied
      // This could be either a redirect to home page or an error message
      try {
        // Check if redirected to home page
        const currentUrl = page.url();
        if (currentUrl.endsWith('/')) {
          // Successfully redirected to home
          continue;
        }
        
        // Check if error message is shown
        const errorMessage = await page.locator('.access-denied-message, .el-message--error').isVisible();
        expect(errorMessage).toBeTruthy();
      } catch (error) {
        // If neither condition is met, the test should fail
        expect(false).toBeTruthy('User should not have access to ' + module.path);
      }
    }
  });

  test('should show different UI elements based on user role', async ({ page }) => {
    // First check admin view
    await login(page, 'admin');
    await page.goto('/department');
    
    // Admin should see "Add Department" button
    await expect(page.locator('button:has-text("添加部门")')).toBeVisible();
    
    // Logout
    await page.goto('/login');
    
    // Login as regular user
    await login(page, 'user');
    
    // Try to access department page (if accessible)
    await page.goto('/department');
    
    // If page is accessible, "Add Department" button should not be visible
    const currentUrl = page.url();
    if (!currentUrl.includes('login')) {
      await expect(page.locator('button:has-text("添加部门")')).not.toBeVisible();
    }
  });
});
