const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     Department:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: integer
 *           description: 部门ID
 *           example: 1
 *         name:
 *           type: string
 *           description: 部门名称
 *           example: "研发部"
 *         code:
 *           type: string
 *           description: 部门编码，唯一标识
 *           example: "RD001"
 *         parent_id:
 *           type: integer
 *           description: 父部门ID，如果是顶级部门则为null
 *           example: null
 *         manager_id:
 *           type: integer
 *           description: 部门经理ID
 *           example: 1
 *         description:
 *           type: string
 *           description: 部门描述
 *           example: "负责产品研发和技术支持"
 *         status:
 *           type: string
 *           description: 部门状态
 *           enum: [active, inactive]
 *           example: "active"
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 *       example:
 *         id: 1
 *         name: "研发部"
 *         code: "RD001"
 *         parent_id: null
 *         manager_id: 1
 *         description: "负责产品研发和技术支持"
 *         status: "active"
 *         created_at: "2023-06-01T10:00:00Z"
 *         updated_at: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class Department extends Model {
    static associate(models) {
      // 自关联 - 父部门
      Department.belongsTo(models.Department, {
        foreignKey: 'parent_id',
        as: 'parent'
      });

      // 自关联 - 子部门
      Department.hasMany(models.Department, {
        foreignKey: 'parent_id',
        as: 'children'
      });

      // 部门经理
      Department.belongsTo(models.User, {
        foreignKey: 'manager_id',
        as: 'manager'
      });

      // 部门成员
      Department.belongsToMany(models.User, {
        through: 'department_users',
        foreignKey: 'department_id',
        otherKey: 'user_id',
        as: 'members'
      });
    }
  }

  Department.init({
    name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    code: {
      type: DataTypes.STRING(50),
      unique: true,
      allowNull: true
    },
    parent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'departments',
        key: 'id'
      }
    },
    manager_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'active'
    }
  }, {
    sequelize,
    modelName: 'Department',
    tableName: 'departments',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return Department;
};
