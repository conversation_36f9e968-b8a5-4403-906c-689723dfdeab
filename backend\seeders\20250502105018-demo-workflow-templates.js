'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // 获取管理员用户ID和表单模板ID
    const users = await queryInterface.sequelize.query(
      `SELECT id, username FROM users WHERE username = 'admin';`,
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const formTemplates = await queryInterface.sequelize.query(
      `SELECT id, title FROM form_templates;`,
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const adminId = users[0]?.id;
    const leaveFormId = formTemplates.find(template => template.title === '请假申请表')?.id;
    const expenseFormId = formTemplates.find(template => template.title === '报销申请表')?.id;

    if (adminId && leaveFormId) {
      // 创建请假工作流模板
      await queryInterface.bulkInsert('workflow_templates', [
        {
          title: '请假审批流程',
          description: '员工请假审批流程',
          form_template_id: leaveFormId,
          creator_id: adminId,
          version: 1,
          status: 'published',
          schema: JSON.stringify({
            nodes: [
              {
                id: 'start',
                type: 'start',
                name: '开始',
                position: { x: 100, y: 100 }
              },
              {
                id: 'fillForm',
                type: 'task',
                name: '填写请假表单',
                position: { x: 100, y: 200 },
                assignee: 'initiator'
              },
              {
                id: 'managerApproval',
                type: 'approval',
                name: '部门经理审批',
                position: { x: 100, y: 300 },
                assignee: 'department_manager'
              },
              {
                id: 'hrApproval',
                type: 'approval',
                name: '人事审批',
                position: { x: 100, y: 400 },
                assignee: 'department:HR'
              },
              {
                id: 'end',
                type: 'end',
                name: '结束',
                position: { x: 100, y: 500 }
              }
            ],
            edges: [
              { source: 'start', target: 'fillForm' },
              { source: 'fillForm', target: 'managerApproval' },
              { source: 'managerApproval', target: 'hrApproval', condition: 'approved' },
              { source: 'managerApproval', target: 'end', condition: 'rejected' },
              { source: 'hrApproval', target: 'end' }
            ]
          }),
          created_at: new Date(),
          updated_at: new Date()
        }
      ], {});
    }

    if (adminId && expenseFormId) {
      // 创建报销工作流模板
      await queryInterface.bulkInsert('workflow_templates', [
        {
          title: '报销审批流程',
          description: '员工报销审批流程',
          form_template_id: expenseFormId,
          creator_id: adminId,
          version: 1,
          status: 'published',
          schema: JSON.stringify({
            nodes: [
              {
                id: 'start',
                type: 'start',
                name: '开始',
                position: { x: 100, y: 100 }
              },
              {
                id: 'fillForm',
                type: 'task',
                name: '填写报销表单',
                position: { x: 100, y: 200 },
                assignee: 'initiator'
              },
              {
                id: 'managerApproval',
                type: 'approval',
                name: '部门经理审批',
                position: { x: 100, y: 300 },
                assignee: 'department_manager'
              },
              {
                id: 'financeApproval',
                type: 'approval',
                name: '财务审批',
                position: { x: 100, y: 400 },
                assignee: 'role:finance'
              },
              {
                id: 'end',
                type: 'end',
                name: '结束',
                position: { x: 100, y: 500 }
              }
            ],
            edges: [
              { source: 'start', target: 'fillForm' },
              { source: 'fillForm', target: 'managerApproval' },
              { source: 'managerApproval', target: 'financeApproval', condition: 'approved' },
              { source: 'managerApproval', target: 'end', condition: 'rejected' },
              { source: 'financeApproval', target: 'end' }
            ]
          }),
          created_at: new Date(),
          updated_at: new Date()
        }
      ], {});
    }
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.bulkDelete('workflow_templates', null, {});
  }
};
