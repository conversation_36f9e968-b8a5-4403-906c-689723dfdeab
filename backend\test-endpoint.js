/**
 * Test script for workflow instance with form schema
 * Run with: node test-endpoint.js
 */

const axios = require('axios');
const { WorkflowTemplate, FormTemplate } = require('./src/models');

// Configuration
const API_URL = 'http://localhost:3001/api';
const INSTANCE_ID = 81;

// Test function
async function testWorkflowInstanceEndpoint() {
  try {
    console.log('=== Testing Workflow Instance Endpoint ===');
    
    // Get database info first for reference
    console.log('\n0. Checking database records directly...');
    try {
      // Get workflow with ID 8 (the one associated with instance 81)
      const workflowTemplate = await WorkflowTemplate.findByPk(8);
      if (workflowTemplate) {
        console.log(`Workflow template 8 has form_template_id: ${workflowTemplate.form_template_id}`);
        
        // Get form template
        const formTemplate = await FormTemplate.findByPk(workflowTemplate.form_template_id);
        if (formTemplate) {
          console.log(`Form template ${formTemplate.id} exists with title: ${formTemplate.title}`);
          console.log(`Form schema exists: ${!!formTemplate.schema}`);
        } else {
          console.log(`Form template ${workflowTemplate.form_template_id} does not exist in database`);
        }
      } else {
        console.log('Workflow template 8 not found in database');
      }
    } catch (dbError) {
      console.error('Database check error:', dbError);
    }
    
    // Step 1: Login to get token
    console.log('\n1. Logging in to get auth token...');
    const loginResponse = await axios.post(`${API_URL}/users/login`, {
      username: 'admin',
      password: 'admin123'
    });
    
    if (!loginResponse.data.success) {
      console.error('Login failed:', loginResponse.data);
      return;
    }
    
    // Extract token from the response
    const token = loginResponse.data.token || (loginResponse.data.data && loginResponse.data.data.token);
    if (!token) {
      console.error('No token found in response:', loginResponse.data);
      return;
    }
    
    console.log('Login successful, token received');
    
    // Step 2: Get the workflow instance
    console.log(`\n2. Getting workflow instance ${INSTANCE_ID}...`);
    
    const instanceResponse = await axios.get(
      `${API_URL}/workflows/instances/${INSTANCE_ID}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    if (!instanceResponse.data.success) {
      console.error('Failed to get workflow instance:', instanceResponse.data);
      return;
    }
    
    console.log('Received workflow instance successfully');
    
    // Step 3: Verify the response structure
    const instance = instanceResponse.data.data;
    
    console.log('\n3. Verifying response structure:');
    console.log('Instance properties:', Object.keys(instance));
    
    // Special debug check for form_data structure
    console.log('\nForm Data details:');
    if (instance.form_data) {
      console.log('form_data type:', typeof instance.form_data);
      if (typeof instance.form_data === 'object') {
        console.log('form_data has values:', !!instance.form_data.values);
        console.log('form_data has schema:', !!instance.form_data.schema);
        
        if (instance.form_data.values) {
          console.log('form_data.values:', JSON.stringify(instance.form_data.values, null, 2));
        } else {
          console.log('direct form_data:', JSON.stringify(instance.form_data, null, 2));
        }
      }
    } else {
      console.log('No form_data in response');
    }
    
    console.log('\nSchema details:');
    console.log('- Has form_schema:', !!instance.form_schema);
    if (instance.form_schema) {
      console.log('- form_schema keys:', Object.keys(instance.form_schema));
    }
    
    // Check for workflowTemplate and formTemplate
    console.log('- Has workflowTemplate:', !!instance.workflowTemplate);
    if (instance.workflowTemplate) {
      console.log(`  - ID: ${instance.workflowTemplate.id}`);
      console.log(`  - form_template_id: ${instance.workflowTemplate.form_template_id}`);
      console.log('  - Has formTemplate:', !!instance.workflowTemplate.formTemplate);
      if (instance.workflowTemplate.formTemplate) {
        console.log('    - Has schema:', !!instance.workflowTemplate.formTemplate.schema);
      }
    }
    
    // Display form data
    if (instance.form_data) {
      console.log('\nForm Data structure:');
      console.log(JSON.stringify(instance.form_data, null, 2));
    }
    
    console.log('\n=== Test completed successfully ===');
  } catch (error) {
    console.error('Test error:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
}

// Run the test
testWorkflowInstanceEndpoint(); 