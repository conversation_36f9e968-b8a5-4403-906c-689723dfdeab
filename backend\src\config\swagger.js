const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: '工作流系统 API',
      version: '1.0.0',
      description: '工作流系统的RESTful API文档，提供表单设计、工作流设计和流转等功能',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: '/api',
        description: 'API服务器'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: '使用JWT令牌进行身份验证。在开发环境中，身份验证会被自动跳过。'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              example: '错误信息'
            },
            error: {
              type: 'string',
              example: '详细错误信息'
            }
          }
        },
        SuccessResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            message: {
              type: 'string',
              example: '操作成功'
            },
            data: {
              type: 'object',
              example: {}
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ],
    tags: [
      {
        name: 'Forms',
        description: '表单管理API，用于创建、查询、更新和删除表单模板'
      },
      {
        name: 'Workflows',
        description: '工作流管理API，用于创建、查询、更新和删除工作流模板，以及启动和处理工作流实例'
      },
      {
        name: 'Departments',
        description: '部门管理API，用于创建、查询、更新和删除部门信息'
      },
      {
        name: 'Users',
        description: '用户管理API，用于用户注册、登录和查询用户信息'
      },
      {
        name: '测试分析',
        description: '测试分析API，用于查询和分析测试结果数据'
      }
    ]
  },
  apis: [
    './src/routes/*.js',
    './src/models/*.js',
    './src/controllers/*.js'
  ]
};

const specs = swaggerJsdoc(options);

module.exports = specs;
