/**
 * Test Data Manager
 * Utility for managing test data creation and cleanup
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class TestDataManager {
  constructor(config = {}) {
    this.baseURL = config.baseURL || 'http://localhost:5273/api';
    this.authToken = config.authToken || '';
    this.createdData = {
      departments: [],
      forms: [],
      workflows: [],
      instances: [],
      users: []
    };
    this.dataFile = config.dataFile || path.join(__dirname, '../test-data.json');
    
    // Load previously created data if exists
    this.loadCreatedData();
  }

  /**
   * Set auth token for API requests
   * @param {string} token - JWT token
   */
  setAuthToken(token) {
    this.authToken = token;
  }

  /**
   * Make an authenticated API request
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {object} data - Request data
   * @returns {Promise<object>} - Response data
   */
  async makeRequest(method, endpoint, data = null) {
    try {
      const config = {
        method,
        url: `${this.baseURL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      if (this.authToken) {
        config.headers['Authorization'] = `Bearer ${this.authToken}`;
      }

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error(`API Request failed: ${method} ${endpoint}`);
      if (error.response) {
        console.error(`Status: ${error.response.status}`);
        console.error(`Data: ${JSON.stringify(error.response.data)}`);
      } else {
        console.error(error.message);
      }
      throw error;
    }
  }

  /**
   * Create a department for testing
   * @param {object} departmentData - Department data
   * @returns {Promise<object>} - Created department
   */
  async createDepartment(departmentData = {}) {
    const defaultData = {
      name: `Test Department ${Date.now()}`,
      code: `TEST${Date.now()}`,
      description: 'Test department created for E2E testing',
      status: 'active'
    };

    const data = { ...defaultData, ...departmentData };
    
    try {
      const response = await this.makeRequest('post', '/departments', data);
      if (response.success && response.data) {
        this.createdData.departments.push(response.data);
        this.saveCreatedData();
        return response.data;
      }
      throw new Error('Failed to create department');
    } catch (error) {
      console.error('Create department failed:', error.message);
      throw error;
    }
  }

  /**
   * Create a form template for testing
   * @param {object} formData - Form data
   * @returns {Promise<object>} - Created form
   */
  async createForm(formData = {}) {
    const defaultData = {
      title: `Test Form ${Date.now()}`,
      description: 'Test form created for E2E testing',
      fields: [
        {
          type: 'input',
          label: 'Test Text Field',
          name: 'test_text',
          required: true,
          placeholder: 'Enter text'
        },
        {
          type: 'textarea',
          label: 'Test Textarea',
          name: 'test_textarea',
          required: false,
          placeholder: 'Enter description'
        },
        {
          type: 'select',
          label: 'Test Select',
          name: 'test_select',
          required: true,
          options: [
            { label: 'Option 1', value: 'option1' },
            { label: 'Option 2', value: 'option2' }
          ]
        }
      ],
      status: 'published'
    };

    const data = { ...defaultData, ...formData };
    
    try {
      const response = await this.makeRequest('post', '/forms', data);
      if (response.success && response.data) {
        this.createdData.forms.push(response.data);
        this.saveCreatedData();
        return response.data;
      }
      throw new Error('Failed to create form');
    } catch (error) {
      console.error('Create form failed:', error.message);
      throw error;
    }
  }

  /**
   * Create a workflow template for testing
   * @param {object} workflowData - Workflow data
   * @returns {Promise<object>} - Created workflow
   */
  async createWorkflow(workflowData = {}) {
    // Create a form first if no form_template_id provided
    let formTemplateId = workflowData.form_template_id;
    if (!formTemplateId) {
      try {
        const form = await this.createForm();
        formTemplateId = form.id;
      } catch (error) {
        console.error('Failed to create form for workflow:', error.message);
        throw error;
      }
    }

    const defaultData = {
      title: `Test Workflow ${Date.now()}`,
      description: 'Test workflow created for E2E testing',
      form_template_id: formTemplateId,
      nodes: [
        {
          id: 'start_node',
          type: 'start',
          name: 'Start',
          position: { x: 100, y: 100 }
        },
        {
          id: 'approval_node',
          type: 'approval',
          name: 'Approval',
          position: { x: 300, y: 100 },
          config: {
            approver_type: 'specific',
            approver_id: 1 // Admin user ID
          }
        },
        {
          id: 'end_node',
          type: 'end',
          name: 'End',
          position: { x: 500, y: 100 }
        }
      ],
      transitions: [
        {
          source: 'start_node',
          target: 'approval_node'
        },
        {
          source: 'approval_node',
          target: 'end_node'
        }
      ],
      status: 'published'
    };

    const data = { ...defaultData, ...workflowData };
    
    try {
      const response = await this.makeRequest('post', '/workflows', data);
      if (response.success && response.data) {
        this.createdData.workflows.push(response.data);
        this.saveCreatedData();
        return response.data;
      }
      throw new Error('Failed to create workflow');
    } catch (error) {
      console.error('Create workflow failed:', error.message);
      throw error;
    }
  }

  /**
   * Start a workflow instance for testing
   * @param {number} workflowId - Workflow template ID
   * @param {object} instanceData - Instance data
   * @returns {Promise<object>} - Created instance
   */
  async startWorkflowInstance(workflowId, instanceData = {}) {
    // Create a workflow first if no workflowId provided
    if (!workflowId) {
      try {
        const workflow = await this.createWorkflow();
        workflowId = workflow.id;
      } catch (error) {
        console.error('Failed to create workflow for instance:', error.message);
        throw error;
      }
    }

    const defaultData = {
      title: `Test Instance ${Date.now()}`,
      form_data: {
        test_text: 'Test value',
        test_textarea: 'Test description',
        test_select: 'option1'
      }
    };

    const data = { ...defaultData, ...instanceData };
    
    try {
      const response = await this.makeRequest('post', `/workflows/${workflowId}/instances`, data);
      if (response.success && response.data) {
        this.createdData.instances.push(response.data);
        this.saveCreatedData();
        return response.data;
      }
      throw new Error('Failed to start workflow instance');
    } catch (error) {
      console.error('Start workflow instance failed:', error.message);
      throw error;
    }
  }

  /**
   * Create a test user
   * @param {object} userData - User data
   * @returns {Promise<object>} - Created user
   */
  async createUser(userData = {}) {
    const defaultData = {
      username: `testuser_${Date.now()}`,
      password: 'Test123!',
      full_name: 'Test User',
      email: `testuser_${Date.now()}@example.com`,
      roles: ['user']
    };

    const data = { ...defaultData, ...userData };
    
    try {
      const response = await this.makeRequest('post', '/users/register', data);
      if (response.success && response.data) {
        this.createdData.users.push(response.data);
        this.saveCreatedData();
        return response.data;
      }
      throw new Error('Failed to create user');
    } catch (error) {
      console.error('Create user failed:', error.message);
      throw error;
    }
  }

  /**
   * Clean up created test data
   * @returns {Promise<void>}
   */
  async cleanup() {
    // Clean up in reverse order of creation to handle dependencies
    console.log('Cleaning up test data...');

    // Clean up instances
    for (const instance of this.createdData.instances) {
      try {
        await this.makeRequest('delete', `/workflows/instances/${instance.id}`);
        console.log(`Deleted instance: ${instance.id}`);
      } catch (error) {
        console.error(`Failed to delete instance ${instance.id}:`, error.message);
      }
    }

    // Clean up workflows
    for (const workflow of this.createdData.workflows) {
      try {
        await this.makeRequest('delete', `/workflows/${workflow.id}`);
        console.log(`Deleted workflow: ${workflow.id}`);
      } catch (error) {
        console.error(`Failed to delete workflow ${workflow.id}:`, error.message);
      }
    }

    // Clean up forms
    for (const form of this.createdData.forms) {
      try {
        await this.makeRequest('delete', `/forms/${form.id}`);
        console.log(`Deleted form: ${form.id}`);
      } catch (error) {
        console.error(`Failed to delete form ${form.id}:`, error.message);
      }
    }

    // Clean up users (except admin and default user)
    for (const user of this.createdData.users) {
      if (user.username !== 'admin' && user.username !== 'user') {
        try {
          await this.makeRequest('delete', `/users/${user.id}`);
          console.log(`Deleted user: ${user.id}`);
        } catch (error) {
          console.error(`Failed to delete user ${user.id}:`, error.message);
        }
      }
    }

    // Clean up departments
    for (const department of this.createdData.departments) {
      try {
        await this.makeRequest('delete', `/departments/${department.id}`);
        console.log(`Deleted department: ${department.id}`);
      } catch (error) {
        console.error(`Failed to delete department ${department.id}:`, error.message);
      }
    }

    // Reset created data
    this.createdData = {
      departments: [],
      forms: [],
      workflows: [],
      instances: [],
      users: []
    };
    this.saveCreatedData();
  }

  /**
   * Save created data to file
   */
  saveCreatedData() {
    try {
      fs.writeFileSync(
        this.dataFile,
        JSON.stringify(this.createdData, null, 2)
      );
    } catch (error) {
      console.error('Failed to save created data:', error.message);
    }
  }

  /**
   * Load created data from file
   */
  loadCreatedData() {
    try {
      if (fs.existsSync(this.dataFile)) {
        const data = fs.readFileSync(this.dataFile, 'utf8');
        this.createdData = JSON.parse(data);
      }
    } catch (error) {
      console.error('Failed to load created data:', error.message);
      this.createdData = {
        departments: [],
        forms: [],
        workflows: [],
        instances: [],
        users: []
      };
    }
  }
}

module.exports = TestDataManager;
