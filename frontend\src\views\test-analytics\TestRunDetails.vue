<template>
  <div class="test-run-details">
    <div class="page-header">
      <el-page-header @back="$router.push('/test-analytics')" title="测试分析" content="测试运行详情" />
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 错误提示 -->
    <el-alert
      v-if="error"
      :title="error"
      type="error"
      show-icon
      :closable="false"
      class="error-alert"
    />

    <template v-if="!loading && !error && testRun">
      <!-- 测试运行概览 -->
      <el-card class="overview-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>测试运行概览</span>
            <el-tag :type="testRun.success ? 'success' : 'danger'" size="large">
              {{ testRun.success ? '成功' : '失败' }}
            </el-tag>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="运行ID">{{ testRun.testRunId }}</el-descriptions-item>
          <el-descriptions-item label="测试类型">{{ testRun.testType || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="执行时间">{{ formatDate(testRun.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="耗时">{{ testRun.duration ? testRun.duration.toFixed(2) + 's' : '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 测试统计 -->
      <el-card v-if="testRun.testStats" class="stats-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>测试统计</span>
          </div>
        </template>
        <el-row :gutter="20" class="stats-row">
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-item">
              <div class="stat-value">{{ testRun.testStats.total || 0 }}</div>
              <div class="stat-label">总测试数</div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-item success">
              <div class="stat-value">{{ testRun.testStats.passed || 0 }}</div>
              <div class="stat-label">通过</div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-item danger">
              <div class="stat-value">{{ testRun.testStats.failed || 0 }}</div>
              <div class="stat-label">失败</div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-item warning">
              <div class="stat-value">{{ testRun.testStats.skipped || 0 }}</div>
              <div class="stat-label">跳过</div>
            </div>
          </el-col>
        </el-row>
        <div class="progress-container">
          <el-progress
            :percentage="calculatePassRate()"
            :format="formatPassRate"
            :stroke-width="20"
            :color="getPassRateColor()"
          />
        </div>
      </el-card>

      <!-- 测试结果图表 -->
      <el-card class="chart-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>测试结果分布</span>
          </div>
        </template>
        <div class="chart-container">
          <div id="resultsPieChart" class="chart"></div>
        </div>
      </el-card>

      <!-- 测试配置 -->
      <el-card v-if="testRun.config" class="config-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>测试配置</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item v-for="(value, key) in testRun.config" :key="key" :label="formatConfigKey(key)">
            {{ formatConfigValue(value) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 失败的测试 -->
      <el-card v-if="testRun.failedTests && testRun.failedTests.length > 0" class="failed-tests-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>失败的测试</span>
          </div>
        </template>
        <el-collapse>
          <el-collapse-item v-for="(test, index) in testRun.failedTests" :key="index" :name="index">
            <template #title>
              <div class="failed-test-title">
                <el-tag type="danger" size="small">失败</el-tag>
                <span class="test-file">{{ test.file }}</span>
                <span class="test-title">{{ test.title }}</span>
              </div>
            </template>
            <div class="failed-test-details">
              <div v-if="test.error" class="error-message">
                <div class="error-label">错误信息:</div>
                <div class="error-content">{{ test.error.message }}</div>
              </div>
              <div v-if="test.error && test.error.stack" class="error-stack">
                <div class="error-label">错误堆栈:</div>
                <pre class="error-content">{{ test.error.stack }}</pre>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-card>

      <!-- 测试结果列表 -->
      <el-card v-if="testRun.results && testRun.results.length > 0" class="results-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>测试结果列表</span>
            <div class="filter-container">
              <el-select v-model="statusFilter" placeholder="状态过滤" clearable>
                <el-option label="全部" value="" />
                <el-option label="通过" value="passed" />
                <el-option label="失败" value="failed" />
                <el-option label="跳过" value="skipped" />
              </el-select>
              <el-input
                v-model="searchQuery"
                placeholder="搜索测试"
                clearable
                class="search-input"
              />
            </div>
          </div>
        </template>
        <el-table :data="filteredResults" stripe style="width: 100%">
          <el-table-column prop="file" label="测试文件" min-width="200" />
          <el-table-column prop="title" label="测试标题" min-width="300" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag
                :type="getStatusType(scope.row.status)"
                size="small"
              >
                {{ formatStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="耗时" width="100">
            <template #default="scope">
              {{ scope.row.duration ? (scope.row.duration / 1000).toFixed(2) + 's' : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button
                v-if="scope.row.status === 'failed' && scope.row.error"
                type="danger"
                size="small"
                link
                @click="showErrorDetails(scope.row)"
              >
                查看错误
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 性能指标 -->
      <el-card v-if="testRun.metrics && testRun.metrics.length > 0" class="metrics-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>性能指标</span>
          </div>
        </template>
        <div class="chart-container">
          <div id="metricsChart" class="chart"></div>
        </div>
      </el-card>
    </template>

    <!-- 错误详情对话框 -->
    <el-dialog v-model="errorDialogVisible" title="错误详情" width="60%">
      <div v-if="selectedError" class="error-details">
        <div class="error-details-header">
          <div class="error-test-info">
            <div class="error-test-file">{{ selectedError.file }}</div>
            <div class="error-test-title">{{ selectedError.title }}</div>
          </div>
        </div>
        <div class="error-message">
          <div class="error-label">错误信息:</div>
          <div class="error-content">{{ selectedError.error.message }}</div>
        </div>
        <div v-if="selectedError.error.stack" class="error-stack">
          <div class="error-label">错误堆栈:</div>
          <pre class="error-content">{{ selectedError.error.stack }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useTestAnalyticsStore } from '../../stores/test-analytics'
import * as echarts from 'echarts/core'
import { PieChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  PieChart,
  LineChart,
  CanvasRenderer
])

const route = useRoute()
const store = useTestAnalyticsStore()
const statusFilter = ref('')
const searchQuery = ref('')
const errorDialogVisible = ref(false)
const selectedError = ref(null)

// 计算属性
const loading = computed(() => store.loading)
const error = computed(() => store.error)
const testRun = computed(() => store.currentTestRun)

// 过滤测试结果
const filteredResults = computed(() => {
  if (!testRun.value || !testRun.value.results) return []
  
  return testRun.value.results.filter(result => {
    // 状态过滤
    if (statusFilter.value && result.status !== statusFilter.value) {
      return false
    }
    
    // 搜索过滤
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      return (
        result.file.toLowerCase().includes(query) ||
        result.title.toLowerCase().includes(query)
      )
    }
    
    return true
  })
})

// 图表实例
let resultsPieChart = null
let metricsChart = null

// 初始化图表
const initCharts = () => {
  // 结果分布饼图
  resultsPieChart = echarts.init(document.getElementById('resultsPieChart'))
  
  // 性能指标图
  if (testRun.value && testRun.value.metrics && testRun.value.metrics.length > 0) {
    metricsChart = echarts.init(document.getElementById('metricsChart'))
  }
  
  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    resultsPieChart && resultsPieChart.resize()
    metricsChart && metricsChart.resize()
  })
}

// 更新图表数据
const updateCharts = () => {
  // 更新结果分布饼图
  if (resultsPieChart && testRun.value && testRun.value.testStats) {
    const { passed = 0, failed = 0, skipped = 0 } = testRun.value.testStats
    
    resultsPieChart.setOption({
      title: {
        text: '测试结果分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: ['通过', '失败', '跳过']
      },
      series: [
        {
          name: '测试结果',
          type: 'pie',
          radius: '70%',
          data: [
            { value: passed, name: '通过', itemStyle: { color: '#67C23A' } },
            { value: failed, name: '失败', itemStyle: { color: '#F56C6C' } },
            { value: skipped, name: '跳过', itemStyle: { color: '#E6A23C' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }
  
  // 更新性能指标图
  if (metricsChart && testRun.value && testRun.value.metrics && testRun.value.metrics.length > 0) {
    // 按指标类型分组
    const metricsByType = {}
    testRun.value.metrics.forEach(metric => {
      if (!metricsByType[metric.metricType]) {
        metricsByType[metric.metricType] = []
      }
      metricsByType[metric.metricType].push(metric)
    })
    
    // 准备图表数据
    const series = []
    const metricTypes = Object.keys(metricsByType)
    
    metricTypes.forEach(type => {
      const metrics = metricsByType[type].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
      
      series.push({
        name: formatMetricType(type),
        type: 'line',
        data: metrics.map(m => m.value),
        smooth: true
      })
    })
    
    // 设置图表选项
    metricsChart.setOption({
      title: {
        text: '性能指标',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: metricTypes.map(formatMetricType),
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: metricsByType[metricTypes[0]].map((_, index) => index + 1)
      },
      yAxis: {
        type: 'value'
      },
      series
    })
  }
}

// 计算通过率
const calculatePassRate = () => {
  if (!testRun.value || !testRun.value.testStats) return 0
  const { total = 0, passed = 0 } = testRun.value.testStats
  return total > 0 ? Math.round((passed / total) * 100) : 0
}

// 格式化通过率
const formatPassRate = (percentage) => {
  return `${percentage}% 通过率`
}

// 获取通过率颜色
const getPassRateColor = () => {
  const rate = calculatePassRate()
  if (rate < 60) return '#F56C6C'
  if (rate < 80) return '#E6A23C'
  return '#67C23A'
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'passed': return 'success'
    case 'failed': return 'danger'
    case 'skipped': return 'warning'
    default: return 'info'
  }
}

// 格式化状态
const formatStatus = (status) => {
  switch (status) {
    case 'passed': return '通过'
    case 'failed': return '失败'
    case 'skipped': return '跳过'
    default: return status
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 格式化配置键
const formatConfigKey = (key) => {
  return key
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .replace(/_/g, ' ')
}

// 格式化配置值
const formatConfigValue = (value) => {
  if (value === null || value === undefined) return '-'
  if (typeof value === 'boolean') return value ? '是' : '否'
  if (typeof value === 'object') return JSON.stringify(value)
  return value.toString()
}

// 格式化指标类型
const formatMetricType = (type) => {
  switch (type) {
    case 'cpu': return 'CPU 使用率'
    case 'memory': return '内存使用率'
    case 'responseTime': return '响应时间'
    default: return type
  }
}

// 显示错误详情
const showErrorDetails = (result) => {
  selectedError.value = result
  errorDialogVisible.value = true
}

// 监听测试运行数据变化
watch(testRun, () => {
  if (testRun.value) {
    // 等待 DOM 更新后初始化图表
    setTimeout(() => {
      initCharts()
      updateCharts()
    }, 100)
  }
})

// 组件挂载时获取数据
onMounted(async () => {
  const testRunId = route.params.id
  if (testRunId) {
    await store.fetchTestRunById(testRunId)
  }
})
</script>

<style scoped>
.test-run-details {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.loading-container {
  padding: 20px;
}

.error-alert {
  margin-bottom: 20px;
}

.overview-card,
.stats-card,
.chart-card,
.config-card,
.failed-tests-card,
.results-card,
.metrics-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-row {
  margin-top: 16px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.stat-item.success {
  background-color: rgba(103, 194, 58, 0.1);
}

.stat-item.danger {
  background-color: rgba(245, 108, 108, 0.1);
}

.stat-item.warning {
  background-color: rgba(230, 162, 60, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.progress-container {
  margin-top: 16px;
}

.chart-container {
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.filter-container {
  display: flex;
  gap: 16px;
}

.search-input {
  width: 200px;
}

.failed-test-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.test-file {
  color: #606266;
  font-weight: 500;
}

.test-title {
  color: #303133;
  font-weight: 400;
}

.failed-test-details {
  padding: 16px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.error-message,
.error-stack {
  margin-bottom: 16px;
}

.error-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: #606266;
}

.error-content {
  font-family: monospace;
  white-space: pre-wrap;
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #F56C6C;
  overflow-x: auto;
}

.error-details-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.error-test-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-test-file {
  font-weight: 500;
  color: #606266;
}

.error-test-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    gap: 8px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .chart-container {
    height: 250px;
  }
}
</style>
