/**
 * Accessibility Tests for Core Pages
 */

const { test } = require('@playwright/test');
const { login } = require('../../utils/test-helpers');
const AccessibilityHelper = require('../../utils/accessibility-helper');
const fs = require('fs');
const path = require('path');

test.describe('Core Pages Accessibility Tests', () => {
  // Define pages to test
  const pagesToTest = [
    { name: 'Login Page', path: '/login', requiresAuth: false },
    { name: 'Home Page', path: '/', requiresAuth: true },
    { name: 'Department Page', path: '/department', requiresAuth: true },
    { name: 'Form Design Page', path: '/form-design', requiresAuth: true },
    { name: 'Workflow Design Page', path: '/workflow-design', requiresAuth: true },
    { name: 'Workflow Form Page', path: '/workflow-form', requiresAuth: true },
    { name: 'Workflow Process Page', path: '/workflow-process', requiresAuth: true }
  ];

  // Rules to exclude (false positives or non-critical issues)
  const excludeRules = [
    'color-contrast', // Temporarily exclude color contrast issues
    'landmark-one-main', // Some pages might not have proper landmarks yet
    'region' // Some pages might not have proper regions yet
  ];

  // Test each page
  for (const page of pagesToTest) {
    test(`${page.name} should be accessible`, async ({ page: pageObject }) => {
      // Login if required
      if (page.requiresAuth) {
        await login(pageObject, 'admin');
      }
      
      // Navigate to page
      await pageObject.goto(page.path);
      
      // Wait for page to load completely
      await pageObject.waitForLoadState('networkidle');
      
      // Run accessibility tests
      await AccessibilityHelper.expectNoViolations(pageObject, {
        includedImpacts: ['critical', 'serious'],
        excludeRules
      });
    });
  }

  // Generate comprehensive accessibility report
  test('Generate accessibility report for all pages', async ({ page }) => {
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(__dirname, '../../accessibility-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // Login first
    await login(page, 'admin');
    
    // Test each page and collect reports
    const reports = [];
    
    for (const pageInfo of pagesToTest) {
      // Skip login page since we're already logged in
      if (pageInfo.path === '/login' && pageInfo.requiresAuth === false) {
        // Logout first
        await page.goto('/');
        await page.evaluate(() => {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
        });
      }
      
      // Navigate to page
      await page.goto(pageInfo.path);
      
      // Wait for page to load completely
      await page.waitForLoadState('networkidle');
      
      // Generate report
      const report = await AccessibilityHelper.generateAccessibilityReport(
        page,
        pageInfo.name,
        { excludeRules }
      );
      
      reports.push(report);
      
      // Take screenshot for reference
      await page.screenshot({
        path: path.join(reportsDir, `${pageInfo.name.replace(/\s+/g, '-').toLowerCase()}.png`),
        fullPage: true
      });
    }
    
    // Save consolidated report
    fs.writeFileSync(
      path.join(reportsDir, 'accessibility-report.json'),
      JSON.stringify(reports, null, 2)
    );
    
    // Generate HTML report
    const htmlReport = generateHtmlReport(reports);
    fs.writeFileSync(
      path.join(reportsDir, 'accessibility-report.html'),
      htmlReport
    );
  });
});

/**
 * Generate HTML report from accessibility data
 * @param {Array<object>} reports - Accessibility reports
 * @returns {string} - HTML report
 */
function generateHtmlReport(reports) {
  const totalViolations = reports.reduce((sum, report) => sum + report.violations, 0);
  const timestamp = new Date().toLocaleString();
  
  let html = `
  <!DOCTYPE html>
  <html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流系统无障碍测试报告</title>
    <style>
      body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }
      h1 { color: #2c3e50; border-bottom: 2px solid #eee; padding-bottom: 10px; }
      h2 { color: #3498db; margin-top: 30px; }
      table { border-collapse: collapse; width: 100%; margin-bottom: 30px; }
      th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
      th { background-color: #f2f2f2; }
      tr:nth-child(even) { background-color: #f9f9f9; }
      .summary { background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
      .violation-count { font-weight: bold; }
      .critical { color: #e74c3c; }
      .serious { color: #e67e22; }
      .moderate { color: #f39c12; }
      .minor { color: #3498db; }
      .screenshot { max-width: 300px; border: 1px solid #ddd; margin-top: 10px; }
    </style>
  </head>
  <body>
    <h1>工作流系统无障碍测试报告</h1>
    <div class="summary">
      <p>测试时间: ${timestamp}</p>
      <p>测试页面数: ${reports.length}</p>
      <p>发现问题总数: <span class="violation-count ${totalViolations > 0 ? 'critical' : ''}">${totalViolations}</span></p>
    </div>
    
    <h2>页面测试结果摘要</h2>
    <table>
      <tr>
        <th>页面名称</th>
        <th>URL</th>
        <th>问题数</th>
        <th>通过数</th>
        <th>不完整数</th>
        <th>不适用数</th>
      </tr>
  `;
  
  reports.forEach(report => {
    html += `
      <tr>
        <td>${report.pageName}</td>
        <td>${report.url}</td>
        <td class="${report.violations > 0 ? 'critical' : ''}">${report.violations}</td>
        <td>${report.passes}</td>
        <td>${report.incomplete}</td>
        <td>${report.inapplicable}</td>
      </tr>
    `;
  });
  
  html += `
    </table>
    
    <h2>详细问题列表</h2>
  `;
  
  reports.forEach(report => {
    if (report.violations === 0) {
      return;
    }
    
    html += `
      <h3>${report.pageName}</h3>
      <table>
        <tr>
          <th>问题ID</th>
          <th>严重程度</th>
          <th>描述</th>
          <th>元素数</th>
          <th>帮助链接</th>
        </tr>
    `;
    
    report.violationDetails.forEach(violation => {
      html += `
        <tr>
          <td>${violation.id}</td>
          <td class="${violation.impact}">${violation.impact}</td>
          <td>${violation.description}</td>
          <td>${violation.nodeCount}</td>
          <td><a href="${violation.helpUrl}" target="_blank">查看详情</a></td>
        </tr>
      `;
    });
    
    html += `
      </table>
      <img src="${report.pageName.replace(/\s+/g, '-').toLowerCase()}.png" alt="${report.pageName} 截图" class="screenshot">
    `;
  });
  
  html += `
    <h2>测试说明</h2>
    <p>本测试基于 axe-core 进行自动化无障碍检测，符合 WCAG 2.1 AA 级标准。</p>
    <p>测试排除了以下规则：</p>
    <ul>
      <li>color-contrast - 颜色对比度（暂时排除）</li>
      <li>landmark-one-main - 主要地标（部分页面可能尚未实现）</li>
      <li>region - 区域定义（部分页面可能尚未实现）</li>
    </ul>
  </body>
  </html>
  `;
  
  return html;
}
