const { test, expect } = require('@playwright/test');
const { login, navigateToModule } = require('../utils/test-helpers');

test.describe('Department Management Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await login(page, 'admin');
    // Navigate to department page
    await navigateToModule(page, '部门配置');
  });

  test('should display department list', async ({ page }) => {
    // Verify department table is visible
    await expect(page.locator('.el-table')).toBeVisible();
    
    // Verify table headers
    const headers = ['部门名称', '部门代码', '部门负责人', '描述', '状态', '操作'];
    for (const header of headers) {
      await expect(page.locator('.el-table__header').getByText(header)).toBeVisible();
    }
  });

  test('should open add department dialog', async ({ page }) => {
    // Click add department button
    await page.click('button:has-text("添加部门")');
    
    // Verify dialog is displayed
    await expect(page.locator('.el-dialog__title')).toContainText('添加部门');
    await expect(page.locator('form')).toBeVisible();
    
    // Verify form fields
    await expect(page.locator('label').filter({ hasText: '部门名称' })).toBeVisible();
    await expect(page.locator('label').filter({ hasText: '部门代码' })).toBeVisible();
    await expect(page.locator('label').filter({ hasText: '上级部门' })).toBeVisible();
    
    // Close dialog
    await page.click('.el-dialog__close');
  });

  test('should create a new department', async ({ page }) => {
    // Generate unique department name and code
    const timestamp = Date.now();
    const deptName = `测试部门${timestamp}`;
    const deptCode = `TEST${timestamp}`;
    
    // Click add department button
    await page.click('button:has-text("添加部门")');
    
    // Fill department form
    await page.fill('input[placeholder="请输入部门名称"]', deptName);
    await page.fill('input[placeholder="请输入部门代码"]', deptCode);
    await page.fill('textarea[placeholder="请输入部门描述"]', '这是一个测试部门');
    
    // Submit form
    await page.click('.el-dialog__footer button:has-text("确定")');
    
    // Verify success message
    await expect(page.locator('.el-message--success')).toBeVisible();
    
    // Verify new department appears in the table
    await expect(page.locator('.el-table__row').filter({ hasText: deptName })).toBeVisible();
    await expect(page.locator('.el-table__row').filter({ hasText: deptCode })).toBeVisible();
  });

  test('should edit an existing department', async ({ page }) => {
    // Wait for table to load
    await page.waitForSelector('.el-table__row');
    
    // Click edit button on first department
    await page.locator('.el-table__row').first().locator('button:has-text("编辑")').click();
    
    // Verify edit dialog is displayed
    await expect(page.locator('.el-dialog__title')).toContainText('编辑部门');
    
    // Update department description
    const newDesc = '更新的部门描述' + Date.now();
    await page.fill('textarea[placeholder="请输入部门描述"]', newDesc);
    
    // Submit form
    await page.click('.el-dialog__footer button:has-text("确定")');
    
    // Verify success message
    await expect(page.locator('.el-message--success')).toBeVisible();
    
    // Verify department description is updated
    await expect(page.locator('.el-table__row').first()).toContainText(newDesc);
  });

  test('should manage department members', async ({ page }) => {
    // Wait for table to load
    await page.waitForSelector('.el-table__row');
    
    // Click manage members button on first department
    await page.locator('.el-table__row').first().locator('button:has-text("成员管理")').click();
    
    // Verify members dialog is displayed
    await expect(page.locator('.el-dialog__title')).toContainText('部门成员管理');
    
    // Verify members table is visible
    await expect(page.locator('.member-table')).toBeVisible();
    
    // Close dialog
    await page.click('.el-dialog__close');
  });
});
