<template>
  <div class="workflow-process-detail">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <h2>工作流实例详情</h2>
          <el-button @click="goBack" type="primary" plain>返回</el-button>
        </div>
      </template>

      <div v-if="workflowInstance">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工作流标题">{{ workflowInstance.title }}</el-descriptions-item>
          <el-descriptions-item label="工作流类型">{{ workflowInstance.workflowTemplate ? workflowInstance.workflowTemplate.title : '-' }}</el-descriptions-item>
          <el-descriptions-item label="当前节点">{{ workflowInstance.currentNode ? workflowInstance.currentNode.name : '-' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(workflowInstance.status)">
              {{ getStatusText(workflowInstance.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发起人">{{ workflowInstance.initiator ? workflowInstance.initiator.full_name : '-' }}</el-descriptions-item>
          <el-descriptions-item label="发起时间">{{ formatDate(workflowInstance.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间" v-if="workflowInstance.completed_at">{{ formatDate(workflowInstance.completed_at) }}</el-descriptions-item>
        </el-descriptions>

        <el-tabs class="workflow-detail-tabs" style="margin-top: 20px;">
          <el-tab-pane label="表单数据">
            <FormDataDisplay
              :form-data="{
                ...workflowInstance.form_data,
                workflowTemplate: workflowInstance.workflowTemplate,
                fields: formFields
              }"
              :schema="workflowInstance.form_schema || workflowInstance.workflow_schema"
            />
          </el-tab-pane>
          <el-tab-pane label="流程图">
            <WorkflowVisualizer :workflow-instance="workflowInstance" />
          </el-tab-pane>
          <el-tab-pane label="任务历史">
            <el-timeline>
              <el-timeline-item
                v-for="history in taskHistories"
                :key="history.id"
                :timestamp="formatDate(history.created_at)"
                :type="getOperationColor(history.operation)">
                <h4>{{ getOperationText(history.operation) }}</h4>
                <p>操作人: {{ history.operator ? history.operator.full_name : '-' }}</p>
                <p v-if="history.comments">备注: {{ history.comments }}</p>
              </el-timeline-item>
            </el-timeline>
          </el-tab-pane>
        </el-tabs>
      </div>
      <el-empty v-else description="未找到工作流实例数据"></el-empty>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { workflowApi, formApi } from '@/api'
import WorkflowVisualizer from '@/components/WorkflowVisualizer.vue'
import FormDataDisplay from '@/components/FormDataDisplay.vue'

const route = useRoute()
const router = useRouter()
const workflowInstance = ref(null)
const taskHistories = ref([])
const formFields = ref([])
const loading = ref(true)

// 初始化
onMounted(async () => {
  const instanceId = route.params.id
  if (!instanceId) {
    ElMessage.error('缺少工作流实例ID')
    router.push('/workflow-process')
    return
  }

  try {
    await fetchWorkflowInstance(instanceId)
  } catch (error) {
    console.error('获取工作流实例详情失败:', error)
    ElMessage.error('获取工作流实例详情失败')
  } finally {
    loading.value = false
  }
})

// 获取工作流实例详情
const fetchWorkflowInstance = async (instanceId) => {
  try {
    const result = await workflowApi.getWorkflowInstance(instanceId)
    if (result.success && result.data) {
      workflowInstance.value = result.data

      // 获取任务历史
      if (workflowInstance.value.taskHistories) {
        taskHistories.value = workflowInstance.value.taskHistories
      }

      // 获取表单字段
      if (workflowInstance.value.workflowTemplate?.form_template_id) {
        const formResult = await formApi.getFormById(
          workflowInstance.value.workflowTemplate.form_template_id
        )
        if (formResult.success && formResult.data?.fields) {
          formFields.value = formResult.data.fields
        }
      }
    } else {
      ElMessage.error(result.message || '获取工作流实例详情失败')
    }
  } catch (error) {
    console.error('获取工作流实例详情失败:', error)
    throw error
  }
}

// 返回上一页
const goBack = () => {
  router.push('/workflow-process')
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'running': return 'primary'
    case 'completed': return 'success'
    case 'terminated': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'running': return '进行中'
    case 'completed': return '已完成'
    case 'terminated': return '已终止'
    default: return '未知'
  }
}

// 获取操作颜色
const getOperationColor = (operation) => {
  switch (operation) {
    case 'start': return 'primary'
    case 'approve': return 'success'
    case 'reject': return 'danger'
    case 'return': return 'warning'
    case 'transfer': return 'info'
    default: return ''
  }
}

// 获取操作文本
const getOperationText = (operation) => {
  switch (operation) {
    case 'start': return '发起流程'
    case 'approve': return '同意'
    case 'reject': return '拒绝'
    case 'return': return '退回'
    case 'transfer': return '转交'
    default: return operation
  }
}

// 获取字段标签
const getFieldLabel = (key) => {
  // 安全检查
  if (!key) return '未知字段';

  try {
    // 首先在表单字段中查找
    if (formFields.value && Array.isArray(formFields.value) && formFields.value.length > 0) {
      const field = formFields.value.find(f => f && f.field_key === key);
      if (field && field.label) {
        return field.label;
      }
    }

    // 其次，尝试从工作流实例中查找字段信息
    if (workflowInstance.value && workflowInstance.value.workflowTemplate) {
      // 如果工作流模板包含form_template对象
      if (workflowInstance.value.workflowTemplate.form_template &&
          workflowInstance.value.workflowTemplate.form_template.fields &&
          Array.isArray(workflowInstance.value.workflowTemplate.form_template.fields)) {
        const field = workflowInstance.value.workflowTemplate.form_template.fields.find(
          f => f && f.field_key === key
        );
        if (field && field.label) {
          return field.label;
        }
      }

      // 尝试从模板schema中查找
      if (workflowInstance.value.workflowTemplate.schema &&
          workflowInstance.value.workflowTemplate.schema.form_template &&
          workflowInstance.value.workflowTemplate.schema.form_template.fields &&
          Array.isArray(workflowInstance.value.workflowTemplate.schema.form_template.fields)) {
        const field = workflowInstance.value.workflowTemplate.schema.form_template.fields.find(
          f => f && f.field_key === key
        );
        if (field && field.label) {
          return field.label;
        }
      }
    }

    // 最后，尝试格式化key本身作为标签
    // 将下划线替换为空格，并首字母大写
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  } catch (error) {
    console.error('获取字段标签时出错:', error);
    return key; // 出错时返回原始key
  }
}

// 格式化字段值
const formatFieldValue = (key, value) => {
  // 处理空值
  if (value === null || value === undefined) return '-';

  try {
    // 如果是选项类型的字段，尝试将值转换为标签
    if (formFields.value && Array.isArray(formFields.value)) {
      const field = formFields.value.find(f => f && f.field_key === key);

      if (field && field.options && Array.isArray(field.options)) {
        // 多选
        if (Array.isArray(value)) {
          return value.map(v => {
            try {
              const option = field.options.find(opt => opt && opt.value == v);
              return option ? option.label : v;
            } catch (e) {
              console.warn(`格式化多选值时出错(${key}):`, e);
              return v;
            }
          }).filter(v => v !== null && v !== undefined).join(', ') || '-';
        }

        // 单选
        try {
          const option = field.options.find(opt => opt && opt.value == value);
          if (option) return option.label;
        } catch (e) {
          console.warn(`格式化单选值时出错(${key}):`, e);
        }
      }

      // 日期类型
      if (field && (field.field_type === 'date' || field.field_type === 'datetime')) {
        try {
          return formatDate(value);
        } catch (e) {
          console.warn(`格式化日期时出错(${key}):`, e);
        }
      }

      // 布尔类型
      if (field && field.field_type === 'switch') {
        return value === true ? '是' : '否';
      }
    }

    // 如果是日期字段（通过格式判断）
    if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
      return formatDate(value);
    }

    // 如果是数组
    if (Array.isArray(value)) {
      return value.join(', ') || '-';
    }

    // 如果是对象
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value);
      } catch (e) {
        console.warn(`JSON序列化对象时出错(${key}):`, e);
        return '[复杂对象]';
      }
    }

    // 最后的安全返回
    return String(value);
  } catch (error) {
    console.error(`格式化字段值时出错(${key}):`, error);
    return String(value || '-');
  }
}
</script>

<style scoped>
.workflow-process-detail {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workflow-detail-tabs {
  margin-top: 20px;
}
</style>
