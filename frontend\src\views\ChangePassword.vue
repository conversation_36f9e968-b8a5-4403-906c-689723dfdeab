<template>
  <div class="change-password-page">
    <el-card class="password-card">
      <template #header>
        <div class="card-header">
          <h2>修改密码</h2>
        </div>
      </template>
      
      <div v-loading="loading" class="password-content">
        <el-form 
          :model="passwordForm" 
          :rules="rules" 
          ref="passwordFormRef" 
          label-width="120px" 
          class="password-form"
        >
          <el-form-item label="当前密码" prop="current_password">
            <el-input 
              v-model="passwordForm.current_password" 
              type="password" 
              placeholder="请输入当前密码" 
              show-password
            ></el-input>
          </el-form-item>
          
          <el-form-item label="新密码" prop="new_password">
            <el-input 
              v-model="passwordForm.new_password" 
              type="password" 
              placeholder="请输入新密码" 
              show-password
            ></el-input>
          </el-form-item>
          
          <el-form-item label="确认新密码" prop="confirm_password">
            <el-input 
              v-model="passwordForm.confirm_password" 
              type="password" 
              placeholder="请再次输入新密码" 
              show-password
            ></el-input>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="changePassword" :loading="loading">确认修改</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
          
          <div class="password-tips">
            <h3>密码要求</h3>
            <ul>
              <li>密码长度至少6位</li>
              <li>建议包含大小写字母、数字和特殊字符</li>
              <li>不要使用与旧密码相似的密码</li>
            </ul>
          </div>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { userApi } from '@/api'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
const loading = ref(false)
const passwordFormRef = ref(null)

// 表单数据
const passwordForm = reactive({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

// 表单验证规则
const rules = {
  current_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    
    loading.value = true
    
    // 获取当前用户ID
    const userStr = localStorage.getItem('user')
    if (!userStr) {
      ElMessage.error('获取用户信息失败')
      return
    }
    
    const user = JSON.parse(userStr)
    const userId = user.id
    
    if (!userId) {
      ElMessage.error('获取用户ID失败')
      return
    }
    
    const { success, message: msg } = await userApi.changePassword(userId, {
      current_password: passwordForm.current_password,
      new_password: passwordForm.new_password
    })
    
    if (success) {
      ElMessage.success('密码修改成功，请重新登录')
      
      // 清除登录信息，跳转到登录页
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      router.push('/login')
    } else {
      ElMessage.error(msg || '密码修改失败')
    }
  } catch (error) {
    console.error('修改密码错误:', error)
    ElMessage.error('表单验证失败，请检查输入')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  passwordFormRef.value?.resetFields()
}
</script>

<style scoped>
.change-password-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.password-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
}

.password-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.password-form {
  width: 100%;
  max-width: 500px;
}

.password-tips {
  margin-top: 30px;
  padding: 15px;
  background-color: var(--background-color-light);
  border-radius: 4px;
  border-left: 4px solid var(--primary-color);
}

.password-tips h3 {
  margin-top: 0;
  font-size: 16px;
  color: var(--text-color);
}

.password-tips ul {
  margin-bottom: 0;
  padding-left: 20px;
  color: var(--text-color-secondary);
}
</style> 