<template>
  <div class="workflow-visualizer">
    <div v-if="useStaticView || !hasValidData" class="static-flow">
      <div class="static-flow-container">
        <div v-for="(node, index) in staticNodes" :key="node.id" class="flow-node"
             :class="[`node-type-${node.node_type}`, node.isActive ? 'active' : '', node.isCompleted ? 'completed' : '']"
             :style="getNodeStyle(node, index)">
          <div class="node-content">{{ node.name }}</div>
        </div>
        <svg class="flow-lines" :width="flowWidth" :height="flowHeight">
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#909399" />
            </marker>
            <marker id="arrowhead-active" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#409EFF" />
            </marker>
            <marker id="arrowhead-completed" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#67C23A" />
            </marker>
          </defs>
          <path v-for="(line, idx) in staticLines" :key="idx"
                :d="line.path" :stroke="line.stroke"
                stroke-width="2" fill="none"
                :marker-end="line.marker" />
        </svg>
      </div>
      <div class="static-flow-controls">
        <el-button size="small" @click="toggleView">切换到{{ useStaticView ? 'ECharts图表' : '静态视图' }}</el-button>
        <el-button size="small" @click="showDebugInfo" type="info">调试信息</el-button>
      </div>
    </div>
    <div v-else ref="chartRef" class="chart-container"></div>
    <div class="visualizer-controls">
      <el-button v-if="!useStaticView" size="small" @click="toggleView">切换到静态视图</el-button>
      <el-button size="small" @click="showDebugInfo" type="info">调试信息</el-button>
    </div>

    <!-- 调试信息对话框 -->
    <el-dialog
      v-model="debugDialogVisible"
      title="工作流数据调试"
      width="80%">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="工作流实例" name="instance">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="ID">{{ props.workflowInstance.id }}</el-descriptions-item>
            <el-descriptions-item label="标题">{{ props.workflowInstance.title }}</el-descriptions-item>
            <el-descriptions-item label="状态">{{ props.workflowInstance.status }}</el-descriptions-item>
            <el-descriptions-item label="当前节点ID">{{ currentNodeId }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="节点数据" name="nodes">
          <div style="margin-bottom: 10px">
            <strong>节点数量:</strong> {{ staticNodes.length }}
          </div>
          <el-table :data="staticNodes" border max-height="400">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="名称" width="150" />
            <el-table-column prop="node_type" label="类型" width="100" />
            <el-table-column prop="level" label="层级" width="80" />
            <el-table-column prop="isActive" label="当前节点" width="100">
              <template #default="scope">
                {{ scope.row.isActive ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column prop="isCompleted" label="已完成" width="100">
              <template #default="scope">
                {{ scope.row.isCompleted ? '是' : '否' }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="转换数据" name="transitions">
          <div style="margin-bottom: 10px">
            <strong>转换数量:</strong> {{ staticLines.length }}
          </div>
          <div style="max-height: 400px; overflow: auto">
            <pre>{{ JSON.stringify(staticLines, null, 2) }}</pre>
          </div>
        </el-tab-pane>
        <el-tab-pane label="原始数据" name="raw">
          <div style="max-height: 400px; overflow: auto">
            <pre>{{ JSON.stringify(preprocessWorkflowData(props.workflowInstance), null, 2) }}</pre>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import * as echarts from 'echarts'

// 接收工作流实例和模板作为props
const props = defineProps({
  workflowInstance: {
    type: Object,
    required: true
  }
})

const chartRef = ref(null)
let chart = null

// 静态视图控制
const useStaticView = ref(false)
const flowWidth = ref(1000) // 增加默认宽度
const flowHeight = ref(700) // 增加默认高度
const debugDialogVisible = ref(false)
const activeTab = ref('instance')

// 显示调试信息
const showDebugInfo = () => {
  debugDialogVisible.value = true
}

// 切换视图
const toggleView = () => {
  useStaticView.value = !useStaticView.value
  if (!useStaticView.value) {
    nextTick(() => {
      initChart()
    })
  }
}

// 计算当前节点的ID
const currentNodeId = computed(() => {
  return props.workflowInstance.currentNode?.id || null
})

// 检查是否有有效的数据
const hasValidData = computed(() => {
  const { nodes, transitions } = preprocessWorkflowData(props.workflowInstance)
  return nodes.filter(node => node && node.id).length > 0 &&
         transitions.filter(t => t && t.source_node_id && t.target_node_id).length > 0
})

// 预处理工作流数据，确保格式正确
const preprocessWorkflowData = (instance) => {
  if (!instance || !instance.workflowTemplate) {
    console.warn('工作流实例或模板为空')
    return { nodes: [], transitions: [] }
  }

  let template = instance.workflowTemplate

  // 处理节点数据
  let nodes = []
  if (Array.isArray(template.nodes)) {
    nodes = template.nodes
  } else if (template.schema && Array.isArray(template.schema.nodes)) {
    // 从schema中获取节点
    nodes = template.schema.nodes
  }

  // 处理转换数据
  let transitions = []
  if (Array.isArray(template.transitions)) {
    transitions = template.transitions
  } else if (template.schema && Array.isArray(template.schema.transitions)) {
    // 从schema中获取转换
    transitions = template.schema.transitions
  }

  // 补充转换的源节点和目标节点引用
  transitions.forEach(transition => {
    // 已经有完整引用的情况跳过
    if (transition.sourceNode && transition.targetNode) {
      return
    }

    // 先确保source_node_id和target_node_id字段存在
    if (!transition.source_node_id && transition.sourceNode) {
      transition.source_node_id = transition.sourceNode.id || transition.sourceNode.node_id
    } else if (!transition.source_node_id && transition.source_node_key) {
      // 尝试通过node_key找到对应的node_id
      const sourceNode = nodes.find(n => n.node_key === transition.source_node_key)
      if (sourceNode) {
        transition.source_node_id = sourceNode.id
      }
    }

    if (!transition.target_node_id && transition.targetNode) {
      transition.target_node_id = transition.targetNode.id || transition.targetNode.node_id
    } else if (!transition.target_node_id && transition.target_node_key) {
      // 尝试通过node_key找到对应的node_id
      const targetNode = nodes.find(n => n.node_key === transition.target_node_key)
      if (targetNode) {
        transition.target_node_id = targetNode.id
      }
    }

    // 为转换添加源节点和目标节点引用
    if (transition.source_node_id) {
      const sourceNode = nodes.find(n => n.id === transition.source_node_id)
      if (sourceNode) {
        transition.sourceNode = sourceNode
      }
    }

    if (transition.target_node_id) {
      const targetNode = nodes.find(n => n.id === transition.target_node_id)
      if (targetNode) {
        transition.targetNode = targetNode
      }
    }
  })

  console.log('预处理后的节点数据:', nodes)
  console.log('预处理后的转换数据:', transitions)

  return { nodes, transitions }
}

// 这里已经在上面定义了hasValidData，所以这段代码可以删除

// 静态流程图节点
const staticNodes = computed(() => {
  const { nodes, transitions } = preprocessWorkflowData(props.workflowInstance)
  const completedNodeIds = new Set()
  if (props.workflowInstance.taskHistories && Array.isArray(props.workflowInstance.taskHistories)) {
    props.workflowInstance.taskHistories.forEach(history => {
      if (history.node_id) {
        completedNodeIds.add(history.node_id)
      }
    })
  }

  // 添加层级信息(level)到节点
  const nodeMap = new Map() // 用于后续查找
  const nodeWithLevels = nodes.filter(node => node && node.id).map(node => {
    const isActive = node.id === currentNodeId.value
    const isCompleted = completedNodeIds.has(node.id)
    const nodeData = {
      ...node,
      level: 0, // 默认层级
      isActive,
      isCompleted
    }
    if (node.id) {
      nodeMap.set(node.id.toString(), nodeData)
    }
    return nodeData
  })

  // 通过BFS设置层级
  const startNodes = nodeWithLevels.filter(n => n.node_type === 'start')
  const queue = [...startNodes]

  if (queue.length === 0 && nodeWithLevels.length > 0) {
    // 如果没有开始节点，选一个作为开始
    queue.push(nodeWithLevels[0])
  }

  // 防止死循环
  const visited = new Set()

  // 处理层级关系
  while (queue.length > 0) {
    const currentNode = queue.shift()
    if (!currentNode || !currentNode.id) continue

    const nodeId = currentNode.id.toString()

    if (visited.has(nodeId)) continue
    visited.add(nodeId)

    // 查找出口转换
    const outgoingTransitions = transitions.filter(t =>
      t && t.source_node_id && typeof t.source_node_id.toString === 'function' && t.source_node_id.toString() === nodeId
    )

    // 处理子节点
    for (const transition of outgoingTransitions) {
      if (!transition.target_node_id) continue

      const targetId = typeof transition.target_node_id.toString === 'function' ? transition.target_node_id.toString() : String(transition.target_node_id)
      const targetNode = nodeMap.get(targetId)

      if (targetNode) {
        // 设置子节点层级 = 当前层级 + 1
        targetNode.level = Math.max(targetNode.level, currentNode.level + 1)
        queue.push(targetNode)
      }
    }
  }

  return nodeWithLevels
})

// 静态流程图连线
const staticLines = computed(() => {
  const { transitions } = preprocessWorkflowData(props.workflowInstance)
  const nodeMap = new Map()

  // 创建节点位置映射
  staticNodes.value.forEach((node, index) => {
    if (!node || !node.id) return

    const x = 100 + node.level * 300 // 水平间距增加到300，与getNodeStyle保持一致
    const y = 70 + (index % 3) * 180 // 垂直间距增加到180，与getNodeStyle保持一致

    if (node && node.id) {
      nodeMap.set(node.id.toString(), {
        x,
        y,
        isActive: node.isActive,
        isCompleted: node.isCompleted,
        level: node.level
      })
    }
  })

  return transitions
    .filter(transition => transition && transition.source_node_id && transition.target_node_id)
    .map(transition => {
      const sourceId = typeof transition.source_node_id.toString === 'function' ?
        transition.source_node_id.toString() : String(transition.source_node_id);
      const targetId = typeof transition.target_node_id.toString === 'function' ?
        transition.target_node_id.toString() : String(transition.target_node_id);
      const sourceNode = nodeMap.get(sourceId)
      const targetNode = nodeMap.get(targetId)

      if (!sourceNode || !targetNode) {
        return null
      }

      // 确定连线状态
      let stroke = '#909399'
      let marker = 'url(#arrowhead)'

      if (sourceNode.isActive) {
        stroke = '#409EFF'
        marker = 'url(#arrowhead-active)'
      } else if (sourceNode.isCompleted && (targetNode.isCompleted || targetNode.isActive)) {
        stroke = '#67C23A'
        marker = 'url(#arrowhead-completed)'
      }

      // 创建曲线路径
      const startX = sourceNode.x + 60 // 节点宽度的一半
      const startY = sourceNode.y + 30 // 节点高度的一半
      const endX = targetNode.x
      const endY = targetNode.y + 30 // 节点高度的一半

      // 控制点
      const controlX = (startX + endX) / 2

      return {
        path: `M ${startX} ${startY} C ${controlX} ${startY}, ${controlX} ${endY}, ${endX} ${endY}`,
        stroke,
        marker
      }
    })
    .filter(Boolean) // 过滤掉空项
})

// 获取节点样式
const getNodeStyle = (node, index) => {
  const level = node.level || 0
  const x = 120 + level * 300 // 水平间距增加到300，提供更多空间
  const y = 80 + (index % 3) * 180 // 垂直间距增加到180，提供更多空间

  return {
    left: `${x}px`,
    top: `${y}px`
  }
}

// 计算节点和连线数据
const chartData = computed(() => {
  if (!props.workflowInstance || !props.workflowInstance.workflowTemplate) {
    console.warn('无效的工作流实例或模板数据')
    return { nodes: [], links: [] }
  }

  // 预处理数据
  const { nodes, transitions } = preprocessWorkflowData(props.workflowInstance)

  console.log('工作流节点数据:', nodes)
  console.log('工作流转换数据:', transitions)

  // 处理历史记录，获取已经完成的节点ID
  const completedNodeIds = new Set()
  if (props.workflowInstance.taskHistories && Array.isArray(props.workflowInstance.taskHistories)) {
    props.workflowInstance.taskHistories.forEach(history => {
      if (history.node_id) {
        completedNodeIds.add(history.node_id)
      }
    })
  }

  // 构建节点数据
  const nodeData = nodes
    .filter(node => node && node.id) // 确保节点有效且有ID
    .map(node => {
      // 确定节点形状和大小
      let symbol = 'circle'
      let symbolSize = 60

      // 设置最小节点宽度
      const minNodeWidth = 150 // 设置最小宽度为150px

      // 根据节点类型设置不同形状
      if (node.node_type === 'start') {
        symbol = 'circle'
        symbolSize = 60 // 增加开始节点大小
      } else if (node.node_type === 'end') {
        symbol = 'circle'
        symbolSize = 60 // 增加结束节点大小
      } else if (node.node_type === 'approval') {
        symbol = 'rect'
        symbolSize = [Math.max(minNodeWidth, node.name?.length * 12 || minNodeWidth), 60] // 根据文本长度动态调整宽度，但不小于最小宽度
      } else if (node.node_type === 'task') {
        symbol = 'rect'
        symbolSize = [Math.max(minNodeWidth, node.name?.length * 12 || minNodeWidth), 60] // 根据文本长度动态调整宽度，但不小于最小宽度
      } else if (node.node_type === 'condition') {
        symbol = 'diamond'
        symbolSize = 80 // 增加条件节点大小
      }

      // 确定节点状态
      let itemStyle = {}
      let category = 0 // 默认未处理

      if (node.id === currentNodeId.value) {
        // 当前节点
        category = 1
        itemStyle = {
          color: '#409EFF'
        }
      } else if (completedNodeIds.has(node.id)) {
        // 已完成节点
        category = 2
        itemStyle = {
          color: '#67C23A'
        }
      } else if (node.node_type === 'start') {
        // 开始节点
        category = 3
        itemStyle = {
          color: '#67C23A'
        }
      } else if (node.node_type === 'end') {
        // 结束节点
        category = 4
        itemStyle = {
          color: '#F56C6C'
        }
      } else if (node.node_type === 'approval') {
        // 审批节点
        category = 5
        itemStyle = {
          color: '#E6A23C'
        }
      } else if (node.node_type === 'condition') {
        // 条件节点
        category = 6
        itemStyle = {
          color: '#409EFF'
        }
      } else if (node.node_type === 'task') {
        // 任务节点
        category = 7
        itemStyle = {
          color: '#909399'
        }
      }

      return {
        id: node.id.toString(), // 确保ID是字符串
        name: node.name || '',
        symbol,
        symbolSize,
        itemStyle,
        category,
        tooltip: {
          formatter: () => {
            return `<div style="font-weight:bold">${node.name || ''}</div>
                    <div>类型: ${getNodeTypeText(node.node_type)}</div>
                    ${node.description ? `<div>描述: ${node.description}</div>` : ''}
                   `
          }
        }
      }
  })

  // 构建连线数据
  const linkData = []

  // 创建节点ID映射，用于快速查找
  const nodeMap = new Map()
  nodeData.forEach(node => {
    if (node && node.id) {
      nodeMap.set(node.id, node)
    }
  })

  transitions
    .filter(transition => transition && (transition.source_node_id || transition.sourceNode) && (transition.target_node_id || transition.targetNode))
    .forEach(transition => {
      // 确保转换数据有效
      if (!transition.source_node_id || !transition.target_node_id) {
        // 尝试从sourceNode和targetNode对象获取ID
        if (transition.sourceNode && transition.targetNode) {
          transition.source_node_id = transition.sourceNode.id || transition.sourceNode.node_id
          transition.target_node_id = transition.targetNode.id || transition.targetNode.node_id
        } else {
          console.warn('转换数据不完整:', transition)
          return // 跳过这个转换
        }
      }

      // 确保ID是字符串
      const sourceId = transition.source_node_id ? transition.source_node_id.toString() : ''
      const targetId = transition.target_node_id ? transition.target_node_id.toString() : ''

      if (!sourceId || !targetId) {
        console.warn('转换的源节点或目标节点ID无效:', {
          transitionId: transition.id,
          sourceId,
          targetId
        })
        return // 跳过这个转换
      }

      // 检查节点是否存在于处理后的节点数据中
      if (!nodeMap.has(sourceId) || !nodeMap.has(targetId)) {
        console.warn('找不到转换对应的节点:', {
          transitionId: transition.id,
          sourceId,
          targetId,
          foundSourceNode: nodeMap.has(sourceId),
          foundTargetNode: nodeMap.has(targetId)
        })
        return // 跳过这个转换
      }

      // 获取源节点和目标节点
      const sourceNode = nodes.find(node => node.id && node.id.toString() === sourceId) || {}
      const targetNode = nodes.find(node => node.id && node.id.toString() === targetId) || {}

      // 确定连线状态
      const isCompleted = completedNodeIds.has(sourceNode.id) &&
                         (completedNodeIds.has(targetNode.id) || targetNode.id === currentNodeId.value)

      // 设置不同状态的连线样式
      const lineStyle = {
        color: isCompleted ? '#67C23A' : '#909399',
        width: isCompleted ? 3 : 1.5,
        curveness: 0.3,  // 增加曲线度
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 3,
        shadowOffsetX: 1,
        shadowOffsetY: 1
      }

      // 当前连线特殊处理
      if (sourceNode.id === currentNodeId.value) {
        lineStyle.color = '#409EFF'
        lineStyle.width = 2.5
        lineStyle.type = 'dashed'
      }

      // 添加有效的连线
      linkData.push({
        source: sourceId,
        target: targetId,
        lineStyle,
        // 添加连线标签，显示条件
        label: {
          show: !!transition.condition,
          formatter: transition.condition ? getConditionText(transition.condition) : '',
          fontSize: 12
        },
        tooltip: {
          formatter: () => {
            return `<div>从: ${sourceNode.name || '未知'}</div>
                    <div>到: ${targetNode.name || '未知'}</div>
                    ${transition.condition ? `<div>条件: ${getConditionText(transition.condition)}</div>` : ''}
                   `
          }
        }
      })
  })

  console.log('生成的节点数据:', nodeData)
  console.log('生成的连线数据:', linkData)

  return {
    nodes: nodeData,
    links: linkData
  }
})

// 获取条件文本
const getConditionText = (condition) => {
  if (!condition) return ''

  try {
    if (typeof condition === 'string') {
      // 尝试解析JSON字符串
      const parsed = JSON.parse(condition)
      return formatCondition(parsed)
    } else {
      return formatCondition(condition)
    }
  } catch (e) {
    // 如果是简单字符串，直接返回
    if (condition && typeof condition !== 'undefined') {
      // 确保condition不是null或undefined
      try {
        return condition.toString();
      } catch (err) {
        return '';
      }
    }
    return '';
  }
}

// 格式化条件
const formatCondition = (condition) => {
  if (!condition) return ''

  if (condition.type && condition.type === 'approval') {
    return condition.value === 'approved' ? '同意' : '拒绝'
  } else if (condition.field) {
    let operator = ''
    switch (condition.operator) {
      case 'eq': operator = '='; break
      case 'neq': operator = '≠'; break
      case 'gt': operator = '>'; break
      case 'gte': operator = '≥'; break
      case 'lt': operator = '<'; break
      case 'lte': operator = '≤'; break
      case 'contains': operator = '包含'; break
      default: operator = condition.operator || ''
    }

    const value = condition.value !== undefined ? condition.value : ''
    return `${condition.field} ${operator} ${value}`
  }

  try {
    return JSON.stringify(condition)
  } catch (e) {
    return condition != null ? String(condition) : ''
  }
}

// 节点类型文本
const getNodeTypeText = (type) => {
  switch (type) {
    case 'start':
      return '开始节点'
    case 'end':
      return '结束节点'
    case 'approval':
      return '审批节点'
    case 'task':
      return '任务节点'
    case 'condition':
      return '条件节点'
    default:
      return type
  }
}

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return

  // 等待DOM更新
  await nextTick()

  if (chart) {
    chart.dispose()
  }

  // 创建图表实例
  chart = echarts.init(chartRef.value)

  try {
    // 确认数据是否有效
    if (chartData.value.nodes.length === 0) {
      console.warn('没有有效的节点数据可供可视化')
      // 显示无数据提示
      chart.setOption({
        title: {
          text: '暂无工作流节点数据',
          left: 'center',
          top: 'middle'
        }
      })
      return
    }

    // 验证节点数据的有效性
    const validNodes = chartData.value.nodes.filter(node =>
      node && typeof node.id === 'string' && node.id.trim() !== ''
    )

    // 验证连线数据的有效性
    const validLinks = chartData.value.links.filter(link =>
      link && typeof link.source === 'string' && typeof link.target === 'string' &&
      link.source.trim() !== '' && link.target.trim() !== ''
    )

    if (validNodes.length === 0) {
      console.warn('所有节点数据无效')
      chart.setOption({
        title: {
          text: '工作流节点数据无效',
          left: 'center',
          top: 'middle'
        }
      })
      return
    }

    console.log('有效节点数量:', validNodes.length)
    console.log('有效连线数量:', validLinks.length)

    // 图表分类
    const categories = [
      { name: '未处理' },
      { name: '当前节点' },
      { name: '已完成' },
      { name: '开始节点' },
      { name: '结束节点' },
      { name: '审批节点' },
      { name: '条件节点' },
      { name: '任务节点' }
    ]

    // 图表配置
    const option = {
      title: {
        text: '工作流流程图',
        top: 'top',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        confine: true,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#ccc',
        borderWidth: 1,
        padding: 10,
        textStyle: {
          color: '#333'
        }
      },
      toolbox: {
        feature: {
          saveAsImage: { title: '保存图片' },
          restore: { title: '还原' },
          dataZoom: { title: '区域缩放' },
          dataView: { title: '数据视图', readOnly: true }
        }
      },
      legend: {
        data: categories.map(a => a.name),
        orient: 'vertical',
        right: 10,
        top: 'center',
        textStyle: {
          fontSize: 12
        },
        itemWidth: 15,
        itemHeight: 10,
        itemGap: 10
      },
      grid: {
        left: 10,
        right: 10,
        top: 50,
        bottom: 10,
        containLabel: true
      },
      animationDuration: 1500,
      animationEasingUpdate: 'quinticInOut',
      series: [
        {
          name: '工作流',
          type: 'graph',
          layout: validLinks.length > 0 ? 'force' : 'circular', // 如果有连线用力导向布局，否则使用环形布局
          data: validNodes,
          links: validLinks,
          categories: categories,
          roam: true,
          draggable: true,
          initialLayout: 'circular', // 初始使用环形布局
          layoutIterations: 300,     // 增加布局迭代次数
          label: {
            show: true,
            position: 'inside',
            formatter: '{b}',
            fontSize: 12,
            color: '#fff',
            fontWeight: 'bold',
            overflow: 'break'
          },
          edgeLabel: {
            show: true,
            formatter: '{c}',
            fontSize: 10,
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            padding: [2, 4],
            borderRadius: 2
          },
          force: {
            repulsion: 1200,      // 进一步增强节点间排斥力，防止节点重叠
            edgeLength: 350,      // 增加连线长度，提供更多空间
            gravity: 0.03,        // 进一步减小向中心的引力，使节点分布更均匀
            layoutAnimation: true, // 启用布局动画
            friction: 0.08,       // 减小摩擦力，使节点更容易移动到最佳位置
            nodeGap: 50           // 设置节点之间的最小间距
          },
          emphasis: {
            focus: 'adjacency',
            scale: true,
            lineStyle: {
              width: 4
            }
          }
        }
      ]
    }

    // 渲染图表
    chart.setOption(option)
  } catch (error) {
    console.error('初始化图表时出错:', error)
    // 显示错误提示
    useStaticView.value = true
    // 如果图表已经创建，显示错误信息
    if (chart) {
      chart.setOption({
        title: {
          text: '图表渲染出错，已切换到静态视图',
          left: 'center',
          top: 'middle'
        }
      })
    }
  }

  // 自适应大小
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }
  })
}

// 监听工作流实例变化
watch(() => props.workflowInstance, () => {
  try {
    if (!useStaticView.value) {
      initChart()
    }
  } catch (error) {
    console.error('监听工作流实例变化时出错:', error)
    useStaticView.value = true
  }
}, { deep: true })

// 组件挂载时初始化图表
onMounted(() => {
  try {
    // 设置流程图容器的最小宽度
    if (chartRef.value) {
      const parentElement = chartRef.value.parentElement
      if (parentElement) {
        // 确保父容器有足够的宽度
        if (parentElement.offsetWidth < 1000) {
          parentElement.style.minWidth = '1000px' // 增加最小宽度
        }
      }
    }

    // 自动选择使用静态视图或图表
    const { nodes, transitions } = preprocessWorkflowData(props.workflowInstance)

    // 检查节点和转换数据的有效性
    const validNodes = nodes.filter(node => node && node.id)
    const validTransitions = transitions.filter(t =>
      t && t.source_node_id && t.target_node_id
    )

    console.log('挂载时有效节点数量:', validNodes.length)
    console.log('挂载时有效转换数量:', validTransitions.length)

    // 设置静态视图的宽度和高度
    const nodeCount = validNodes.length
    if (nodeCount > 10) {
      // 对于大型流程图，增加容器高度
      flowHeight.value = Math.max(600, 200 + nodeCount * 50)
    }

    if (validNodes.length === 0 || validTransitions.length === 0) {
      console.warn('工作流数据无效，使用静态视图')
      useStaticView.value = true
    } else {
      // 延迟初始化图表，确保DOM已完全渲染
      setTimeout(() => {
        initChart()

        // 初始化后再次调整大小，确保图表填满容器
        if (chart) {
          chart.resize()
        }
      }, 100)
    }
  } catch (error) {
    console.error('组件挂载时出错:', error)
    useStaticView.value = true
  }

  // 自适应大小
  const resizeHandler = () => {
    if (chart && !useStaticView.value) {
      try {
        chart.resize()
      } catch (error) {
        console.error('调整图表大小时出错:', error)
      }
    }
  }

  window.addEventListener('resize', resizeHandler)

  // 在组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', resizeHandler)
    if (chart) {
      chart.dispose()
      chart = null
    }
  })
})
</script>

<style scoped>
.workflow-visualizer {
  width: 100%;
  position: relative;
}

.chart-container {
  width: 100%;
  min-width: 1000px; /* 增加最小宽度 */
  height: 700px; /* 增加高度以适应更大的节点间距 */
}

.static-flow {
  width: 100%;
  min-width: 1000px; /* 增加最小宽度 */
  min-height: 700px; /* 增加最小高度与其他容器保持一致 */
  position: relative;
}

.static-flow-container {
  width: 100%;
  min-width: 1000px; /* 增加最小宽度 */
  height: 700px; /* 增加高度以适应更大的垂直间距 */
  position: relative;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  background-image: radial-gradient(#dcdfe6 1px, transparent 0);
  background-size: 20px 20px;
  padding: 20px;
}

.flow-node {
  position: absolute;
  width: 150px; /* 增加节点宽度与ECharts节点保持一致 */
  height: 70px;
  border: 2px solid #909399;
  border-radius: 4px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.flow-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  z-index: 3;
}

.node-content {
  text-align: center;
  padding: 5px;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flow-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.flow-node.active {
  border-color: #409EFF;
  background-color: #ecf5ff;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.6);
  z-index: 4;
}

.flow-node.completed {
  border-color: #67C23A;
  background-color: #f0f9eb;
}

.node-type-start {
  border-color: #67C23A;
  background-color: #f0f9eb;
  border-radius: 30px;
}

.node-type-end {
  border-color: #F56C6C;
  background-color: #fef0f0;
  border-radius: 30px;
}

.node-type-approval {
  border-color: #E6A23C;
  background-color: #fdf6ec;
}

.node-type-condition {
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: rotate(45deg);
  width: 70px;
  height: 70px;
}

.node-type-condition .node-content {
  transform: rotate(-45deg);
}

.node-type-task {
  border-color: #909399;
  background-color: #f4f4f5;
}

.static-flow-controls, .visualizer-controls {
  margin-top: 10px;
  text-align: right;
}

.visualizer-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>