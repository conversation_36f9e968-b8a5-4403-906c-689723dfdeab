// @ts-check
const { defineConfig, devices } = require('@playwright/test');
const path = require('path');

/**
 * @see https://playwright.dev/docs/test-configuration
 */
module.exports = defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [['html'], ['list', { printSteps: true }]],

  // Global setup and teardown (disabled for now)
  // globalSetup: require.resolve('./global-setup'),
  // globalTeardown: require.resolve('./global-teardown'),

  // Timeout settings
  timeout: 60000, // 60 seconds per test
  expect: {
    timeout: 10000 // 10 seconds per assertion
  },

  use: {
    baseURL: 'http://localhost:5278',
    headless: true, // Run in headless mode
    screenshot: 'only-on-failure',
    video: 'on-first-retry',

    // Collect trace for each test
    trace: 'retain-on-failure',

    // Viewport size
    viewport: { width: 1280, height: 720 },

    // Automatically wait for elements
    actionTimeout: 15000,
  },

  projects: [
    // Setup project
    {
      name: 'setup',
      testMatch: /global\.setup\.js/,
    },

    // Tests with admin user
    {
      name: 'admin',
      use: {
        ...devices['Desktop Chrome'],
        storageState: path.join(__dirname, 'auth', 'admin.json'),
      },
      dependencies: ['setup'],
      testIgnore: /.*\.user\.spec\.js/,
    },

    // Tests with regular user
    {
      name: 'user',
      use: {
        ...devices['Desktop Chrome'],
        storageState: path.join(__dirname, 'auth', 'test-user.json'),
      },
      dependencies: ['setup'],
      testMatch: /.*\.user\.spec\.js/,
    },

    // Mobile tests
    {
      name: 'mobile',
      use: {
        ...devices['iPhone 12'],
        storageState: path.join(__dirname, 'auth', 'admin.json'),
      },
      dependencies: ['setup'],
      testMatch: /.*\.mobile\.spec\.js/,
    },
  ],

  // Comment out webServer config since we're running the servers manually
  // webServer: {
  //   command: 'cd ../frontend && npm run dev & cd ../backend && npm start',
  //   url: 'http://localhost:5278',
  //   reuseExistingServer: !process.env.CI,
  //   timeout: 120 * 1000, // 120 seconds
  // },
});
