# Test info

- Name: verify test environment is properly set up
- Location: D:\source\jiuan\workflow-system-final\e2e\tests\global.setup.js:9:1

# Error details

```
Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5278/
Call log:
  - navigating to "http://localhost:5278/", waiting until "load"

    at D:\source\jiuan\workflow-system-final\e2e\tests\global.setup.js:13:14
```

# Test source

```ts
   1 | /**
   2 |  * Global setup test
   3 |  * This test runs before other tests to verify the test environment is properly set up
   4 |  */
   5 |
   6 | const { test, expect } = require('@playwright/test');
   7 | const TestDataManager = require('../utils/test-data-manager');
   8 |
   9 | test('verify test environment is properly set up', async ({ page }) => {
  10 |   console.log('Verifying test environment...');
  11 |   
  12 |   // Navigate to home page
> 13 |   await page.goto('/');
     |              ^ Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5278/
  14 |   
  15 |   // Verify we're logged in as admin
  16 |   const userInfo = await page.locator('.user-info, .user-profile').textContent();
  17 |   expect(userInfo).toContain('管理员');
  18 |   
  19 |   // Verify E2E test data exists
  20 |   
  21 |   // 1. Check if E2E Test Department exists
  22 |   await page.goto('/department');
  23 |   await expect(page.locator('.el-table__row').filter({ hasText: 'E2E Test Department' })).toBeVisible();
  24 |   
  25 |   // 2. Check if E2E Test Form exists
  26 |   await page.goto('/form-design');
  27 |   await expect(page.locator('.el-table__row').filter({ hasText: 'E2E Test Form' })).toBeVisible();
  28 |   
  29 |   // 3. Check if E2E Test Workflow exists
  30 |   await page.goto('/workflow-design');
  31 |   await expect(page.locator('.el-table__row').filter({ hasText: 'E2E Test Workflow' })).toBeVisible();
  32 |   
  33 |   console.log('Test environment verification completed successfully');
  34 | });
  35 |
```