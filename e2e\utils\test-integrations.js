/**
 * Test Integration Service
 * Integrates test results with external systems
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class TestIntegrations {
  constructor(config = {}) {
    this.config = {
      jira: config.jira || null,
      slack: config.slack || null,
      teams: config.teams || null,
      email: config.email || null
    };
  }

  /**
   * Send test results to Jira
   * @param {object} testResults - Test results
   * @param {object} options - Options
   * @returns {Promise<object>} - Response
   */
  async sendToJira(testResults, options = {}) {
    if (!this.config.jira) {
      throw new Error('Jira configuration not provided');
    }
    
    const { url, apiToken, username, project } = this.config.jira;
    
    // Create issue data
    const issueData = {
      fields: {
        project: {
          key: project
        },
        summary: options.summary || `Test Run Results: ${testResults.success ? 'Success' : 'Failure'}`,
        description: this.formatJiraDescription(testResults),
        issuetype: {
          name: options.issueType || 'Test Result'
        },
        labels: options.labels || ['automated-test']
      }
    };
    
    try {
      // Create issue
      const response = await axios({
        method: 'post',
        url: `${url}/rest/api/2/issue`,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${Buffer.from(`${username}:${apiToken}`).toString('base64')}`
        },
        data: issueData
      });
      
      // Attach report if available
      if (options.reportPath && fs.existsSync(options.reportPath)) {
        const issueKey = response.data.key;
        const reportContent = fs.readFileSync(options.reportPath);
        
        await axios({
          method: 'post',
          url: `${url}/rest/api/2/issue/${issueKey}/attachments`,
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Basic ${Buffer.from(`${username}:${apiToken}`).toString('base64')}`,
            'X-Atlassian-Token': 'no-check'
          },
          data: {
            file: {
              value: reportContent,
              options: {
                filename: path.basename(options.reportPath)
              }
            }
          }
        });
      }
      
      return response.data;
    } catch (error) {
      console.error('Failed to send to Jira:', error.message);
      throw error;
    }
  }

  /**
   * Format Jira description
   * @param {object} testResults - Test results
   * @returns {string} - Formatted description
   */
  formatJiraDescription(testResults) {
    return `
h2. Test Results

* *Status:* ${testResults.success ? '✅ Success' : '❌ Failure'}
* *Total Tests:* ${testResults.total}
* *Passed:* ${testResults.passed}
* *Failed:* ${testResults.failed}
* *Skipped:* ${testResults.skipped}
* *Duration:* ${testResults.duration / 1000} seconds

h2. Failed Tests

${testResults.failedTests && testResults.failedTests.length > 0 
  ? testResults.failedTests.map(test => `* ${test.title} - ${test.error}`).join('\n') 
  : 'No failed tests'}
    `;
  }

  /**
   * Send test results to Slack
   * @param {object} testResults - Test results
   * @param {object} options - Options
   * @returns {Promise<object>} - Response
   */
  async sendToSlack(testResults, options = {}) {
    if (!this.config.slack) {
      throw new Error('Slack configuration not provided');
    }
    
    const { webhookUrl, channel } = this.config.slack;
    
    // Create message
    const message = {
      channel: channel,
      text: options.text || `Test Run Results: ${testResults.success ? 'Success' : 'Failure'}`,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: options.title || `Test Run Results: ${testResults.success ? 'Success' : 'Failure'}`
          }
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Status:* ${testResults.success ? ':white_check_mark: Success' : ':x: Failure'}`
            },
            {
              type: 'mrkdwn',
              text: `*Duration:* ${(testResults.duration / 1000).toFixed(2)} seconds`
            },
            {
              type: 'mrkdwn',
              text: `*Total Tests:* ${testResults.total}`
            },
            {
              type: 'mrkdwn',
              text: `*Passed:* ${testResults.passed}`
            },
            {
              type: 'mrkdwn',
              text: `*Failed:* ${testResults.failed}`
            },
            {
              type: 'mrkdwn',
              text: `*Skipped:* ${testResults.skipped}`
            }
          ]
        }
      ]
    };
    
    // Add failed tests if any
    if (testResults.failedTests && testResults.failedTests.length > 0) {
      message.blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Failed Tests:*'
        }
      });
      
      const failedTestsText = testResults.failedTests
        .map(test => `• *${test.title}*\n  ${test.error}`)
        .join('\n');
      
      message.blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: failedTestsText
        }
      });
    }
    
    // Add report link if available
    if (options.reportUrl) {
      message.blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `<${options.reportUrl}|View Full Report>`
        }
      });
    }
    
    try {
      const response = await axios({
        method: 'post',
        url: webhookUrl,
        headers: {
          'Content-Type': 'application/json'
        },
        data: message
      });
      
      return response.data;
    } catch (error) {
      console.error('Failed to send to Slack:', error.message);
      throw error;
    }
  }

  /**
   * Send test results to Microsoft Teams
   * @param {object} testResults - Test results
   * @param {object} options - Options
   * @returns {Promise<object>} - Response
   */
  async sendToTeams(testResults, options = {}) {
    if (!this.config.teams) {
      throw new Error('Teams configuration not provided');
    }
    
    const { webhookUrl } = this.config.teams;
    
    // Create message
    const message = {
      '@type': 'MessageCard',
      '@context': 'http://schema.org/extensions',
      themeColor: testResults.success ? '00FF00' : 'FF0000',
      summary: options.summary || `Test Run Results: ${testResults.success ? 'Success' : 'Failure'}`,
      sections: [
        {
          activityTitle: options.title || `Test Run Results: ${testResults.success ? 'Success' : 'Failure'}`,
          facts: [
            {
              name: 'Status',
              value: testResults.success ? 'Success' : 'Failure'
            },
            {
              name: 'Duration',
              value: `${(testResults.duration / 1000).toFixed(2)} seconds`
            },
            {
              name: 'Total Tests',
              value: testResults.total.toString()
            },
            {
              name: 'Passed',
              value: testResults.passed.toString()
            },
            {
              name: 'Failed',
              value: testResults.failed.toString()
            },
            {
              name: 'Skipped',
              value: testResults.skipped.toString()
            }
          ]
        }
      ]
    };
    
    // Add failed tests if any
    if (testResults.failedTests && testResults.failedTests.length > 0) {
      message.sections.push({
        title: 'Failed Tests',
        text: testResults.failedTests
          .map(test => `- **${test.title}**: ${test.error}`)
          .join('\n')
      });
    }
    
    // Add report link if available
    if (options.reportUrl) {
      message.potentialAction = [
        {
          '@type': 'OpenUri',
          name: 'View Full Report',
          targets: [
            {
              os: 'default',
              uri: options.reportUrl
            }
          ]
        }
      ];
    }
    
    try {
      const response = await axios({
        method: 'post',
        url: webhookUrl,
        headers: {
          'Content-Type': 'application/json'
        },
        data: message
      });
      
      return response.data;
    } catch (error) {
      console.error('Failed to send to Teams:', error.message);
      throw error;
    }
  }

  /**
   * Send test results via email
   * @param {object} testResults - Test results
   * @param {object} options - Options
   * @returns {Promise<object>} - Response
   */
  async sendEmail(testResults, options = {}) {
    if (!this.config.email) {
      throw new Error('Email configuration not provided');
    }
    
    const { apiKey, from, to } = this.config.email;
    
    // Create email data
    const emailData = {
      from: from,
      to: options.to || to,
      subject: options.subject || `Test Run Results: ${testResults.success ? 'Success' : 'Failure'}`,
      html: this.formatEmailHtml(testResults, options)
    };
    
    // Add attachments if available
    if (options.reportPath && fs.existsSync(options.reportPath)) {
      emailData.attachments = [
        {
          content: fs.readFileSync(options.reportPath, { encoding: 'base64' }),
          filename: path.basename(options.reportPath),
          type: 'text/html',
          disposition: 'attachment'
        }
      ];
    }
    
    try {
      // Using SendGrid API as an example
      const response = await axios({
        method: 'post',
        url: 'https://api.sendgrid.com/v3/mail/send',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        data: {
          personalizations: [
            {
              to: [{ email: emailData.to }]
            }
          ],
          from: { email: emailData.from },
          subject: emailData.subject,
          content: [
            {
              type: 'text/html',
              value: emailData.html
            }
          ],
          attachments: emailData.attachments
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Failed to send email:', error.message);
      throw error;
    }
  }

  /**
   * Format email HTML
   * @param {object} testResults - Test results
   * @param {object} options - Options
   * @returns {string} - Formatted HTML
   */
  formatEmailHtml(testResults, options = {}) {
    return `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    h1 { color: #2c3e50; }
    h2 { color: #3498db; margin-top: 20px; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .success { color: #27ae60; }
    .failure { color: #e74c3c; }
    .warning { color: #f39c12; }
    .error-details { background-color: #ffeeee; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 10px; }
  </style>
</head>
<body>
  <h1>Test Run Results: <span class="${testResults.success ? 'success' : 'failure'}">${testResults.success ? 'Success' : 'Failure'}</span></h1>
  
  <h2>Summary</h2>
  <table>
    <tr>
      <th>Metric</th>
      <th>Value</th>
    </tr>
    <tr>
      <td>Status</td>
      <td class="${testResults.success ? 'success' : 'failure'}">${testResults.success ? 'Success' : 'Failure'}</td>
    </tr>
    <tr>
      <td>Duration</td>
      <td>${(testResults.duration / 1000).toFixed(2)} seconds</td>
    </tr>
    <tr>
      <td>Total Tests</td>
      <td>${testResults.total}</td>
    </tr>
    <tr>
      <td>Passed</td>
      <td class="success">${testResults.passed}</td>
    </tr>
    <tr>
      <td>Failed</td>
      <td class="failure">${testResults.failed}</td>
    </tr>
    <tr>
      <td>Skipped</td>
      <td class="warning">${testResults.skipped}</td>
    </tr>
  </table>
  
  ${testResults.failedTests && testResults.failedTests.length > 0 ? `
  <h2>Failed Tests</h2>
  ${testResults.failedTests.map(test => `
  <div class="error-details">
    <h3>${test.title}</h3>
    <p>${test.error}</p>
  </div>
  `).join('')}
  ` : ''}
  
  ${options.reportUrl ? `
  <p><a href="${options.reportUrl}">View Full Report</a></p>
  ` : ''}
</body>
</html>
    `;
  }
}

module.exports = TestIntegrations;
