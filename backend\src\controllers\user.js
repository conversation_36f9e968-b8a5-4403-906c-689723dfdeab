const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { User, Role, Sequelize } = require('../models');

// 用户注册
exports.register = async (req, res) => {
  try {
    const { username, password, email, full_name, phone } = req.body;

    // 检查用户名和邮箱是否已存在
    const existingUser = await User.findOne({
      where: {
        [Sequelize.Op.or]: [
          { username },
          { email }
        ]
      }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名或邮箱已存在'
      });
    }

    // 加密密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // 创建用户
    const user = await User.create({
      username,
      password: hashedPassword,
      email,
      full_name,
      phone,
      status: 'active'
    });

    // 分配默认角色
    const defaultRole = await Role.findOne({ where: { name: 'user' } });
    if (defaultRole) {
      await user.addRole(defaultRole);
    }

    res.status(201).json({
      success: true,
      message: '用户注册成功',
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name
      }
    });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 用户登录
exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;

    // 查找用户
    const user = await User.findOne({
      where: { username },
      include: [{ model: Role, as: 'roles' }]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码不正确'
      });
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '账户已被禁用，请联系管理员'
      });
    }

    // 验证密码
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码不正确'
      });
    }

    // 生成JWT令牌
    const payload = {
      user: {
        id: user.id,
        username: user.username,
        roles: user.roles.map(role => role.name)
      }
    };

    const token = jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    // 更新最后登录时间
    await user.update({ last_login: new Date() });

    res.json({
      success: true,
      message: '登录成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        roles: user.roles.map(role => role.name)
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取当前用户信息
exports.getCurrentUser = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      include: [{ model: Role, as: 'roles' }],
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        avatar: user.avatar,
        phone: user.phone,
        status: user.status,
        last_login: user.last_login,
        roles: user.roles.map(role => ({
          id: role.id,
          name: role.name,
          description: role.description
        }))
      }
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 更新用户信息
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { full_name, email, phone, avatar } = req.body;

    // 检查权限
    if (req.user.id !== parseInt(id) && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新用户信息
    await user.update({
      full_name: full_name || user.full_name,
      email: email || user.email,
      phone: phone || user.phone,
      avatar: avatar || user.avatar
    });

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        avatar: user.avatar,
        phone: user.phone,
        status: user.status
      }
    });
  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取所有用户
exports.getAllUsers = async (req, res) => {
  try {
    console.log('User model:', User);
    console.log('Role model:', Role);

    // 简化查询，先不使用关联和权限检查
    const users = await User.findAll({
      attributes: { exclude: ['password'] }
    });

    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('获取所有用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取单个用户
exports.getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      include: [{ model: Role, as: 'roles' }],
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        avatar: user.avatar,
        phone: user.phone,
        status: user.status,
        last_login: user.last_login,
        roles: user.roles.map(role => ({
          id: role.id,
          name: role.name,
          description: role.description
        }))
      }
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 删除用户
exports.deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    await user.destroy();

    res.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    console.error('删除用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 修改密码
exports.changePassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { current_password, new_password } = req.body;

    // 检查权限
    if (req.user.id !== parseInt(id) && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 如果不是管理员，需要验证当前密码
    if (req.user.id === parseInt(id)) {
      const isMatch = await bcrypt.compare(current_password, user.password);
      if (!isMatch) {
        return res.status(400).json({
          success: false,
          message: '当前密码不正确'
        });
      }
    }

    // 加密新密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(new_password, salt);

    // 更新密码
    await user.update({ password: hashedPassword });

    res.json({
      success: true,
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('修改密码错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 分配角色
exports.assignRoles = async (req, res) => {
  try {
    const { id } = req.params;
    const { role_ids } = req.body;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 查找角色
    const roles = await Role.findAll({
      where: {
        id: {
          [Sequelize.Op.in]: role_ids
        }
      }
    });

    if (roles.length !== role_ids.length) {
      return res.status(400).json({
        success: false,
        message: '部分角色不存在'
      });
    }

    // 设置用户角色
    await user.setRoles(roles);

    // 获取更新后的用户信息
    const updatedUser = await User.findByPk(id, {
      include: [{ model: Role, as: 'roles' }],
      attributes: { exclude: ['password'] }
    });

    res.json({
      success: true,
      message: '角色分配成功',
      data: {
        id: updatedUser.id,
        username: updatedUser.username,
        roles: updatedUser.roles.map(role => ({
          id: role.id,
          name: role.name
        }))
      }
    });
  } catch (error) {
    console.error('分配角色错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 管理员重置用户密码
exports.resetPassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { new_password } = req.body;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 加密新密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(new_password, salt);

    // 更新密码
    await user.update({ password: hashedPassword });

    res.json({
      success: true,
      message: '密码重置成功'
    });
  } catch (error) {
    console.error('重置密码错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};
