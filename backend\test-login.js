/**
 * Test login script
 */
const axios = require('axios');

async function testLogin(username, password) {
  try {
    console.log(`Attempting to login with username: ${username}`);
    
    const response = await axios.post('http://localhost:3001/api/users/login', {
      username,
      password
    });
    
    console.log('Login successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('Login failed!');
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
    
    return null;
  }
}

// Test login with rsb user
testLogin('rsb', 'password123')
  .then(() => {
    console.log('\n--- Testing with incorrect password ---');
    return testLogin('rsb', 'wrongpassword');
  })
  .then(() => {
    console.log('\n--- Testing with admin user ---');
    return testLogin('admin', 'admin123');
  })
  .catch(error => {
    console.error('Error in test sequence:', error);
  });
