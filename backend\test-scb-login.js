/**
 * Test SCB login script
 */
const axios = require('axios');

async function testScbLogin() {
  try {
    console.log('Attempting to login with username: scb');
    
    const response = await axios.post('http://localhost:3001/api/users/login', {
      username: 'scb',
      password: 'password123'
    });
    
    console.log('Login successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('Login failed!');
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
    
    return null;
  }
}

// Test login with scb user
testScbLogin();
