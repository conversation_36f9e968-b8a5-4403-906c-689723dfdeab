const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     WorkflowTask:
 *       type: object
 *       required:
 *         - workflow_instance_id
 *         - node_id
 *       properties:
 *         id:
 *           type: integer
 *           description: 工作流任务ID
 *           example: 1
 *         workflow_instance_id:
 *           type: integer
 *           description: 工作流实例ID
 *           example: 1
 *         node_id:
 *           type: integer
 *           description: 节点ID
 *           example: 2
 *         assignee_id:
 *           type: integer
 *           description: 指派用户ID
 *           example: 3
 *         assignee_role_id:
 *           type: integer
 *           description: 指派角色ID
 *           example: 2
 *         assignee_department_id:
 *           type: integer
 *           description: 指派部门ID
 *           example: 1
 *         status:
 *           type: string
 *           description: 任务状态
 *           enum: [pending, completed, cancelled]
 *           example: "pending"
 *         priority:
 *           type: string
 *           description: 任务优先级
 *           enum: [low, normal, high, urgent]
 *           example: "normal"
 *         due_date:
 *           type: string
 *           format: date-time
 *           description: 截止日期
 *           example: "2023-06-05T00:00:00Z"
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class WorkflowTask extends Model {
    static associate(models) {
      // 工作流实例
      WorkflowTask.belongsTo(models.WorkflowInstance, {
        foreignKey: 'workflow_instance_id',
        as: 'workflowInstance'
      });

      // 节点
      WorkflowTask.belongsTo(models.WorkflowNode, {
        foreignKey: 'node_id',
        as: 'node'
      });

      // 指派用户
      WorkflowTask.belongsTo(models.User, {
        foreignKey: 'assignee_id',
        as: 'assignee'
      });

      // 指派角色
      if (models.Role) {
        WorkflowTask.belongsTo(models.Role, {
          foreignKey: 'assignee_role_id',
          as: 'assigneeRole'
        });
      }

      // 指派部门
      if (models.Department) {
        WorkflowTask.belongsTo(models.Department, {
          foreignKey: 'assignee_department_id',
          as: 'assigneeDepartment'
        });
      }

      // 任务历史
      if (models.WorkflowTaskHistory) {
        WorkflowTask.hasMany(models.WorkflowTaskHistory, {
          foreignKey: 'task_id',
          as: 'histories'
        });
      }
    }
  }

  WorkflowTask.init({
    workflow_instance_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_instances',
        key: 'id'
      }
    },
    node_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_nodes',
        key: 'id'
      }
    },
    assignee_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    assignee_role_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'roles',
        key: 'id'
      }
    },
    assignee_department_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'departments',
        key: 'id'
      }
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'pending'
    },
    priority: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: 'normal'
    },
    due_date: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'WorkflowTask',
    tableName: 'workflow_tasks',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return WorkflowTask;
};
