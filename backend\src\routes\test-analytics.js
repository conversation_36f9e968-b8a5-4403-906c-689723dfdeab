const express = require('express');
const router = express.Router();
const testAnalyticsController = require('../controllers/test-analytics.controller');

/**
 * @swagger
 * /api/test-analytics:
 *   get:
 *     summary: 获取测试分析数据
 *     tags: [测试分析]
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *       - in: query
 *         name: testType
 *         schema:
 *           type: string
 *         description: 测试类型
 *     responses:
 *       200:
 *         description: 成功获取测试分析数据
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 */
router.get('/', testAnalyticsController.getTestAnalytics);

/**
 * @swagger
 * /api/test-analytics/summary:
 *   get:
 *     summary: 获取测试分析摘要
 *     tags: [测试分析]
 *     responses:
 *       200:
 *         description: 成功获取测试分析摘要
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 */
router.get('/summary', testAnalyticsController.getTestSummary);

/**
 * @swagger
 * /api/test-analytics/runs:
 *   get:
 *     summary: 获取测试运行列表
 *     tags: [测试分析]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: testType
 *         schema:
 *           type: string
 *         description: 测试类型
 *     responses:
 *       200:
 *         description: 成功获取测试运行列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 */
router.get('/runs', testAnalyticsController.getTestRuns);

/**
 * @swagger
 * /api/test-analytics/runs/{id}:
 *   get:
 *     summary: 获取测试运行详情
 *     tags: [测试分析]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: 测试运行ID
 *     responses:
 *       200:
 *         description: 成功获取测试运行详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 */
router.get('/runs/:id', testAnalyticsController.getTestRunById);

/**
 * @swagger
 * /api/test-analytics/runs:
 *   post:
 *     summary: 提交测试运行结果
 *     tags: [测试分析]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       201:
 *         description: 成功提交测试运行结果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 */
router.post('/runs', testAnalyticsController.createTestRun);

/**
 * @swagger
 * /api/test-analytics/trends:
 *   get:
 *     summary: 获取测试趋势数据
 *     tags: [测试分析]
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *         description: 天数
 *       - in: query
 *         name: testType
 *         schema:
 *           type: string
 *         description: 测试类型
 *     responses:
 *       200:
 *         description: 成功获取测试趋势数据
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 */
router.get('/trends', testAnalyticsController.getTestTrends);

module.exports = router;
