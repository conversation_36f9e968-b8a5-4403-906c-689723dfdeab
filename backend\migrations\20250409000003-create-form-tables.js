'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 创建表单模板表
    await queryInterface.createTable('form_templates', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      title: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      creator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      version: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1
      },
      status: {
        type: Sequelize.STRING(20),
        defaultValue: 'draft'
      },
      schema: {
        type: Sequelize.JSONB,
        allowNull: false
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 创建表单模板版本表
    await queryInterface.createTable('form_template_versions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      form_template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'form_templates',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      version: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      schema: {
        type: Sequelize.JSONB,
        allowNull: false
      },
      creator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 添加唯一约束
    await queryInterface.addConstraint('form_template_versions', {
      fields: ['form_template_id', 'version'],
      type: 'unique',
      name: 'unique_form_template_version'
    });

    // 创建表单字段表
    await queryInterface.createTable('form_fields', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      form_template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'form_templates',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      field_key: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      field_type: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      label: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      placeholder: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      default_value: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      options: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      validation_rules: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      is_required: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      order_index: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 添加唯一约束
    await queryInterface.addConstraint('form_fields', {
      fields: ['form_template_id', 'field_key'],
      type: 'unique',
      name: 'unique_form_field_key'
    });

    // 创建索引
    await queryInterface.addIndex('form_templates', ['creator_id'], {
      name: 'idx_form_templates_creator_id'
    });
    await queryInterface.addIndex('form_templates', ['status'], {
      name: 'idx_form_templates_status'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('form_fields');
    await queryInterface.dropTable('form_template_versions');
    await queryInterface.dropTable('form_templates');
  }
};
