<template>
  <div class="department-container">
    <el-tabs v-model="activeTab" class="mb-4">
      <el-tab-pane label="列表视图" name="list">
        <el-row :gutter="20" class="mb-4">
          <el-col :span="24">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>部门管理</span>
                  <el-button
                    type="primary"
                    @click="handleAddDepartment"
                    v-if="hasPermission">
                    添加部门
                  </el-button>
                </div>
              </template>

              <el-table
                :data="departments"
                row-key="id"
                border
                default-expand-all
                :tree-props="{ children: 'children' }">
                <el-table-column prop="name" label="部门名称" width="180" />
                <el-table-column prop="code" label="部门代码" width="120" />
                <el-table-column label="部门负责人" width="150">
                  <template #default="scope">
                    {{ scope.row.manager ? scope.row.manager.full_name : '未设置' }}
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                      {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="250" fixed="right">
                  <template #default="scope">
                    <el-button
                      size="small"
                      @click="handleViewDepartment(scope.row)"
                      type="primary"
                      plain>
                      查看
                    </el-button>
                    <el-button
                      size="small"
                      @click="handleEditDepartment(scope.row)"
                      type="primary"
                      v-if="hasPermission">
                      编辑
                    </el-button>
                    <el-button
                      size="small"
                      @click="handleManageMembers(scope.row)"
                      type="warning"
                      v-if="hasPermission">
                      成员
                    </el-button>
                    <el-button
                      size="small"
                      type="danger"
                      @click="handleDeleteDepartment(scope.row)"
                      v-if="hasPermission">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="架构视图" name="chart">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>组织架构图</span>
              <div>
                <el-button type="primary" @click="fetchDepartmentsWithDetails">
                  <el-icon><i-ep-refresh /></el-icon> 刷新数据
                </el-button>
              </div>
            </div>
          </template>

          <DepartmentChart :departments="departmentsWithDetails" />
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 部门表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加部门' : '编辑部门'"
      width="500px">
      <el-form
        :model="departmentForm"
        :rules="rules"
        ref="departmentFormRef"
        label-width="100px">
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="departmentForm.name" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="部门代码" prop="code">
          <el-input v-model="departmentForm.code" placeholder="请输入部门代码" />
        </el-form-item>
        <el-form-item label="上级部门">
          <el-select
            v-model="departmentForm.parent_id"
            placeholder="请选择上级部门"
            clearable
            style="width: 100%">
            <el-option
              v-for="dept in flatDepartments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
              :disabled="dialogType === 'edit' && dept.id === departmentForm.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="部门负责人">
          <el-select
            v-model="departmentForm.manager_id"
            placeholder="请选择部门负责人"
            clearable
            style="width: 100%">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.full_name"
              :value="user.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="departmentForm.description"
            type="textarea"
            placeholder="请输入部门描述" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="departmentForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDepartmentForm" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 部门成员管理对话框 -->
    <el-dialog
      v-model="memberDialogVisible"
      title="部门成员管理"
      width="700px">
      <div v-if="currentDepartment">
        <h3>{{ currentDepartment.name }} - 成员列表</h3>

        <el-card class="mb-4">
          <template #header>
            <div class="card-header">
              <span>添加成员</span>
            </div>
          </template>

          <el-form :model="memberForm" :rules="memberRules" ref="memberFormRef" label-width="100px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="选择用户" prop="user_id">
                  <el-select
                    v-model="memberForm.user_id"
                    placeholder="请选择要添加的用户"
                    filterable
                    style="width: 100%">
                    <el-option
                      v-for="user in availableUsers"
                      :key="user.id"
                      :label="user.full_name"
                      :value="user.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职位" prop="position">
                  <el-input v-model="memberForm.position" placeholder="请输入职位"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="是否主部门">
                  <el-switch v-model="memberForm.is_primary"></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="text-right">
                <el-button
                  type="primary"
                  @click="handleAddMember"
                  :disabled="!memberForm.user_id">
                  添加成员
                </el-button>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <el-table :data="departmentMembers" border>
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="full_name" label="姓名" width="120" />
          <el-table-column prop="email" label="邮箱" />
          <el-table-column label="主部门" width="80">
            <template #default="scope">
              <el-tag v-if="scope.row.department_users && scope.row.department_users.is_primary">
                是
              </el-tag>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="职位" width="120">
            <template #default="scope">
              {{ scope.row.department_users && scope.row.department_users.position ? scope.row.department_users.position : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button
                size="small"
                type="primary"
                @click="handleEditMember(scope.row)">
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleRemoveMember(scope.row)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 编辑成员对话框 -->
    <el-dialog
      v-model="editMemberDialogVisible"
      title="编辑部门成员"
      width="500px">
      <el-form :model="editMemberForm" :rules="memberRules" ref="editMemberFormRef" label-width="100px">
        <el-form-item label="用户">
          <el-input v-model="editMemberForm.full_name" disabled></el-input>
        </el-form-item>
        <el-form-item label="职位" prop="position">
          <el-input v-model="editMemberForm.position" placeholder="请输入职位"></el-input>
        </el-form-item>
        <el-form-item label="是否主部门">
          <el-switch v-model="editMemberForm.is_primary"></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editMemberDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateMember" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { departmentApi, userApi } from '@/api'
import DepartmentChart from '@/components/DepartmentChart.vue'

// 数据
const activeTab = ref('list')
const departments = ref([])
const departmentsWithDetails = ref([])
const users = ref([])
const departmentMembers = ref([])
const flatDepartments = ref([])
const currentDepartment = ref(null)
const dialogVisible = ref(false)
const memberDialogVisible = ref(false)
const editMemberDialogVisible = ref(false)
const dialogType = ref('add') // 'add' or 'edit'
const submitting = ref(false)

// 成员表单
const memberFormRef = ref(null)
const editMemberFormRef = ref(null)
const memberForm = reactive({
  user_id: null,
  position: '',
  is_primary: false
})
const editMemberForm = reactive({
  user_id: null,
  full_name: '',
  position: '',
  is_primary: false
})

// 表单
const departmentFormRef = ref(null)
const departmentForm = reactive({
  name: '',
  code: '',
  parent_id: null,
  manager_id: null,
  description: '',
  status: 'active'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入部门代码', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9_-]+$/, message: '只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ]
}

// 成员表单验证规则
const memberRules = {
  user_id: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  position: [
    { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' }
  ]
}

// 权限检查
const hasPermission = computed(() => {
  const user = JSON.parse(localStorage.getItem('user') || '{}')
  return user.roles && user.roles.includes('admin')
})

// 可添加的用户列表（排除已在部门中的用户）
const availableUsers = computed(() => {
  const memberIds = departmentMembers.value.map(member => member.id)
  return users.value.filter(user => !memberIds.includes(user.id))
})

// 初始化
onMounted(async () => {
  await fetchDepartments()
  await fetchUsers()

  // 如果选择了架构视图，加载详细数据
  if (activeTab.value === 'chart') {
    await fetchDepartmentsWithDetails()
  }
})

// 监听标签页变化
watch(activeTab, async (newValue) => {
  if (newValue === 'chart' && departmentsWithDetails.value.length === 0) {
    await fetchDepartmentsWithDetails()
  }
})

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const result = await departmentApi.getAllDepartments()
    if (result.success) {
      departments.value = result.data
      // 创建扁平化的部门列表，用于选择上级部门
      flattenDepartments(result.data)
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 获取带详细信息的部门列表（用于架构图）
const fetchDepartmentsWithDetails = async () => {
  try {
    ElMessage.info('正在加载部门数据...')

    // 获取所有部门
    const result = await departmentApi.getAllDepartments()
    if (!result.success) {
      ElMessage.error('获取部门列表失败')
      return
    }

    // 获取每个部门的详细信息
    const detailedDepts = []
    for (const dept of result.data) {
      try {
        const detailResult = await departmentApi.getDepartmentById(dept.id)
        if (detailResult.success) {
          detailedDepts.push(detailResult.data)
        }
      } catch (error) {
        console.error(`获取部门 ${dept.id} 详情失败:`, error)
      }
    }

    // 构建部门树
    const buildDeptTree = (depts) => {
      const deptMap = new Map()
      const rootDepts = []

      // 先创建映射
      depts.forEach(dept => {
        deptMap.set(dept.id, { ...dept, children: [] })
      })

      // 构建树结构
      depts.forEach(dept => {
        const mappedDept = deptMap.get(dept.id)
        if (dept.parent_id && deptMap.has(dept.parent_id)) {
          const parent = deptMap.get(dept.parent_id)
          parent.children.push(mappedDept)
        } else {
          rootDepts.push(mappedDept)
        }
      })

      return rootDepts
    }

    departmentsWithDetails.value = buildDeptTree(detailedDepts)
    ElMessage.success('部门数据加载完成')
  } catch (error) {
    console.error('获取部门详情失败:', error)
    ElMessage.error('获取部门详情失败')
  }
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    const result = await userApi.getAllUsers()
    if (result.success) {
      users.value = result.data
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 获取部门成员
const fetchDepartmentMembers = async (departmentId) => {
  try {
    const result = await departmentApi.getDepartmentMembers(departmentId)
    if (result.success) {
      departmentMembers.value = result.data
    }
  } catch (error) {
    console.error('获取部门成员失败:', error)
    ElMessage.error('获取部门成员失败')
  }
}

// 将嵌套的部门列表扁平化
const flattenDepartments = (depts, parentId = null) => {
  flatDepartments.value = []
  const flatten = (items, parentId) => {
    items.forEach(item => {
      flatDepartments.value.push(item)
      if (item.children && item.children.length > 0) {
        flatten(item.children, item.id)
      }
    })
  }
  flatten(depts, parentId)
}

// 添加部门
const handleAddDepartment = () => {
  resetForm()
  dialogType.value = 'add'
  dialogVisible.value = true
}

// 编辑部门
const handleEditDepartment = (row) => {
  resetForm()
  dialogType.value = 'edit'
  Object.assign(departmentForm, {
    id: row.id,
    name: row.name,
    code: row.code,
    parent_id: row.parent_id,
    manager_id: row.manager_id,
    description: row.description,
    status: row.status
  })
  dialogVisible.value = true
}

// 查看部门
const handleViewDepartment = async (row) => {
  try {
    const result = await departmentApi.getDepartmentById(row.id)
    if (result.success) {
      ElMessageBox.alert(
        `<div>
          <p><strong>部门名称:</strong> ${result.data.name}</p>
          <p><strong>部门代码:</strong> ${result.data.code || '-'}</p>
          <p><strong>上级部门:</strong> ${result.data.parent ? result.data.parent.name : '无'}</p>
          <p><strong>部门负责人:</strong> ${result.data.manager ? result.data.manager.full_name : '未设置'}</p>
          <p><strong>描述:</strong> ${result.data.description || '-'}</p>
          <p><strong>状态:</strong> ${result.data.status === 'active' ? '启用' : '禁用'}</p>
          <p><strong>成员数量:</strong> ${result.data.members ? result.data.members.length : 0}</p>
        </div>`,
        '部门详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        }
      )
    }
  } catch (error) {
    console.error('获取部门详情失败:', error)
    ElMessage.error('获取部门详情失败')
  }
}

// 删除部门
const handleDeleteDepartment = (row) => {
  ElMessageBox.confirm(
    '此操作将永久删除该部门，是否继续?',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const result = await departmentApi.deleteDepartment(row.id)
      if (result.success) {
        ElMessage.success('删除成功')
        fetchDepartments()
      } else {
        ElMessage.error(result.message || '删除失败')
      }
    } catch (error) {
      console.error('删除部门失败:', error)
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 管理部门成员
const handleManageMembers = async (row) => {
  currentDepartment.value = row
  resetMemberForm()
  await fetchDepartmentMembers(row.id)
  memberDialogVisible.value = true
}

// 重置成员表单
const resetMemberForm = () => {
  memberForm.user_id = null
  memberForm.position = ''
  memberForm.is_primary = false

  if (memberFormRef.value) {
    memberFormRef.value.resetFields()
  }
}

// 添加部门成员
const handleAddMember = async () => {
  if (!memberFormRef.value) return

  await memberFormRef.value.validate(async (valid) => {
    if (valid && currentDepartment.value) {
      try {
        submitting.value = true
        const result = await departmentApi.addDepartmentMember(
          currentDepartment.value.id,
          {
            user_id: memberForm.user_id,
            position: memberForm.position,
            is_primary: memberForm.is_primary
          }
        )

        if (result.success) {
          ElMessage.success('添加成员成功')
          await fetchDepartmentMembers(currentDepartment.value.id)
          resetMemberForm()
        } else {
          ElMessage.error(result.message || '添加成员失败')
        }
      } catch (error) {
        console.error('添加部门成员失败:', error)
        ElMessage.error(error.response?.data?.message || '添加成员失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 编辑部门成员
const handleEditMember = (user) => {
  editMemberForm.user_id = user.id
  editMemberForm.full_name = user.full_name
  editMemberForm.position = user.department_users?.position || ''
  editMemberForm.is_primary = user.department_users?.is_primary || false

  editMemberDialogVisible.value = true
}

// 更新部门成员
const handleUpdateMember = async () => {
  if (!editMemberFormRef.value) return

  await editMemberFormRef.value.validate(async (valid) => {
    if (valid && currentDepartment.value) {
      try {
        submitting.value = true
        const result = await departmentApi.updateDepartmentMember(
          currentDepartment.value.id,
          editMemberForm.user_id,
          {
            position: editMemberForm.position,
            is_primary: editMemberForm.is_primary
          }
        )

        if (result.success) {
          ElMessage.success('更新成员成功')
          await fetchDepartmentMembers(currentDepartment.value.id)
          editMemberDialogVisible.value = false
        } else {
          ElMessage.error(result.message || '更新成员失败')
        }
      } catch (error) {
        console.error('更新部门成员失败:', error)
        ElMessage.error(error.response?.data?.message || '更新成员失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 移除部门成员
const handleRemoveMember = (user) => {
  if (!currentDepartment.value) return

  ElMessageBox.confirm(
    `确定要将 ${user.full_name} 从部门中移除吗?`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const result = await departmentApi.removeDepartmentMember(
        currentDepartment.value.id,
        user.id
      )

      if (result.success) {
        ElMessage.success('移除成员成功')
        await fetchDepartmentMembers(currentDepartment.value.id)
      } else {
        ElMessage.error(result.message || '移除成员失败')
      }
    } catch (error) {
      console.error('移除部门成员失败:', error)
      ElMessage.error(error.response?.data?.message || '移除成员失败')
    }
  }).catch(() => {
    // 取消移除
  })
}

// 提交部门表单
const submitDepartmentForm = async () => {
  if (!departmentFormRef.value) return

  await departmentFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        let result

        if (dialogType.value === 'add') {
          result = await departmentApi.createDepartment(departmentForm)
        } else {
          result = await departmentApi.updateDepartment(departmentForm.id, departmentForm)
        }

        if (result.success) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
          dialogVisible.value = false
          fetchDepartments()
        } else {
          ElMessage.error(result.message || (dialogType.value === 'add' ? '添加失败' : '更新失败'))
        }
      } catch (error) {
        console.error(dialogType.value === 'add' ? '添加部门失败:' : '更新部门失败:', error)
        ElMessage.error(error.response?.data?.message || (dialogType.value === 'add' ? '添加失败' : '更新失败'))
      } finally {
        submitting.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (departmentFormRef.value) {
    departmentFormRef.value.resetFields()
  }

  Object.assign(departmentForm, {
    id: undefined,
    name: '',
    code: '',
    parent_id: null,
    manager_id: null,
    description: '',
    status: 'active'
  })
}
</script>

<style scoped>
.department-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
