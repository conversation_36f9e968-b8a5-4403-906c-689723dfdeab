/**
 * Global setup for Playwright tests
 * This script runs before all tests to set up the test environment
 */

const { chromium } = require('@playwright/test');
const TestDataManager = require('./utils/test-data-manager');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:5273';
const API_BASE_URL = 'http://localhost:5273/api';
const ADMIN_USER = {
  username: 'admin',
  password: 'admin123'
};

/**
 * Setup function that runs before all tests
 */
async function globalSetup(config) {
  console.log('Starting global setup...');
  
  // Create auth directory if it doesn't exist
  const authDir = path.join(__dirname, 'auth');
  if (!fs.existsSync(authDir)) {
    fs.mkdirSync(authDir);
  }
  
  // Launch browser and login as admin
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Login as admin
  await page.goto(`${BASE_URL}/login`);
  await page.fill('input[placeholder="用户名"]', ADMIN_USER.username);
  await page.fill('input[placeholder="密码"]', ADMIN_USER.password);
  await page.click('.login-button');
  
  // Wait for login to complete
  await page.waitForURL(`${BASE_URL}/`);
  
  // Get auth token from localStorage
  const authToken = await page.evaluate(() => localStorage.getItem('token'));
  
  // Save admin auth state
  await context.storageState({ path: path.join(authDir, 'admin.json') });
  
  // Create test data manager
  const testDataManager = new TestDataManager({
    baseURL: API_BASE_URL,
    authToken
  });
  
  // Create test data for all tests
  try {
    console.log('Creating test data...');
    
    // Create a department
    const department = await testDataManager.createDepartment({
      name: 'E2E Test Department',
      code: 'E2E_TEST'
    });
    console.log(`Created department: ${department.name} (ID: ${department.id})`);
    
    // Create a form
    const form = await testDataManager.createForm({
      title: 'E2E Test Form',
      description: 'Form created for E2E testing'
    });
    console.log(`Created form: ${form.title} (ID: ${form.id})`);
    
    // Create a workflow
    const workflow = await testDataManager.createWorkflow({
      title: 'E2E Test Workflow',
      description: 'Workflow created for E2E testing',
      form_template_id: form.id
    });
    console.log(`Created workflow: ${workflow.title} (ID: ${workflow.id})`);
    
    // Create a test user
    const user = await testDataManager.createUser({
      username: 'e2e_test_user',
      password: 'Test123!',
      full_name: 'E2E Test User'
    });
    console.log(`Created user: ${user.username} (ID: ${user.id})`);
    
    // Login as test user and save auth state
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[placeholder="用户名"]', 'e2e_test_user');
    await page.fill('input[placeholder="密码"]', 'Test123!');
    await page.click('.login-button');
    await page.waitForURL(`${BASE_URL}/`);
    
    // Save test user auth state
    const testUserContext = await browser.newContext();
    await testUserContext.storageState({ path: path.join(authDir, 'test-user.json') });
    await testUserContext.close();
    
  } catch (error) {
    console.error('Error creating test data:', error.message);
  }
  
  // Close browser
  await browser.close();
  
  console.log('Global setup completed');
}

module.exports = globalSetup;
