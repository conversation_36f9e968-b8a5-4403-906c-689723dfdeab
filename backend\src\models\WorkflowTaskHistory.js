const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     WorkflowTaskHistory:
 *       type: object
 *       required:
 *         - workflow_instance_id
 *         - task_id
 *         - node_id
 *         - operator_id
 *         - operation
 *       properties:
 *         id:
 *           type: integer
 *           description: 工作流任务历史ID
 *           example: 1
 *         workflow_instance_id:
 *           type: integer
 *           description: 工作流实例ID
 *           example: 1
 *         task_id:
 *           type: integer
 *           description: 任务ID
 *           example: 1
 *         node_id:
 *           type: integer
 *           description: 节点ID
 *           example: 2
 *         operator_id:
 *           type: integer
 *           description: 操作人ID
 *           example: 3
 *         operation:
 *           type: string
 *           description: 操作类型
 *           enum: [approve, reject, transfer, complete]
 *           example: "approve"
 *         comments:
 *           type: string
 *           description: 处理意见
 *           example: "同意请假申请"
 *         form_data:
 *           type: object
 *           description: 表单数据
 *           example: {
 *             "leave_type": "sick",
 *             "start_date": "2023-06-01",
 *             "end_date": "2023-06-03",
 *             "reason": "生病需要休息"
 *           }
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class WorkflowTaskHistory extends Model {
    static associate(models) {
      // 工作流实例
      WorkflowTaskHistory.belongsTo(models.WorkflowInstance, {
        foreignKey: 'workflow_instance_id',
        as: 'workflowInstance'
      });

      // 任务
      WorkflowTaskHistory.belongsTo(models.WorkflowTask, {
        foreignKey: 'task_id',
        as: 'task'
      });

      // 节点
      WorkflowTaskHistory.belongsTo(models.WorkflowNode, {
        foreignKey: 'node_id',
        as: 'node'
      });

      // 操作人
      WorkflowTaskHistory.belongsTo(models.User, {
        foreignKey: 'operator_id',
        as: 'operator'
      });
    }
  }

  WorkflowTaskHistory.init({
    workflow_instance_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_instances',
        key: 'id'
      }
    },
    task_id: {
      type: DataTypes.INTEGER,
      allowNull: false, // 数据库中设置为NOT NULL，保持一致
      references: {
        model: 'workflow_tasks',
        key: 'id'
      }
    },
    node_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_nodes',
        key: 'id'
      }
    },
    operator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    operation: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    comments: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    form_data: {
      type: DataTypes.JSONB,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'WorkflowTaskHistory',
    tableName: 'workflow_task_histories',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false
  });

  return WorkflowTaskHistory;
};
