const { Role, Permission, Sequelize } = require('../models');

// 获取所有角色
exports.getAllRoles = async (req, res) => {
  try {
    const roles = await Role.findAll({
      attributes: ['id', 'name', 'display_name', 'description']
    });

    res.json({
      success: true,
      data: roles
    });
  } catch (error) {
    console.error('获取角色列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取单个角色
exports.getRoleById = async (req, res) => {
  try {
    const { id } = req.params;

    const role = await Role.findByPk(id, {
      attributes: ['id', 'name', 'display_name', 'description'],
      include: [
        {
          model: Permission,
          as: 'permissions',
          attributes: ['id', 'name', 'description'],
          through: { attributes: [] }
        }
      ]
    });

    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }

    res.json({
      success: true,
      data: role
    });
  } catch (error) {
    console.error('获取角色详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 创建角色
exports.createRole = async (req, res) => {
  try {
    const { name, display_name, description, permissions } = req.body;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    // 检查角色名是否已存在
    const existingRole = await Role.findOne({ where: { name } });
    if (existingRole) {
      return res.status(400).json({
        success: false,
        message: '角色名已存在'
      });
    }

    // 创建角色
    const role = await Role.create({
      name,
      display_name,
      description
    });

    // 如果提供了权限，分配权限
    if (permissions && permissions.length > 0) {
      const permissionInstances = await Permission.findAll({
        where: {
          id: {
            [Sequelize.Op.in]: permissions
          }
        }
      });
      await role.setPermissions(permissionInstances);
    }

    res.status(201).json({
      success: true,
      message: '角色创建成功',
      data: role
    });
  } catch (error) {
    console.error('创建角色错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 更新角色
exports.updateRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, display_name, description, permissions } = req.body;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const role = await Role.findByPk(id);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }

    // 如果修改了角色名，检查是否已存在
    if (name && name !== role.name) {
      const existingRole = await Role.findOne({ where: { name } });
      if (existingRole) {
        return res.status(400).json({
          success: false,
          message: '角色名已存在'
        });
      }
    }

    // 更新角色
    await role.update({
      name: name || role.name,
      display_name: display_name || role.display_name,
      description: description !== undefined ? description : role.description
    });

    // 如果提供了权限，更新权限
    if (permissions) {
      const permissionInstances = await Permission.findAll({
        where: {
          id: {
            [Sequelize.Op.in]: permissions
          }
        }
      });
      await role.setPermissions(permissionInstances);
    }

    res.json({
      success: true,
      message: '角色更新成功',
      data: role
    });
  } catch (error) {
    console.error('更新角色错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 删除角色
exports.deleteRole = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const role = await Role.findByPk(id);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }

    // 检查是否是系统角色
    if (role.name === 'admin' || role.name === 'user') {
      return res.status(400).json({
        success: false,
        message: '无法删除系统角色'
      });
    }

    await role.destroy();

    res.json({
      success: true,
      message: '角色删除成功'
    });
  } catch (error) {
    console.error('删除角色错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
}; 