'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 创建工作流实例表
    await queryInterface.createTable('workflow_instances', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_templates',
          key: 'id'
        }
      },
      title: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      initiator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      current_node_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'workflow_nodes',
          key: 'id'
        }
      },
      status: {
        type: Sequelize.STRING(20),
        defaultValue: 'running'
      },
      form_data: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      completed_at: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });

    // 创建工作流任务表
    await queryInterface.createTable('workflow_tasks', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_instance_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_instances',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      node_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_nodes',
          key: 'id'
        }
      },
      assignee_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      assignee_role_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'roles',
          key: 'id'
        }
      },
      assignee_department_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'departments',
          key: 'id'
        }
      },
      status: {
        type: Sequelize.STRING(20),
        defaultValue: 'pending'
      },
      due_date: {
        type: Sequelize.DATE,
        allowNull: true
      },
      priority: {
        type: Sequelize.STRING(20),
        defaultValue: 'normal'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      completed_at: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });

    // 创建工作流任务历史表
    await queryInterface.createTable('workflow_task_histories', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_instance_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_instances',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      task_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'workflow_tasks',
          key: 'id'
        },
        onDelete: 'SET NULL'
      },
      node_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_nodes',
          key: 'id'
        }
      },
      operator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      operation: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      comments: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      form_data: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 创建工作流附件表
    await queryInterface.createTable('workflow_attachments', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_instance_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_instances',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      task_history_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'workflow_task_histories',
          key: 'id'
        },
        onDelete: 'SET NULL'
      },
      file_name: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      file_path: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      file_size: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      file_type: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      uploader_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 创建索引
    await queryInterface.addIndex('workflow_instances', ['workflow_template_id'], {
      name: 'idx_workflow_instances_workflow_template_id'
    });
    await queryInterface.addIndex('workflow_instances', ['initiator_id'], {
      name: 'idx_workflow_instances_initiator_id'
    });
    await queryInterface.addIndex('workflow_instances', ['current_node_id'], {
      name: 'idx_workflow_instances_current_node_id'
    });
    await queryInterface.addIndex('workflow_instances', ['status'], {
      name: 'idx_workflow_instances_status'
    });

    await queryInterface.addIndex('workflow_tasks', ['workflow_instance_id'], {
      name: 'idx_workflow_tasks_workflow_instance_id'
    });
    await queryInterface.addIndex('workflow_tasks', ['node_id'], {
      name: 'idx_workflow_tasks_node_id'
    });
    await queryInterface.addIndex('workflow_tasks', ['assignee_id'], {
      name: 'idx_workflow_tasks_assignee_id'
    });
    await queryInterface.addIndex('workflow_tasks', ['assignee_role_id'], {
      name: 'idx_workflow_tasks_assignee_role_id'
    });
    await queryInterface.addIndex('workflow_tasks', ['assignee_department_id'], {
      name: 'idx_workflow_tasks_assignee_department_id'
    });
    await queryInterface.addIndex('workflow_tasks', ['status'], {
      name: 'idx_workflow_tasks_status'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('workflow_attachments');
    await queryInterface.dropTable('workflow_task_histories');
    await queryInterface.dropTable('workflow_tasks');
    await queryInterface.dropTable('workflow_instances');
  }
};
