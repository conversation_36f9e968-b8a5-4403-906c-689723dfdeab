/**
 * Create admin user script
 * Run with: node create-admin-user.js
 */

const bcrypt = require('bcryptjs');
const { sequelize, User, Role } = require('./src/models');

async function createAdminUser() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Check if admin role exists
    let adminRole = await Role.findOne({ where: { name: 'admin' } });
    
    // Create admin role if it doesn't exist
    if (!adminRole) {
      console.log('Creating admin role...');
      adminRole = await Role.create({
        name: 'admin',
        display_name: '管理员',
        description: '系统管理员，拥有所有权限'
      });
      console.log('Admin role created successfully.');
    } else {
      console.log('Admin role already exists.');
    }

    // Check if admin user exists
    let adminUser = await User.findOne({ where: { username: 'admin' } });
    
    // Create admin user if it doesn't exist
    if (!adminUser) {
      console.log('Creating admin user...');
      
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);
      
      // Create the user
      adminUser = await User.create({
        username: 'admin',
        password: hashedPassword,
        email: '<EMAIL>',
        full_name: 'System Administrator',
        status: 'active'
      });
      
      // Assign admin role to user
      await adminUser.addRole(adminRole);
      
      console.log('Admin user created successfully.');
    } else {
      console.log('Admin user already exists.');
      
      // Check if admin user has admin role
      const userRoles = await adminUser.getRoles();
      const hasAdminRole = userRoles.some(role => role.name === 'admin');
      
      if (!hasAdminRole) {
        console.log('Assigning admin role to admin user...');
        await adminUser.addRole(adminRole);
        console.log('Admin role assigned to admin user.');
      }
    }
    
    console.log('Admin user setup completed successfully.');
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

createAdminUser();
