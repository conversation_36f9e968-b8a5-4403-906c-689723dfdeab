/**
 * Debug script for workflow API
 * Run with: node debug-workflow-api.js
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:5273/api';
const AUTH_TOKEN = 'YOUR_AUTH_TOKEN'; // Replace with a valid token

// Test data for workflow instance creation
const testData = {
  title: 'Debug Test Workflow Instance',
  form_data: {
    // Add sample form data based on your form structure
    // For example:
    text_field: 'Test value',
    textarea_field: 'Test description',
    number_field: 123,
    select_field: 'option1',
    checkbox_field: true
  }
};

// Helper function to make authenticated requests
async function makeRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (AUTH_TOKEN) {
      config.headers['Authorization'] = `Bearer ${AUTH_TOKEN}`;
    }

    if (data) {
      config.data = data;
    }

    console.log(`Making ${method} request to ${endpoint}`);
    const response = await axios(config);
    console.log('Response status:', response.status);
    console.log('Response data:', JSO<PERSON>.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Request failed:');
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error setting up request:', error.message);
    }
    throw error;
  }
}

// Main function to run tests
async function runTests() {
  try {
    // 1. Get all workflows to find available ones
    console.log('\n=== Getting all workflows ===');
    const workflows = await makeRequest('get', '/workflows');
    
    if (!workflows.success || !workflows.data || workflows.data.length === 0) {
      console.error('No workflows found or API returned error');
      return;
    }
    
    // 2. Get details of the first workflow
    const workflowId = workflows.data[0].id;
    console.log(`\n=== Getting details for workflow ${workflowId} ===`);
    const workflowDetails = await makeRequest('get', `/workflows/${workflowId}`);
    
    // 3. Try to create a workflow instance
    console.log(`\n=== Creating workflow instance for workflow ${workflowId} ===`);
    await makeRequest('post', `/workflows/${workflowId}/instances`, testData);
    
  } catch (error) {
    console.error('Test run failed:', error.message);
  }
}

// Run the tests
runTests();
