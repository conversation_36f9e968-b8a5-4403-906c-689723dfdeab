<template>
  <div class="department-chart">
    <div v-if="useStaticView" class="static-chart">
      <div class="static-chart-container">
        <div v-for="(node, index) in staticNodes" :key="node.id" class="dept-node"
             :class="[node.status === 'active' ? 'active' : 'inactive']"
             :style="getNodeStyle(node, index)">
          <div class="node-content">
            <div class="node-title">{{ node.name }}</div>
            <div class="node-metrics" v-if="showMetrics">
              <div class="metric">成员: {{ node.memberCount || 0 }}</div>
              <div class="metric">状态: {{ node.status === 'active' ? '启用' : '禁用' }}</div>
            </div>
          </div>
        </div>
        <svg class="chart-lines" :width="chartWidth" :height="chartHeight">
          <defs>
            <marker id="dept-arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#909399" />
            </marker>
          </defs>
          <path v-for="(line, idx) in staticLines" :key="idx"
                :d="line.path" :stroke="line.stroke"
                stroke-width="2" fill="none"
                :marker-end="line.marker" />
        </svg>
      </div>
      <div class="static-chart-controls">
        <el-button size="small" @click="toggleView">切换到ECharts图表</el-button>
        <el-button size="small" @click="toggleMetrics" type="primary">
          {{ showMetrics ? '隐藏数据' : '显示数据' }}
        </el-button>
      </div>
    </div>
    <div v-else ref="chartRef" class="echarts-container"></div>
    <div class="chart-controls">
      <el-button v-if="!useStaticView" size="small" @click="toggleView">切换到静态视图</el-button>
      <el-button v-if="!useStaticView" size="small" @click="toggleMetrics" type="primary">
        {{ showMetrics ? '隐藏数据' : '显示数据' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import * as echarts from 'echarts'

// 接收部门数据作为props
const props = defineProps({
  departments: {
    type: Array,
    required: true
  }
})

const chartRef = ref(null)
let chart = null

// 静态视图控制
const useStaticView = ref(false)
const chartWidth = ref(1000)
const chartHeight = ref(600)
const showMetrics = ref(true)

// 切换视图
const toggleView = () => {
  useStaticView.value = !useStaticView.value
  if (!useStaticView.value) {
    nextTick(() => {
      initChart()
    })
  }
}

// 切换是否显示数据指标
const toggleMetrics = () => {
  showMetrics.value = !showMetrics.value
  if (!useStaticView.value) {
    nextTick(() => {
      initChart()
    })
  }
}

// 计算静态节点数据
const staticNodes = computed(() => {
  if (!props.departments || !Array.isArray(props.departments)) {
    return []
  }

  // 扁平化部门树
  const flattenDepts = []
  const flatten = (items, level = 0, parentIndex = null) => {
    items.forEach((item, index) => {
      const nodeIndex = flattenDepts.length
      flattenDepts.push({
        ...item,
        level,
        parentIndex,
        memberCount: item.members?.length || 0,
        childCount: item.children?.length || 0,
        x: 0, // 将在getNodeStyle中计算
        y: 0  // 将在getNodeStyle中计算
      })
      
      if (item.children && item.children.length > 0) {
        flatten(item.children, level + 1, nodeIndex)
      }
    })
  }
  
  flatten(props.departments)
  return flattenDepts
})

// 计算静态连线数据
const staticLines = computed(() => {
  const nodes = staticNodes.value
  if (!nodes || nodes.length === 0) {
    return []
  }
  
  const lines = []
  
  nodes.forEach((node, index) => {
    if (node.parentIndex !== null && node.parentIndex >= 0) {
      const parentNode = nodes[node.parentIndex]
      
      // 确定连线状态
      let stroke = node.status === 'active' ? '#409EFF' : '#909399'
      let marker = 'url(#dept-arrowhead)'
      
      // 创建连线路径
      const startX = parentNode.x + 75 // 节点宽度的一半
      const startY = parentNode.y + 50 // 节点高度
      const endX = node.x + 75 // 节点宽度的一半
      const endY = node.y // 节点顶部
      
      // 控制点
      const controlX1 = startX
      const controlY1 = startY + 20
      const controlX2 = endX
      const controlY2 = endY - 20
      
      lines.push({
        path: `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`,
        stroke,
        marker
      })
    }
  })
  
  return lines
})

// 计算节点样式
const getNodeStyle = (node, index) => {
  const level = node.level || 0
  const nodesAtLevel = staticNodes.value.filter(n => n.level === level)
  const levelIndex = nodesAtLevel.findIndex(n => n.id === node.id)
  const levelCount = nodesAtLevel.length
  
  // 计算节点位置
  const nodeWidth = 150
  const nodeHeight = showMetrics.value ? 100 : 50
  const horizontalSpacing = Math.max(200, chartWidth.value / (levelCount + 1))
  const verticalSpacing = 150
  
  const x = (levelIndex + 1) * horizontalSpacing - nodeWidth / 2
  const y = level * verticalSpacing + 50
  
  // 更新节点位置（用于连线计算）
  node.x = x
  node.y = y
  
  return {
    left: `${x}px`,
    top: `${y}px`,
    width: `${nodeWidth}px`,
    height: `${nodeHeight}px`
  }
}

// 初始化ECharts图表
const initChart = async () => {
  if (!chartRef.value) return
  
  await nextTick()
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  try {
    // 准备数据
    const data = formatDepartmentsForChart(props.departments)
    
    // 图表配置
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          const { data } = params
          let tooltip = `<div style="font-weight:bold">${data.name}</div>`
          
          if (showMetrics.value) {
            tooltip += `<div>成员数量: ${data.memberCount || 0}</div>`
            tooltip += `<div>状态: ${data.status === 'active' ? '启用' : '禁用'}</div>`
            if (data.manager) {
              tooltip += `<div>负责人: ${data.manager.full_name || '-'}</div>`
            }
          }
          
          return tooltip
        }
      },
      series: [
        {
          type: 'tree',
          data: [data],
          top: '10%',
          left: '10%',
          bottom: '10%',
          right: '10%',
          symbolSize: 12,
          orient: 'vertical',
          label: {
            position: 'inside',
            verticalAlign: 'middle',
            align: 'center',
            fontSize: 14,
            color: '#fff',
            formatter: (params) => {
              let label = params.data.name
              
              if (showMetrics.value) {
                label += `\n成员: ${params.data.memberCount || 0}`
              }
              
              return label
            }
          },
          leaves: {
            label: {
              position: 'inside',
              verticalAlign: 'middle',
              align: 'center'
            }
          },
          expandAndCollapse: true,
          animationDuration: 550,
          animationDurationUpdate: 750,
          itemStyle: {
            color: (params) => {
              return params.data.status === 'active' ? '#409EFF' : '#909399'
            }
          },
          lineStyle: {
            color: '#ccc',
            width: 2
          },
          emphasis: {
            focus: 'descendant'
          }
        }
      ]
    }
    
    chart.setOption(option)
  } catch (error) {
    console.error('初始化部门图表时出错:', error)
    useStaticView.value = true
  }
  
  // 自适应大小
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }
  })
}

// 格式化部门数据为ECharts树图格式
const formatDepartmentsForChart = (departments) => {
  if (!departments || departments.length === 0) {
    return { name: '无部门数据' }
  }
  
  // 假设第一个部门是根部门
  const rootDept = departments[0]
  
  const formatDept = (dept) => {
    const result = {
      name: dept.name,
      value: dept.id,
      status: dept.status,
      memberCount: dept.members?.length || 0,
      manager: dept.manager,
      itemStyle: {
        color: dept.status === 'active' ? '#409EFF' : '#909399'
      }
    }
    
    if (dept.children && dept.children.length > 0) {
      result.children = dept.children.map(child => formatDept(child))
    }
    
    return result
  }
  
  return formatDept(rootDept)
}

// 监听部门数据变化
watch(() => props.departments, () => {
  if (!useStaticView.value) {
    nextTick(() => {
      initChart()
    })
  }
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 设置图表容器的最小宽度
  if (chartRef.value) {
    const parentElement = chartRef.value.parentElement
    if (parentElement) {
      if (parentElement.offsetWidth < 1000) {
        parentElement.style.minWidth = '1000px'
      }
    }
  }
  
  // 初始化图表
  initChart()
})
</script>

<style scoped>
.department-chart {
  margin: 20px 0;
  position: relative;
}

.static-chart-container {
  width: 100%;
  min-width: 1000px;
  height: 600px;
  position: relative;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  background-image: radial-gradient(#dcdfe6 1px, transparent 0);
  background-size: 20px 20px;
  padding: 20px;
}

.dept-node {
  position: absolute;
  width: 150px;
  min-height: 50px;
  border: 2px solid #909399;
  border-radius: 4px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 10px;
}

.dept-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  z-index: 3;
}

.dept-node.active {
  border-color: #409EFF;
  background-color: rgba(64, 158, 255, 0.1);
}

.dept-node.inactive {
  border-color: #909399;
  background-color: rgba(144, 147, 153, 0.1);
}

.node-content {
  width: 100%;
  text-align: center;
}

.node-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.node-metrics {
  font-size: 12px;
  color: #606266;
}

.metric {
  margin: 2px 0;
}

.chart-lines {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
}

.static-chart-controls {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.chart-controls {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.echarts-container {
  width: 100%;
  height: 600px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
</style>
