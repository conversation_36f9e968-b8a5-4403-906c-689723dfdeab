const { test, expect } = require('@playwright/test');
const { login, navigateToModule } = require('../utils/test-helpers');

test.describe('Workflow Lifecycle Tests', () => {
  // This test requires multiple user accounts and a working backend
  // It tests the complete lifecycle of a workflow
  
  test('should complete a full workflow lifecycle', async ({ browser }) => {
    // This test uses multiple browser contexts to simulate different users
    
    // Create a timestamp for unique identification
    const timestamp = Date.now();
    const workflowTitle = `测试工作流-${timestamp}`;
    const instanceTitle = `测试实例-${timestamp}`;
    
    // Step 1: Admin creates a workflow
    const adminContext = await browser.newContext();
    const adminPage = await adminContext.newPage();
    
    // Login as admin
    await adminPage.goto('/login');
    await adminPage.fill('input[placeholder="用户名"]', 'admin');
    await adminPage.fill('input[placeholder="密码"]', 'admin123');
    await adminPage.click('.login-button');
    await adminPage.waitForURL(/.*\//);
    
    // Navigate to workflow design
    await adminPage.goto('/');
    await adminPage.locator('.module-card').filter({ hasText: '工作流设计' }).locator('button').click();
    
    // Create a new workflow
    await adminPage.click('button:has-text("创建工作流")');
    
    // Fill workflow info
    await adminPage.fill('input[placeholder="请输入工作流名称"]', workflowTitle);
    await adminPage.fill('textarea[placeholder="请输入工作流描述"]', '这是一个测试工作流');
    
    // Select a form template
    await adminPage.click('.el-select').filter({ hasText: '请选择关联表单' });
    await adminPage.click('.el-select-dropdown__item:first-child');
    
    // Set status to published
    await adminPage.click('.el-select').filter({ hasText: '草稿' });
    await adminPage.click('.el-select-dropdown__item:has-text("已发布")');
    
    // Add nodes (assuming the workflow designer already has a start node)
    // Add approval node
    await adminPage.locator('.node-types .node-type-item').filter({ hasText: '审批节点' }).click();
    await adminPage.locator('.workflow-canvas').click({ position: { x: 300, y: 200 } });
    
    // Add end node
    await adminPage.locator('.node-types .node-type-item').filter({ hasText: '结束节点' }).click();
    await adminPage.locator('.workflow-canvas').click({ position: { x: 500, y: 300 } });
    
    // Save workflow
    await adminPage.click('button:has-text("保存")');
    
    // Verify success message
    await adminPage.waitForSelector('.el-message--success');
    
    // Close admin context
    await adminContext.close();
    
    // Step 2: User initiates a workflow instance
    const userContext = await browser.newContext();
    const userPage = await userContext.newPage();
    
    // Login as user
    await userPage.goto('/login');
    await userPage.fill('input[placeholder="用户名"]', 'user');
    await userPage.fill('input[placeholder="密码"]', 'user123');
    await userPage.click('.login-button');
    await userPage.waitForURL(/.*\//);
    
    // Navigate to workflow form
    await userPage.goto('/');
    await userPage.locator('.module-card').filter({ hasText: '工作流填写' }).locator('button').click();
    
    // Find and start the workflow we just created
    let workflowFound = false;
    
    // Wait for table to load
    await userPage.waitForSelector('.el-table__row');
    
    // Get all workflow rows
    const rows = await userPage.locator('.el-table__row').all();
    
    for (const row of rows) {
      const text = await row.textContent();
      if (text.includes(workflowTitle)) {
        // Found our workflow
        workflowFound = true;
        await row.locator('button:has-text("发起")').click();
        break;
      }
    }
    
    if (!workflowFound) {
      test.skip('Could not find the created workflow');
      await userContext.close();
      return;
    }
    
    // Fill the form
    await userPage.fill('input[placeholder="请输入工作流标题"]', instanceTitle);
    
    // Fill form fields (generic approach)
    // For text inputs
    const textInputs = await userPage.locator('input[type="text"]:not([placeholder="请输入工作流标题"])').all();
    for (const input of textInputs) {
      await input.fill('测试数据');
    }
    
    // For textareas
    const textareas = await userPage.locator('textarea').all();
    for (const textarea of textareas) {
      await textarea.fill('测试描述');
    }
    
    // For select inputs
    const selects = await userPage.locator('.el-select:not(.is-disabled)').all();
    for (const select of selects) {
      await select.click();
      await userPage.locator('.el-select-dropdown__item').first().click();
    }
    
    // Submit the form
    await userPage.click('button:has-text("提交")');
    
    // Check for success or error message
    try {
      await userPage.waitForSelector('.el-message--success, .el-message--error', { timeout: 5000 });
    } catch (error) {
      console.log('No message displayed after form submission');
      await userContext.close();
      test.skip('Form submission did not show any message');
      return;
    }
    
    // Close user context
    await userContext.close();
    
    // Step 3: Admin processes the workflow task
    const adminContext2 = await browser.newContext();
    const adminPage2 = await adminContext2.newPage();
    
    // Login as admin again
    await adminPage2.goto('/login');
    await adminPage2.fill('input[placeholder="用户名"]', 'admin');
    await adminPage2.fill('input[placeholder="密码"]', 'admin123');
    await adminPage2.click('.login-button');
    await adminPage2.waitForURL(/.*\//);
    
    // Navigate to workflow process
    await adminPage2.goto('/');
    await adminPage2.locator('.module-card').filter({ hasText: '工作流流转' }).locator('button').click();
    
    // Wait for table to load
    await adminPage2.waitForSelector('.el-table');
    
    // Check if there are any tasks
    const hasTasks = await adminPage2.locator('.el-table__row').count() > 0;
    
    if (!hasTasks) {
      test.skip('No tasks found for admin to process');
      await adminContext2.close();
      return;
    }
    
    // Find our specific task
    let taskFound = false;
    const taskRows = await adminPage2.locator('.el-table__row').all();
    
    for (const row of taskRows) {
      const text = await row.textContent();
      if (text.includes(instanceTitle)) {
        // Found our task
        taskFound = true;
        await row.locator('button:has-text("处理")').click();
        break;
      }
    }
    
    if (!taskFound) {
      test.skip('Could not find the specific task to process');
      await adminContext2.close();
      return;
    }
    
    // Process the task
    await adminPage2.fill('textarea[placeholder="请输入处理意见"]', '测试审批通过');
    await adminPage2.click('.operation-buttons button:has-text("同意")');
    await adminPage2.click('button:has-text("提交")');
    
    // Check for success or error message
    try {
      await adminPage2.waitForSelector('.el-message--success, .el-message--error', { timeout: 5000 });
    } catch (error) {
      console.log('No message displayed after task processing');
    }
    
    // Verify task is moved to done tasks
    await adminPage2.click('.el-tabs__item').filter({ hasText: '已办任务' });
    
    // Wait for table to load
    await adminPage2.waitForSelector('.el-table__row');
    
    // Check if our task is in the done list
    let doneTaskFound = false;
    const doneRows = await adminPage2.locator('.el-table__row').all();
    
    for (const row of doneRows) {
      const text = await row.textContent();
      if (text.includes(instanceTitle)) {
        doneTaskFound = true;
        break;
      }
    }
    
    // Close admin context
    await adminContext2.close();
    
    // Verify the workflow completed successfully
    expect(doneTaskFound).toBeTruthy();
  });
});
