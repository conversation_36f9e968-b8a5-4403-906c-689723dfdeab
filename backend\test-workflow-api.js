/**
 * Test script for workflow API
 * Run with: node test-workflow-api.js
 */

// Configuration
const API_BASE_URL = 'http://localhost:3001/api';
const AUTH_TOKEN = 'YOUR_AUTH_TOKEN'; // Not needed in development mode

// Test data for workflow instance creation
const testData = {
  title: 'Test Workflow Instance',
  form_data: {
    test_field: 'Test value',
    test_textarea: 'Test description',
    test_select: 'option1'
  },
  skip_task_history: true // 添加跳过任务历史记录的标志
};

// Helper function to make API requests
async function makeRequest(method, endpoint, data = null) {
  try {
    const url = `${API_BASE_URL}${endpoint}`;
    const options = {
      method: method.toUpperCase(),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    };

    if (data && (method === 'post' || method === 'put')) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);
    const responseText = await response.text();
    let responseData;

    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      console.error('Failed to parse response as JSON:', responseText);
      throw new Error(`Invalid JSON response: ${responseText}`);
    }

    if (!response.ok) {
      console.error('Response details:', {
        status: response.status,
        statusText: response.statusText,
        data: responseData
      });
      throw new Error(`API Error: ${responseData.message || response.statusText}`);
    }

    return responseData;
  } catch (error) {
    console.error(`API Error (${method.toUpperCase()} ${endpoint}):`, error.message);
    throw error;
  }
}

// Run tests
async function runTests() {
  try {
    console.log('=== Starting API Tests ===');

    // 1. Get all workflows
    console.log('\n=== Getting all workflows ===');
    const workflows = await makeRequest('get', '/workflows');
    console.log(`Found ${workflows.data.length} workflows`);

    if (workflows.data.length === 0) {
      console.log('No workflows found, cannot continue testing');
      return;
    }

    // 2. Get details of the first workflow
    const workflowId = workflows.data[0].id;
    console.log(`\n=== Getting details for workflow ${workflowId} ===`);
    const workflowDetails = await makeRequest('get', `/workflows/${workflowId}`);
    console.log(`Workflow title: ${workflowDetails.data.title}`);

    // 3. Try to create a workflow instance
    console.log(`\n=== Creating workflow instance for workflow ${workflowId} ===`);
    try {
      const result = await makeRequest('post', `/workflows/${workflowId}/instances`, testData);
      console.log('Workflow instance created successfully:', result.data.id);
    } catch (error) {
      console.log('Failed to create workflow instance:', error.response ? error.response.data.message : error.message);
    }

    // 4. Try with workflow ID 2 (which should have a start node)
    const workflowId2 = 2;
    console.log(`\n=== Creating workflow instance for workflow ${workflowId2} ===`);
    try {
      const result = await makeRequest('post', `/workflows/${workflowId2}/instances`, testData);
      console.log('Workflow instance created successfully:', result.data.id);
    } catch (error) {
      console.log('Failed to create workflow instance:', error.response ? error.response.data.message : error.message);
    }

    console.log('\n=== Tests completed ===');
  } catch (error) {
    console.error('Test run failed:', error.message);
  }
}

// Run the tests
runTests();
