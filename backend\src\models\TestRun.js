const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     TestRun:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 测试运行ID
 *           example: 1
 *         testRunId:
 *           type: string
 *           description: 测试运行唯一标识符
 *           example: "2025-05-01-123456"
 *         testType:
 *           type: string
 *           description: 测试类型
 *           example: "smoke"
 *         success:
 *           type: boolean
 *           description: 测试是否成功
 *           example: true
 *         duration:
 *           type: number
 *           description: 测试持续时间（秒）
 *           example: 120.5
 *         testStats:
 *           type: object
 *           description: 测试统计数据
 *           properties:
 *             total:
 *               type: integer
 *               description: 测试总数
 *               example: 50
 *             passed:
 *               type: integer
 *               description: 通过测试数
 *               example: 45
 *             failed:
 *               type: integer
 *               description: 失败测试数
 *               example: 3
 *             skipped:
 *               type: integer
 *               description: 跳过测试数
 *               example: 2
 *         failedTests:
 *           type: array
 *           description: 失败的测试列表
 *           items:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 description: 测试文件
 *                 example: "login.spec.js"
 *               title:
 *                 type: string
 *                 description: 测试标题
 *                 example: "should login successfully"
 *               error:
 *                 type: string
 *                 description: 错误信息
 *                 example: "Expected true to be false"
 *         config:
 *           type: object
 *           description: 测试配置
 *           example: { "browser": "chrome", "headless": true }
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2025-05-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2025-05-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class TestRun extends Model {
    static associate(models) {
      // 测试结果
      TestRun.hasMany(models.TestResult, {
        foreignKey: 'testRunId',
        as: 'results'
      });

      // 测试指标
      TestRun.hasMany(models.TestMetric, {
        foreignKey: 'testRunId',
        as: 'metrics'
      });
    }
  }

  TestRun.init({
    testRunId: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    testType: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    success: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    duration: {
      type: DataTypes.FLOAT,
      allowNull: true
    },
    testStats: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    failedTests: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    config: {
      type: DataTypes.JSONB,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'TestRun',
    tableName: 'test_runs',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return TestRun;
};
