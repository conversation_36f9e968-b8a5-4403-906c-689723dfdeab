require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Department, User, FormTemplate, FormField, FormTemplateVersion } = require('./src/models');

const app = express();
const PORT = 3001;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 测试路由
app.get('/api/test', (req, res) => {
  res.json({ message: 'Test route works!' });
});

// 部门列表路由
app.get('/api/departments', async (req, res) => {
  try {
    console.log('Department model:', Department);

    if (Department && Department.findAll) {
      const departments = await Department.findAll();
      res.json({
        success: true,
        data: departments
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Department model or findAll method is not defined'
      });
    }
  } catch (error) {
    console.error('获取部门列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 用户列表路由
app.get('/api/users', async (req, res) => {
  try {
    console.log('User model:', User);

    if (User && User.findAll) {
      const users = await User.findAll({
        attributes: { exclude: ['password'] }
      });
      res.json({
        success: true,
        data: users
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'User model or findAll method is not defined'
      });
    }
  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 表单列表路由
app.get('/api/forms', async (req, res) => {
  try {
    console.log('FormTemplate model:', FormTemplate);

    if (FormTemplate && FormTemplate.findAll) {
      const formTemplates = await FormTemplate.findAll({
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username', 'full_name']
          }
        ],
        order: [['updated_at', 'DESC']]
      });

      res.json({
        success: true,
        data: formTemplates
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'FormTemplate model or findAll method is not defined'
      });
    }
  } catch (error) {
    console.error('获取表单列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 获取单个表单
app.get('/api/forms/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const formTemplate = await FormTemplate.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'full_name']
        },
        {
          model: FormField,
          as: 'fields',
          order: [['order_index', 'ASC']]
        }
      ]
    });

    if (!formTemplate) {
      return res.status(404).json({
        success: false,
        message: '表单模板不存在'
      });
    }

    res.json({
      success: true,
      data: formTemplate
    });
  } catch (error) {
    console.error('获取表单详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 创建表单
app.post('/api/forms', async (req, res) => {
  try {
    const { title, description, schema, fields, status } = req.body;

    // 模拟用户ID
    const userId = 1;

    // 创建表单模板
    const formTemplate = await FormTemplate.create({
      title,
      description,
      creator_id: userId,
      version: 1,
      status: status || 'draft',
      schema: schema || {}
    });

    // 创建表单版本
    await FormTemplateVersion.create({
      form_template_id: formTemplate.id,
      version: 1,
      schema: schema || {},
      creator_id: userId
    });

    // 创建表单字段
    if (fields && fields.length > 0) {
      const formFields = fields.map((field, index) => ({
        form_template_id: formTemplate.id,
        field_key: field.field_key,
        field_type: field.field_type,
        label: field.label,
        placeholder: field.placeholder || null,
        default_value: field.default_value || null,
        options: field.options || null,
        validation_rules: field.validation_rules || null,
        is_required: field.is_required || false,
        order_index: index
      }));

      await FormField.bulkCreate(formFields);
    }

    // 获取创建后的完整表单模板
    const createdFormTemplate = await FormTemplate.findByPk(formTemplate.id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'full_name']
        },
        {
          model: FormField,
          as: 'fields',
          order: [['order_index', 'ASC']]
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: '表单模板创建成功',
      data: createdFormTemplate
    });
  } catch (error) {
    console.error('创建表单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 更新表单
app.put('/api/forms/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, schema, fields, status } = req.body;

    const formTemplate = await FormTemplate.findByPk(id);
    if (!formTemplate) {
      return res.status(404).json({
        success: false,
        message: '表单模板不存在'
      });
    }

    // 模拟用户ID
    const userId = 1;

    // 更新表单模板
    const newVersion = formTemplate.version + 1;
    await formTemplate.update({
      title: title || formTemplate.title,
      description: description !== undefined ? description : formTemplate.description,
      version: schema ? newVersion : formTemplate.version,
      status: status || formTemplate.status,
      schema: schema || formTemplate.schema
    });

    // 如果有新的schema，创建新版本
    if (schema) {
      await FormTemplateVersion.create({
        form_template_id: formTemplate.id,
        version: newVersion,
        schema,
        creator_id: userId
      });
    }

    // 如果有新的字段，更新字段
    if (fields && fields.length > 0) {
      // 删除旧字段
      await FormField.destroy({
        where: { form_template_id: formTemplate.id }
      });

      // 创建新字段
      const formFields = fields.map((field, index) => ({
        form_template_id: formTemplate.id,
        field_key: field.field_key,
        field_type: field.field_type,
        label: field.label,
        placeholder: field.placeholder || null,
        default_value: field.default_value || null,
        options: field.options || null,
        validation_rules: field.validation_rules || null,
        is_required: field.is_required || false,
        order_index: index
      }));

      await FormField.bulkCreate(formFields);
    }

    // 获取更新后的完整表单模板
    const updatedFormTemplate = await FormTemplate.findByPk(formTemplate.id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'full_name']
        },
        {
          model: FormField,
          as: 'fields',
          order: [['order_index', 'ASC']]
        }
      ]
    });

    res.json({
      success: true,
      message: '表单模板更新成功',
      data: updatedFormTemplate
    });
  } catch (error) {
    console.error('更新表单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 删除表单
app.delete('/api/forms/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const formTemplate = await FormTemplate.findByPk(id);
    if (!formTemplate) {
      return res.status(404).json({
        success: false,
        message: '表单模板不存在'
      });
    }

    await formTemplate.destroy();

    res.json({
      success: true,
      message: '表单模板删除成功'
    });
  } catch (error) {
    console.error('删除表单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 获取表单版本历史
app.get('/api/forms/:id/versions', async (req, res) => {
  try {
    const { id } = req.params;

    const formTemplate = await FormTemplate.findByPk(id);
    if (!formTemplate) {
      return res.status(404).json({
        success: false,
        message: '表单模板不存在'
      });
    }

    const versions = await FormTemplateVersion.findAll({
      where: { form_template_id: id },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'full_name']
        }
      ],
      order: [['version', 'DESC']]
    });

    res.json({
      success: true,
      data: versions
    });
  } catch (error) {
    console.error('获取表单版本历史错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 预览表单
app.get('/api/forms/:id/preview', async (req, res) => {
  try {
    const { id } = req.params;

    const formTemplate = await FormTemplate.findByPk(id, {
      include: [
        {
          model: FormField,
          as: 'fields',
          order: [['order_index', 'ASC']]
        }
      ]
    });

    if (!formTemplate) {
      return res.status(404).json({
        success: false,
        message: '表单模板不存在'
      });
    }

    res.json({
      success: true,
      data: {
        id: formTemplate.id,
        title: formTemplate.title,
        description: formTemplate.description,
        schema: formTemplate.schema,
        fields: formTemplate.fields
      }
    });
  } catch (error) {
    console.error('预览表单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`测试服务器运行在 http://localhost:${PORT}`);
});
