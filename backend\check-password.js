/**
 * Check password script
 */
const bcrypt = require('bcryptjs');
const { sequelize, User } = require('./src/models');

async function checkPassword(username, password) {
  try {
    console.log(`Checking password for user: ${username}`);
    
    // Find the user
    const user = await User.findOne({ where: { username } });
    
    if (!user) {
      console.log(`User "${username}" not found in the database.`);
      return;
    }
    
    console.log(`User found: ID=${user.id}, Username=${user.username}, Status=${user.status}`);
    console.log(`Stored password hash: ${user.password}`);
    
    // Check if the password matches
    const isMatch = await bcrypt.compare(password, user.password);
    console.log(`Password match: ${isMatch}`);
    
    // Generate a new hash for the password
    const salt = await bcrypt.genSalt(10);
    const newHash = await bcrypt.hash(password, salt);
    console.log(`New hash for the same password: ${newHash}`);
    
    // Update the password if it doesn't match
    if (!isMatch) {
      console.log('Updating password...');
      user.password = newHash;
      await user.save();
      console.log('Password updated successfully.');
      
      // Verify the new password
      const verifyMatch = await bcrypt.compare(password, user.password);
      console.log(`Verification after update: ${verifyMatch}`);
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await sequelize.close();
  }
}

// Check password for rsb user
checkPassword('rsb', 'password123');
