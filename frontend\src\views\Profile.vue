<template>
  <div class="profile-page">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h2>个人信息</h2>
          <el-button type="primary" @click="startEdit" v-if="!isEditing">编辑</el-button>
          <div v-else>
            <el-button @click="cancelEdit">取消</el-button>
            <el-button type="primary" @click="saveProfile" :loading="loading">保存</el-button>
          </div>
        </div>
      </template>
      
      <div v-loading="loading" class="profile-content">
        <el-avatar :size="100" :src="userInfo.avatar || '/default-avatar.png'" class="profile-avatar" />
        
        <el-form 
          :model="userForm" 
          :rules="rules" 
          ref="profileFormRef" 
          label-width="100px" 
          class="profile-form"
          :disabled="!isEditing"
        >
          <el-form-item label="用户名">
            <el-input v-model="userInfo.username" disabled></el-input>
          </el-form-item>
          
          <el-form-item label="姓名" prop="full_name">
            <el-input v-model="userForm.full_name" placeholder="请输入姓名"></el-input>
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
          </el-form-item>
          
          <el-form-item label="电话" prop="phone">
            <el-input v-model="userForm.phone" placeholder="请输入电话号码"></el-input>
          </el-form-item>
          
          <el-form-item label="角色">
            <div class="role-tags">
              <el-tag 
                v-for="role in userInfo.roles" 
                :key="role.id" 
                size="default" 
                type="info"
              >
                {{ role.name }}
              </el-tag>
              <span v-if="!userInfo.roles || userInfo.roles.length === 0">无角色</span>
            </div>
          </el-form-item>
          
          <el-form-item label="最后登录">
            <span>{{ formatDate(userInfo.last_login) }}</span>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { userApi } from '@/api'
import { ElMessage } from 'element-plus'

// 状态
const loading = ref(false)
const isEditing = ref(false)
const profileFormRef = ref(null)
const userInfo = ref({
  id: null,
  username: '',
  email: '',
  full_name: '',
  phone: '',
  avatar: '',
  roles: [],
  last_login: null
})

// 表单数据
const userForm = reactive({
  full_name: '',
  email: '',
  phone: ''
})

// 表单验证规则
const rules = {
  full_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '从未登录'
  
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    loading.value = true
    const { success, data } = await userApi.getCurrentUser()
    
    if (success && data) {
      userInfo.value = data
      
      // 更新表单数据
      userForm.full_name = data.full_name || ''
      userForm.email = data.email || ''
      userForm.phone = data.phone || ''
    } else {
      ElMessage.error('获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息错误:', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// 开始编辑
const startEdit = () => {
  isEditing.value = true
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  
  // 重置表单数据
  userForm.full_name = userInfo.value.full_name || ''
  userForm.email = userInfo.value.email || ''
  userForm.phone = userInfo.value.phone || ''
  
  profileFormRef.value?.resetFields()
}

// 保存个人信息
const saveProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    
    loading.value = true
    const { success, message: msg } = await userApi.updateUser(userInfo.value.id, {
      full_name: userForm.full_name,
      email: userForm.email,
      phone: userForm.phone
    })
    
    if (success) {
      ElMessage.success('个人信息更新成功')
      isEditing.value = false
      
      // 重新获取用户信息
      await fetchUserInfo()
      
      // 更新本地存储的用户信息
      const storedUser = localStorage.getItem('user')
      if (storedUser) {
        try {
          const userData = JSON.parse(storedUser)
          userData.full_name = userForm.full_name
          userData.email = userForm.email
          userData.phone = userForm.phone
          localStorage.setItem('user', JSON.stringify(userData))
        } catch (e) {
          console.error('Failed to update stored user data', e)
        }
      }
    } else {
      ElMessage.error(msg || '更新个人信息失败')
    }
  } catch (error) {
    console.error('更新个人信息错误:', error)
    ElMessage.error('表单验证失败，请检查输入')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchUserInfo()
})
</script>

<style scoped>
.profile-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.profile-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
}

.profile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profile-avatar {
  margin-bottom: 30px;
  border: 2px solid var(--primary-color);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.profile-form {
  width: 100%;
  max-width: 500px;
}

.role-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style> 