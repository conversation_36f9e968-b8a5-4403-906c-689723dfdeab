const express = require('express');
const router = express.Router();
const formController = require('../controllers/form');

/**
 * @swagger
 * tags:
 *   name: Forms
 *   description: 表单管理API
 */

/**
 * @swagger
 * /forms:
 *   get:
 *     summary: 获取所有表单模板
 *     tags: [Forms]
 *     responses:
 *       200:
 *         description: 成功获取表单模板列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       title:
 *                         type: string
 *                         example: "请假申请表"
 *                       description:
 *                         type: string
 *                         example: "用于员工请假申请"
 *                       status:
 *                         type: string
 *                         example: "published"
 *                       version:
 *                         type: integer
 *                         example: 1
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-06-01T10:00:00Z"
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-06-01T10:00:00Z"
 *       500:
 *         description: 服务器错误
 */
router.get('/', formController.getAllForms);

/**
 * @swagger
 * /forms/{id}:
 *   get:
 *     summary: 获取单个表单模板
 *     tags: [Forms]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 表单模板ID
 *     responses:
 *       200:
 *         description: 成功获取表单模板
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     title:
 *                       type: string
 *                       example: "请假申请表"
 *                     description:
 *                       type: string
 *                       example: "用于员工请假申请"
 *                     status:
 *                       type: string
 *                       example: "published"
 *                     fields:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           field_key:
 *                             type: string
 *                             example: "leave_type"
 *                           field_type:
 *                             type: string
 *                             example: "select"
 *                           label:
 *                             type: string
 *                             example: "请假类型"
 *                           is_required:
 *                             type: boolean
 *                             example: true
 *       404:
 *         description: 表单模板不存在
 *       500:
 *         description: 服务器错误
 */
router.get('/:id', formController.getFormById);

/**
 * @swagger
 * /forms:
 *   post:
 *     summary: 创建表单模板
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *             properties:
 *               title:
 *                 type: string
 *                 example: "报销申请表"
 *               description:
 *                 type: string
 *                 example: "用于员工报销申请"
 *               status:
 *                 type: string
 *                 enum: [draft, published]
 *                 example: "draft"
 *               fields:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - field_key
 *                     - field_type
 *                     - label
 *                   properties:
 *                     field_key:
 *                       type: string
 *                       example: "amount"
 *                     field_type:
 *                       type: string
 *                       example: "number"
 *                     label:
 *                       type: string
 *                       example: "报销金额"
 *                     placeholder:
 *                       type: string
 *                       example: "请输入报销金额"
 *                     is_required:
 *                       type: boolean
 *                       example: true
 *                     order_index:
 *                       type: integer
 *                       example: 0
 *     responses:
 *       201:
 *         description: 表单模板创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 2
 *                     title:
 *                       type: string
 *                       example: "报销申请表"
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/', formController.createForm);

/**
 * @swagger
 * /forms/{id}:
 *   put:
 *     summary: 更新表单模板
 *     description: 更新现有表单模板的信息，包括标题、描述、状态和字段
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 表单模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: "更新后的表单标题"
 *               description:
 *                 type: string
 *                 example: "更新后的表单描述"
 *               status:
 *                 type: string
 *                 enum: [draft, published]
 *                 example: "published"
 *               schema:
 *                 type: object
 *                 example: { "layout": "vertical" }
 *               fields:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     field_key:
 *                       type: string
 *                       example: "updated_field"
 *                     field_type:
 *                       type: string
 *                       example: "text"
 *                     label:
 *                       type: string
 *                       example: "更新后的字段"
 *     responses:
 *       200:
 *         description: 表单模板更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: 没有权限执行此操作
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: 表单模板不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/:id', formController.updateForm);

/**
 * @swagger
 * /forms/{id}:
 *   delete:
 *     summary: 删除表单模板
 *     description: 删除指定ID的表单模板，只有创建者或管理员可以执行此操作
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 表单模板ID
 *     responses:
 *       200:
 *         description: 表单模板删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "表单模板删除成功"
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: 没有权限执行此操作
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: 表单模板不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete('/:id', formController.deleteForm);

/**
 * @swagger
 * /forms/{id}/versions:
 *   get:
 *     summary: 获取表单版本历史
 *     description: 获取指定表单模板的所有历史版本信息
 *     tags: [Forms]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 表单模板ID
 *     responses:
 *       200:
 *         description: 成功获取表单版本历史
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       form_template_id:
 *                         type: integer
 *                         example: 1
 *                       version:
 *                         type: integer
 *                         example: 1
 *                       schema:
 *                         type: object
 *                         example: {}
 *                       creator:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           username:
 *                             type: string
 *                             example: "admin"
 *                           full_name:
 *                             type: string
 *                             example: "系统管理员"
 *       404:
 *         description: 表单模板不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id/versions', formController.getFormVersions);

/**
 * @swagger
 * /forms/{id}/preview:
 *   get:
 *     summary: 预览表单
 *     description: 获取表单模板的预览信息，包括字段和布局
 *     tags: [Forms]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 表单模板ID
 *     responses:
 *       200:
 *         description: 成功获取表单预览
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     title:
 *                       type: string
 *                       example: "请假申请表"
 *                     description:
 *                       type: string
 *                       example: "用于员工请假申请"
 *                     schema:
 *                       type: object
 *                       example: { "layout": "vertical" }
 *                     fields:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           field_key:
 *                             type: string
 *                             example: "leave_type"
 *                           field_type:
 *                             type: string
 *                             example: "select"
 *                           label:
 *                             type: string
 *                             example: "请假类型"
 *       404:
 *         description: 表单模板不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id/preview', formController.previewForm);

module.exports = router;
