/**
 * Tests for mobile navigation
 * This file is specifically for tests that run on mobile devices
 */

const { test, expect } = require('@playwright/test');

test.describe('Mobile Navigation Tests', () => {
  test('should display login page correctly on mobile', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    
    // Verify login form is visible and properly sized
    await expect(page.locator('.login-card')).toBeVisible();
    
    // Check if elements are properly stacked on mobile
    const loginCardBox = await page.locator('.login-card').boundingBox();
    expect(loginCardBox.width).toBeLessThan(500); // Should be narrower on mobile
    
    // Verify inputs are visible and usable
    await expect(page.locator('input[placeholder="用户名"]')).toBeVisible();
    await expect(page.locator('input[placeholder="密码"]')).toBeVisible();
    await expect(page.locator('.login-button')).toBeVisible();
  });

  test('should display home page correctly on mobile', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Verify home page elements are visible
    await expect(page.locator('h1')).toContainText('欢迎使用工作流系统');
    
    // Check if module cards are stacked vertically on mobile
    const moduleCards = await page.locator('.module-card').all();
    if (moduleCards.length > 1) {
      const firstCard = await moduleCards[0].boundingBox();
      const secondCard = await moduleCards[1].boundingBox();
      
      // On mobile, cards should be stacked (second card should be below first)
      expect(secondCard.y).toBeGreaterThan(firstCard.y + firstCard.height - 10);
    }
    
    // Verify statistics are visible
    await expect(page.locator('.stats-row')).toBeVisible();
  });

  test('should display workflow form page correctly on mobile', async ({ page }) => {
    // Navigate to workflow form page
    await page.goto('/workflow-form');
    
    // Verify page title is visible
    await expect(page.locator('.card-header')).toContainText('工作流填写');
    
    // Verify tabs are visible
    await expect(page.locator('.el-tabs__item')).toBeVisible();
    
    // Verify table is visible and responsive
    await expect(page.locator('.el-table')).toBeVisible();
    
    // Check if table is responsive (horizontal scroll might be present)
    const tableWidth = await page.locator('.el-table').evaluate(el => el.scrollWidth);
    const containerWidth = await page.locator('.el-table').evaluate(el => el.clientWidth);
    
    // Log the dimensions for debugging
    console.log(`Table scroll width: ${tableWidth}px, Container width: ${containerWidth}px`);
    
    // Either the table fits (equal widths) or it has horizontal scroll (scroll width > container width)
    expect(tableWidth >= containerWidth).toBeTruthy();
  });

  test('should display workflow process page correctly on mobile', async ({ page }) => {
    // Navigate to workflow process page
    await page.goto('/workflow-process');
    
    // Verify page title is visible
    await expect(page.locator('.card-header')).toContainText('工作流处理');
    
    // Verify tabs are visible
    await expect(page.locator('.el-tabs__item')).toBeVisible();
    
    // Verify table is visible
    await expect(page.locator('.el-table')).toBeVisible();
  });

  test('should handle mobile menu navigation', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Check if mobile menu button is visible
    const menuButton = page.locator('.mobile-menu-button, .navbar-burger, .el-menu-toggle');
    const isMenuButtonVisible = await menuButton.isVisible();
    
    if (isMenuButtonVisible) {
      // Click menu button to open mobile menu
      await menuButton.click();
      
      // Verify menu items are visible
      await expect(page.locator('.el-menu-item, .nav-item')).toBeVisible();
      
      // Try to navigate to workflow form page through menu
      await page.locator('.el-menu-item, .nav-item').filter({ hasText: /工作流填写|Workflow Form/i }).click();
      
      // Verify navigation was successful
      await expect(page.locator('.card-header')).toContainText('工作流填写');
    } else {
      console.log('Mobile menu button not found, skipping menu navigation test');
      test.skip();
    }
  });

  test('should handle form inputs correctly on mobile', async ({ page }) => {
    // Navigate to workflow form page
    await page.goto('/workflow-form');
    
    // Find and click on a workflow to start
    const workflowRows = await page.locator('.el-table__row').all();
    if (workflowRows.length === 0) {
      test.skip('No workflows available to test');
      return;
    }
    
    // Click on start button of the first workflow
    await workflowRows[0].locator('button:has-text("发起")').click();
    
    // Verify start dialog is displayed
    await expect(page.locator('.el-dialog__title')).toContainText('发起工作流');
    
    // Check if form inputs are properly sized for mobile
    const titleInput = page.locator('input[placeholder="请输入工作流标题"]');
    await expect(titleInput).toBeVisible();
    
    // Fill the title input
    await titleInput.fill('移动测试' + Date.now());
    
    // Verify input value was set correctly
    await expect(titleInput).toHaveValue(/移动测试/);
    
    // Close dialog
    await page.locator('.el-dialog__close').click();
  });
});
