<template>
  <div class="message-center">
    <el-badge :value="unreadCount" :max="99" class="message-badge" :hidden="unreadCount === 0">
      <el-button class="message-button" circle @click="toggleMessagePanel">
        <el-icon><i-ep-bell /></el-icon>
      </el-button>
    </el-badge>
    
    <el-drawer
      v-model="drawerVisible"
      title="消息中心"
      direction="rtl"
      size="350px"
      :destroy-on-close="false"
      class="message-drawer"
      :z-index="2000"
    >
      <template #header>
        <div class="drawer-header">
          <div class="drawer-title">
            <el-icon class="drawer-icon"><i-ep-bell /></el-icon>
            <span>消息中心</span>
          </div>
          <div class="drawer-actions">
            <el-button type="primary" text @click="markAllAsRead" :disabled="unreadCount === 0">
              <el-icon><i-ep-check /></el-icon>
              <span>全部已读</span>
            </el-button>
            <el-button type="danger" text @click="clearAllMessages" :disabled="messages.length === 0">
              <el-icon><i-ep-delete /></el-icon>
              <span>清空消息</span>
            </el-button>
          </div>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" class="message-tabs">
        <el-tab-pane label="全部消息" name="all">
          <el-badge :value="messages.length" class="tab-badge" type="info" v-if="messages.length > 0" />
          <el-empty v-if="messages.length === 0" description="暂无消息">
            <template #image>
              <el-icon class="empty-icon"><i-ep-message-box /></el-icon>
            </template>
          </el-empty>
          <el-scrollbar height="calc(100vh - 250px)" v-else>
            <div class="message-list">
              <div
                v-for="message in messages"
                :key="message.id"
                class="message-item"
                :class="{ 'unread': !message.read }"
                @click="readMessage(message)"
              >
                <div class="message-icon" :class="message.type">
                  <el-icon v-if="message.type === 'info'"><i-ep-info-filled /></el-icon>
                  <el-icon v-else-if="message.type === 'warning'"><i-ep-warning-filled /></el-icon>
                  <el-icon v-else-if="message.type === 'success'"><i-ep-success-filled /></el-icon>
                  <el-icon v-else><i-ep-bell-filled /></el-icon>
                </div>
                <div class="message-content">
                  <div class="message-title">{{ message.title }}</div>
                  <div class="message-body">{{ message.content }}</div>
                  <div class="message-time">{{ formatTime(message.time) }}</div>
                </div>
                <div class="message-actions">
                  <el-tooltip content="删除消息" placement="top" :offset="8">
                    <el-button
                      circle
                      size="small"
                      class="delete-button"
                      @click.stop="deleteMessage(message.id)"
                    >
                      <el-icon><i-ep-delete /></el-icon>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="未读消息" name="unread">
          <el-badge :value="unreadCount" class="tab-badge" type="danger" v-if="unreadCount > 0" />
          <el-empty v-if="unreadMessages.length === 0" description="暂无未读消息">
            <template #image>
              <el-icon class="empty-icon"><i-ep-chat-dot-round /></el-icon>
            </template>
          </el-empty>
          <el-scrollbar height="calc(100vh - 250px)" v-else>
            <div class="message-list">
              <div
                v-for="message in unreadMessages"
                :key="message.id"
                class="message-item unread"
                @click="readMessage(message)"
              >
                <div class="message-icon" :class="message.type">
                  <el-icon v-if="message.type === 'info'"><i-ep-info-filled /></el-icon>
                  <el-icon v-else-if="message.type === 'warning'"><i-ep-warning-filled /></el-icon>
                  <el-icon v-else-if="message.type === 'success'"><i-ep-success-filled /></el-icon>
                  <el-icon v-else><i-ep-bell-filled /></el-icon>
                </div>
                <div class="message-content">
                  <div class="message-title">{{ message.title }}</div>
                  <div class="message-body">{{ message.content }}</div>
                  <div class="message-time">{{ formatTime(message.time) }}</div>
                </div>
                <div class="message-actions">
                  <el-tooltip content="删除消息" placement="top" :offset="8">
                    <el-button
                      circle
                      size="small"
                      class="delete-button"
                      @click.stop="deleteMessage(message.id)"
                    >
                      <el-icon><i-ep-delete /></el-icon>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 消息面板显示状态
const drawerVisible = ref(false)
const activeTab = ref('all')

// 模拟消息数据
const messages = ref([
  {
    id: 1,
    title: '工作流审批提醒',
    content: '您有一个新的工作流等待审批',
    time: new Date(Date.now() - 1000 * 60 * 5), // 5分钟前
    read: false,
    type: 'info'
  },
  {
    id: 2,
    title: '系统通知',
    content: '系统将于今晚22:00-23:00进行维护更新',
    time: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
    read: false,
    type: 'warning'
  },
  {
    id: 3,
    title: '工作流已完成',
    content: '您发起的报销申请工作流已完成审批',
    time: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
    read: true,
    type: 'success'
  }
])

// 计算未读消息
const unreadMessages = computed(() => {
  return messages.value.filter(msg => !msg.read)
})

// 未读消息数量
const unreadCount = computed(() => {
  return unreadMessages.value.length
})

// 切换消息面板
const toggleMessagePanel = () => {
  drawerVisible.value = !drawerVisible.value
  console.log('Drawer visibility changed to:', drawerVisible.value)
}

// 阅读消息
const readMessage = (message) => {
  message.read = true
  
  // 如果在未读消息标签页且没有未读消息了，切换到全部消息标签页
  if (activeTab.value === 'unread' && unreadCount.value === 0) {
    setTimeout(() => {
      activeTab.value = 'all'
    }, 300)
  }
}

// 删除消息
const deleteMessage = (id) => {
  messages.value = messages.value.filter(msg => msg.id !== id)
}

// 全部标记为已读
const markAllAsRead = () => {
  messages.value.forEach(msg => {
    msg.read = true
  })
  
  // 如果在未读消息标签页，切换到全部消息标签页
  if (activeTab.value === 'unread') {
    setTimeout(() => {
      activeTab.value = 'all'
    }, 300)
  }
}

// 清空所有消息
const clearAllMessages = () => {
  messages.value = []
}

// 格式化时间
const formatTime = (time) => {
  return formatDistanceToNow(new Date(time), { addSuffix: true, locale: zhCN })
}

// 监听抽屉关闭
watch(drawerVisible, (newVal) => {
  if (!newVal && unreadCount.value === 0) {
    // 关闭抽屉时，如果没有未读消息，切换到全部消息标签页
    activeTab.value = 'all'
  }
})

// 模拟获取消息
onMounted(() => {
  // 这里可以添加从API获取消息的逻辑
  console.log('MessageCenter component mounted, unread count:', unreadCount.value)
})
</script>

<style scoped>
.message-center {
  position: relative;
  z-index: 100;
}

.message-badge {
  margin-right: 16px;
}

.message-button {
  width: 40px;
  height: 40px;
  background: linear-gradient(145deg, var(--background-color-light), var(--background-color));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  z-index: 100;
}

.message-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.message-button:active {
  transform: translateY(0) scale(0.95);
}

.message-drawer :deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 0;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color-light);
}

.drawer-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.drawer-icon {
  margin-right: 8px;
  font-size: 20px;
  color: var(--primary-color);
}

.drawer-actions {
  display: flex;
  gap: 8px;
}

.drawer-actions .el-button {
  display: flex;
  align-items: center;
}

.drawer-actions .el-button .el-icon {
  margin-right: 4px;
}

.message-tabs {
  padding: 0 16px;
}

.message-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 4px;
}

.message-tabs :deep(.el-tabs__item) {
  padding: 0 16px;
  height: 50px;
  line-height: 50px;
  position: relative;
}

.tab-badge {
  position: absolute;
  top: 10px;
  right: 2px;
  transform: scale(0.8);
}

.empty-icon {
  font-size: 60px;
  color: var(--text-color-placeholder);
}

.message-list {
  padding: 16px 0;
}

.message-item {
  display: flex;
  padding: 14px 16px;
  border-radius: 12px;
  margin-bottom: 12px;
  background-color: var(--background-color-light);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

.message-item:hover {
  background-color: var(--primary-color-bg);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.05);
}

.message-item.unread {
  background-color: rgba(64, 158, 255, 0.08);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.message-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 70%;
  background: linear-gradient(to bottom, #60a5fa, #3b82f6);
  border-radius: 0 2px 2px 0;
}

.message-icon {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  flex-shrink: 0;
  transition: all 0.3s;
}

.message-icon .el-icon {
  font-size: 20px;
  transition: all 0.3s;
}

.message-item:hover .message-icon .el-icon {
  transform: scale(1.1);
}

.message-icon.info {
  background: linear-gradient(145deg, rgba(64, 158, 255, 0.1), rgba(64, 158, 255, 0.2));
  color: #409EFF;
}

.message-icon.warning {
  background: linear-gradient(145deg, rgba(230, 162, 60, 0.1), rgba(230, 162, 60, 0.2));
  color: #E6A23C;
}

.message-icon.success {
  background: linear-gradient(145deg, rgba(103, 194, 58, 0.1), rgba(103, 194, 58, 0.2));
  color: #67C23A;
}

.message-content {
  flex: 1;
  overflow: hidden;
}

.message-title {
  font-weight: 600;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-body {
  color: var(--text-color-secondary);
  font-size: 14px;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-time {
  color: var(--text-color-tertiary);
  font-size: 12px;
}

.message-actions {
  opacity: 0;
  transition: all 0.3s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.delete-button {
  background-color: var(--background-color-light);
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.delete-button:hover {
  background-color: var(--color-danger);
  color: white;
  transform: rotate(15deg);
}

/* 暗黑模式适配 */
:global(.dark-mode) .message-button {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

:global(.dark-mode) .message-item {
  background-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

:global(.dark-mode) .message-item:hover {
  background-color: rgba(255, 255, 255, 0.08);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

:global(.dark-mode) .message-item.unread {
  background-color: rgba(64, 158, 255, 0.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

:global(.dark-mode) .delete-button {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.message-badge :deep(.el-badge__content.is-fixed) {
  animation: pulse 2s infinite ease-in-out;
}

/* 确保未读数字的可见性 */
.message-badge :deep(.el-badge__content) {
  background-color: var(--color-danger) !important;
  color: white !important;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(245, 108, 108, 0.5);
}
</style> 