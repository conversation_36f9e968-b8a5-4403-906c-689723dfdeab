<template>
  <div class="workflow-metrics">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="metrics-card time-metrics" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>时间指标</span>
            </div>
          </template>
          <div ref="timeChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="metrics-card progress-metrics" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>进度指标</span>
            </div>
          </template>
          <div ref="progressChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import * as echarts from 'echarts'

// 接收工作流实例作为props
const props = defineProps({
  workflowInstance: {
    type: Object,
    required: true
  }
})

const timeChartRef = ref(null)
const progressChartRef = ref(null)
let timeChart = null
let progressChart = null

// 计算工作流流转时间
const timeMetrics = computed(() => {
  if (!props.workflowInstance || !props.workflowInstance.taskHistories || !Array.isArray(props.workflowInstance.taskHistories)) {
    return { totalDuration: 0, nodesDuration: [] }
  }

  const taskHistories = props.workflowInstance.taskHistories
  const nodes = props.workflowInstance.workflowTemplate?.nodes || []
  
  // 按节点分组历史记录
  const nodeHistories = {}
  
  taskHistories.forEach(history => {
    if (!history.node_id) return
    
    if (!nodeHistories[history.node_id]) {
      nodeHistories[history.node_id] = []
    }
    
    nodeHistories[history.node_id].push(history)
  })
  
  // 计算每个节点的处理时间
  const nodesDuration = []
  let totalDuration = 0
  
  Object.keys(nodeHistories).forEach(nodeId => {
    const histories = nodeHistories[nodeId]
    const node = nodes.find(n => n.id.toString() === nodeId.toString())
    
    if (!node || histories.length === 0) return
    
    // 获取节点的第一个和最后一个历史记录
    const firstRecord = histories.reduce((min, curr) => 
      new Date(curr.created_at) < new Date(min.created_at) ? curr : min, histories[0])
    
    const lastRecord = histories.reduce((max, curr) => 
      new Date(curr.created_at) > new Date(max.created_at) ? curr : max, histories[0])
    
    // 计算时间差（小时）
    const startTime = new Date(firstRecord.created_at)
    const endTime = new Date(lastRecord.created_at)
    const duration = (endTime - startTime) / (1000 * 60 * 60)  // 转换为小时
    
    nodesDuration.push({
      nodeId: nodeId.toString(),
      nodeName: node.name,
      duration: Math.max(0.1, duration)  // 至少显示0.1小时
    })
    
    totalDuration += duration
  })
  
  // 按持续时间排序
  nodesDuration.sort((a, b) => b.duration - a.duration)
  
  return {
    totalDuration,
    nodesDuration: nodesDuration.slice(0, 5)  // 只取前5个
  }
})

// 计算工作流进度
const progressMetrics = computed(() => {
  if (!props.workflowInstance || !props.workflowInstance.workflowTemplate) {
    return { completion: 0, nodeStatus: [] }
  }
  
  const template = props.workflowInstance.workflowTemplate
  const nodes = template.nodes || []
  const currentNodeId = props.workflowInstance.currentNode?.id
  const status = props.workflowInstance.status
  
  // 收集已完成的节点ID
  const completedNodeIds = new Set()
  if (props.workflowInstance.taskHistories && Array.isArray(props.workflowInstance.taskHistories)) {
    props.workflowInstance.taskHistories.forEach(history => {
      if (history.node_id && history.operation === 'approve') {
        completedNodeIds.add(history.node_id)
      }
    })
  }
  
  // 统计节点状态
  const statusCount = {
    completed: 0,
    current: 0,
    pending: 0
  }
  
  const nodeStatus = nodes.map(node => {
    let status = 'pending'
    
    if (completedNodeIds.has(node.id)) {
      status = 'completed'
      statusCount.completed++
    } else if (node.id === currentNodeId) {
      status = 'current'
      statusCount.current++
    } else {
      statusCount.pending++
    }
    
    return {
      nodeId: node.id.toString(),
      nodeName: node.name,
      nodeType: node.node_type,
      status
    }
  })
  
  // 计算完成百分比
  const totalNodes = Math.max(1, nodes.length)  // 避免除以0
  const completion = Math.round((statusCount.completed / totalNodes) * 100)
  
  // 工作流是否已完成
  const isCompleted = status === 'completed'
  
  return {
    completion: isCompleted ? 100 : completion,
    statusCount,
    nodeStatus,
    isCompleted
  }
})

// 初始化时间指标图表
const initTimeChart = async () => {
  if (!timeChartRef.value) return
  
  await nextTick()
  
  if (timeChart) {
    timeChart.dispose()
  }
  
  timeChart = echarts.init(timeChartRef.value)
  
  const { nodesDuration } = timeMetrics.value
  
  // 图表数据
  const data = nodesDuration.map(item => ({
    value: parseFloat(item.duration.toFixed(1)),
    name: item.nodeName
  }))
  
  // 图表配置
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}小时 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      type: 'scroll'
    },
    series: [
      {
        name: '节点处理时间',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }
  
  // 渲染图表
  timeChart.setOption(option)
}

// 初始化进度指标图表
const initProgressChart = async () => {
  if (!progressChartRef.value) return
  
  await nextTick()
  
  if (progressChart) {
    progressChart.dispose()
  }
  
  progressChart = echarts.init(progressChartRef.value)
  
  const { statusCount, completion, isCompleted } = progressMetrics.value
  
  // 图表配置
  const option = {
    tooltip: {
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: ['已完成', '当前处理', '待处理']
    },
    series: [
      {
        type: 'pie',
        radius: '70%',
        center: ['50%', '50%'],
        data: [
          { value: statusCount.completed, name: '已完成', itemStyle: { color: '#67C23A' } },
          { value: statusCount.current, name: '当前处理', itemStyle: { color: '#409EFF' } },
          { value: statusCount.pending, name: '待处理', itemStyle: { color: '#909399' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      },
      {
        name: '进度',
        type: 'gauge',
        radius: '40%',
        center: ['50%', '85%'],
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        splitNumber: 10,
        axisLine: {
          lineStyle: {
            width: 6,
            color: [
              [0.25, '#FF6E76'],
              [0.5, '#FDDD60'],
              [0.75, '#58D9F9'],
              [1, '#7CFFB2']
            ]
          }
        },
        pointer: {
          icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
          length: '12%',
          width: 20,
          offsetCenter: [0, '-60%'],
          itemStyle: {
            color: 'inherit'
          }
        },
        axisTick: {
          length: 12,
          lineStyle: {
            color: 'inherit',
            width: 2
          }
        },
        splitLine: {
          length: 20,
          lineStyle: {
            color: 'inherit',
            width: 2
          }
        },
        axisLabel: {
          color: '#999',
          fontSize: 10
        },
        title: {
          offsetCenter: [0, '-20%'],
          fontSize: 14
        },
        detail: {
          fontSize: 18,
          color: 'inherit',
          formatter: '{value}%',
          valueAnimation: true,
          offsetCenter: [0, '0%']
        },
        data: [
          {
            value: completion,
            name: '完成进度',
            title: {
              offsetCenter: ['0%', '-40%']
            }
          }
        ]
      }
    ]
  }
  
  // 渲染图表
  progressChart.setOption(option)
}

// 监听工作流实例变化
watch(() => props.workflowInstance, () => {
  initTimeChart()
  initProgressChart()
}, { deep: true })

// 组件挂载时初始化图表
onMounted(() => {
  initTimeChart()
  initProgressChart()
  
  // 自适应大小
  window.addEventListener('resize', () => {
    if (timeChart) {
      timeChart.resize()
    }
    if (progressChart) {
      progressChart.resize()
    }
  })
})
</script>

<style scoped>
.workflow-metrics {
  margin: 20px 0;
}

.metrics-card {
  height: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}
</style> 