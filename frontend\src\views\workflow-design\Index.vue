<template>
  <div class="workflow-design-container">
    <el-row :gutter="20" class="mb-4">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>工作流设计器</span>
              <el-button
                type="primary"
                @click="handleCreateWorkflow">
                创建工作流
              </el-button>
            </div>
          </template>

          <el-table
            :data="workflows"
            border
            v-loading="loading">
            <el-table-column prop="title" label="工作流名称" width="180" />
            <el-table-column prop="description" label="描述" />
            <el-table-column label="关联表单" width="150">
              <template #default="scope">
                {{ scope.row.formTemplate ? scope.row.formTemplate.title : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="创建者" width="120">
              <template #default="scope">
                {{ scope.row.creator ? scope.row.creator.full_name : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="version" label="版本" width="80" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="更新时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.updated_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="scope">
                <el-button
                  size="small"
                  @click="handleEditWorkflow(scope.row)"
                  type="primary">
                  编辑
                </el-button>
                <el-button
                  size="small"
                  @click="handlePreviewWorkflow(scope.row)"
                  type="success">
                  预览
                </el-button>
                <el-button
                  size="small"
                  @click="handleViewVersions(scope.row)"
                  type="info">
                  版本
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="handleDeleteWorkflow(scope.row)"
                  v-if="canDelete(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 工作流设计对话框 -->
    <el-dialog
      v-model="workflowDialogVisible"
      :title="isEdit ? '编辑工作流' : '创建工作流'"
      width="90%"
      fullscreen>
      <div class="workflow-designer">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-card class="components-panel">
              <template #header>
                <div class="card-header">
                  <span>节点面板</span>
                </div>
              </template>
              <div class="components-list">
                <div
                  v-for="node in nodeTypes"
                  :key="node.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="handleDragStart($event, node)">
                  <el-icon>
                    <component :is="node.icon"></component>
                  </el-icon>
                  <span>{{ node.label }}</span>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="14">
            <el-card class="design-panel">
              <template #header>
                <div class="card-header">
                  <span>设计面板</span>
                  <div>
                    <el-button @click="handleClearDesign" type="danger" plain size="small">
                      清空
                    </el-button>
                  </div>
                </div>
              </template>

              <div
                class="workflow-canvas"
                @dragover="handleDragOver"
                @drop="handleDrop"
                @click="deselectNode"
                ref="canvasRef">
                <template v-if="workflowNodes.length === 0">
                  <div class="empty-tip">
                    <el-empty description="拖拽左侧节点到此处进行工作流设计" />
                  </div>
                </template>

                <div
                  v-for="(node, index) in workflowNodes"
                  :key="node.node_key"
                  class="workflow-node"
                  :data-key="node.node_key"
                  :class="{
                    'active': selectedNodeIndex === index,
                    'start-node': node.node_type === 'start',
                    'end-node': node.node_type === 'end',
                    'approval-node': node.node_type === 'approval',
                    'condition-node': node.node_type === 'condition',
                    'task-node': node.node_type === 'task'
                  }"
                  :style="{
                    left: `${node.position_x}px`,
                    top: `${node.position_y}px`
                  }"
                  @click.stop="selectNode(index)"
                  @mousedown="startDrag($event, index)">
                  <div class="node-content">
                    <div class="node-icon">
                      <el-icon>
                        <component :is="getNodeIcon(node.node_type)"></component>
                      </el-icon>
                    </div>
                    <div class="node-title">{{ node.name }}</div>
                  </div>
                  <div class="node-ports">
                    <div class="port port-out" @mousedown.stop="startConnection(node)"></div>
                  </div>
                </div>

                <!-- 连线 -->
                <svg class="connections-layer">
                  <path
                    v-for="(transition, index) in workflowTransitions"
                    :key="index"
                    :d="getPathData(transition)"
                    :class="{ 'active': selectedTransitionIndex === index }"
                    @click.stop="selectTransition(index)"
                    fill="none"
                    stroke="#909399"
                    stroke-width="2"></path>
                </svg>

                <!-- 临时连线 -->
                <svg class="temp-connection-layer" v-if="tempConnection.active">
                  <path
                    :d="getTempPathData()"
                    fill="none"
                    stroke="#409EFF"
                    stroke-width="2"
                    stroke-dasharray="5,5"></path>
                </svg>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="properties-panel">
              <template #header>
                <div class="card-header">
                  <span>属性面板</span>
                </div>
              </template>

              <div v-if="selectedNodeIndex === -1 && selectedTransitionIndex === -1">
                <el-form label-width="100px">
                  <el-form-item label="工作流名称">
                    <el-input v-model="workflowInfo.title" placeholder="请输入工作流名称"></el-input>
                  </el-form-item>
                  <el-form-item label="工作流描述">
                    <el-input
                      v-model="workflowInfo.description"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入工作流描述"></el-input>
                  </el-form-item>
                  <el-form-item label="关联表单">
                    <el-select
                      v-model="workflowInfo.form_template_id"
                      placeholder="请选择关联表单"
                      style="width: 100%">
                      <el-option
                        v-for="form in forms"
                        :key="form.id"
                        :label="form.title"
                        :value="form.id"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="工作流状态">
                    <el-select v-model="workflowInfo.status" style="width: 100%">
                      <el-option label="草稿" value="draft"></el-option>
                      <el-option label="已发布" value="published"></el-option>
                      <el-option label="已禁用" value="disabled"></el-option>
                    </el-select>
                  </el-form-item>
                </el-form>
              </div>

              <div v-else-if="selectedNodeIndex !== -1 && selectedNode">
                <el-form label-width="100px">
                  <el-form-item label="节点名称">
                    <el-input v-model="selectedNode.name" placeholder="请输入节点名称"></el-input>
                  </el-form-item>
                  <el-form-item label="节点描述">
                    <el-input
                      v-model="selectedNode.description"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入节点描述"></el-input>
                  </el-form-item>

                  <!-- 审批节点配置 -->
                  <template v-if="selectedNode.node_type === 'approval'">
                    <el-divider>审批人配置</el-divider>
                    <el-form-item label="审批类型">
                      <el-select
                        v-model="selectedNode.config.assignee_type"
                        placeholder="请选择审批类型"
                        style="width: 100%">
                        <el-option label="指定用户" value="user"></el-option>
                        <el-option label="指定角色" value="role"></el-option>
                        <el-option label="指定部门" value="department"></el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="审批人" v-if="selectedNode.config.assignee_type === 'user'">
                      <el-select
                        v-model="selectedNode.config.assignee_id"
                        placeholder="请选择审批人"
                        style="width: 100%">
                        <el-option
                          v-for="user in users"
                          :key="user.id"
                          :label="user.full_name"
                          :value="user.id"></el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="审批角色" v-if="selectedNode.config.assignee_type === 'role'">
                      <el-select
                        v-model="selectedNode.config.assignee_role_id"
                        placeholder="请选择审批角色"
                        style="width: 100%">
                        <el-option
                          v-for="role in roles"
                          :key="role.id"
                          :label="role.name"
                          :value="role.id"></el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="审批部门" v-if="selectedNode.config.assignee_type === 'department'">
                      <el-select
                        v-model="selectedNode.config.assignee_department_id"
                        placeholder="请选择审批部门"
                        style="width: 100%">
                        <el-option
                          v-for="dept in departments"
                          :key="dept.id"
                          :label="dept.name"
                          :value="dept.id"></el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="审批期限">
                      <el-input-number
                        v-model="selectedNode.config.due_days"
                        :min="0"
                        placeholder="审批期限(天)"></el-input-number>
                    </el-form-item>

                    <el-form-item label="优先级">
                      <el-select
                        v-model="selectedNode.config.priority"
                        placeholder="请选择优先级"
                        style="width: 100%">
                        <el-option label="低" value="low"></el-option>
                        <el-option label="中" value="normal"></el-option>
                        <el-option label="高" value="high"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>

                  <!-- 任务节点配置 -->
                  <template v-if="selectedNode.node_type === 'task'">
                    <el-divider>任务配置</el-divider>
                    <el-form-item label="执行人类型">
                      <el-select
                        v-model="selectedNode.config.assignee_type"
                        placeholder="请选择执行人类型"
                        style="width: 100%">
                        <el-option label="指定用户" value="user"></el-option>
                        <el-option label="指定角色" value="role"></el-option>
                        <el-option label="指定部门" value="department"></el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="执行人" v-if="selectedNode.config.assignee_type === 'user'">
                      <el-select
                        v-model="selectedNode.config.assignee_id"
                        placeholder="请选择执行人"
                        style="width: 100%">
                        <el-option
                          v-for="user in users"
                          :key="user.id"
                          :label="user.full_name"
                          :value="user.id"></el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="执行角色" v-if="selectedNode.config.assignee_type === 'role'">
                      <el-select
                        v-model="selectedNode.config.assignee_role_id"
                        placeholder="请选择执行角色"
                        style="width: 100%">
                        <el-option
                          v-for="role in roles"
                          :key="role.id"
                          :label="role.name"
                          :value="role.id"></el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="执行部门" v-if="selectedNode.config.assignee_type === 'department'">
                      <el-select
                        v-model="selectedNode.config.assignee_department_id"
                        placeholder="请选择执行部门"
                        style="width: 100%">
                        <el-option
                          v-for="dept in departments"
                          :key="dept.id"
                          :label="dept.name"
                          :value="dept.id"></el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="任务期限">
                      <el-input-number
                        v-model="selectedNode.config.due_days"
                        :min="0"
                        placeholder="任务期限(天)"></el-input-number>
                    </el-form-item>

                    <el-form-item label="优先级">
                      <el-select
                        v-model="selectedNode.config.priority"
                        placeholder="请选择优先级"
                        style="width: 100%">
                        <el-option label="低" value="low"></el-option>
                        <el-option label="中" value="normal"></el-option>
                        <el-option label="高" value="high"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>

                  <!-- 条件节点配置 -->
                  <template v-if="selectedNode.node_type === 'condition'">
                    <el-divider>条件配置</el-divider>
                    <el-form-item label="条件表达式">
                      <el-input
                        v-model="selectedNode.config.condition"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入条件表达式"></el-input>
                    </el-form-item>
                    <el-alert
                      title="条件表达式说明"
                      type="info"
                      description="使用JavaScript表达式，可引用表单字段，如：form.amount > 1000"
                      :closable="false"
                      show-icon>
                    </el-alert>
                  </template>

                  <el-divider>操作</el-divider>
                  <el-button
                    type="danger"
                    @click="removeNode"
                    v-if="selectedNode.node_type !== 'start' && selectedNode.node_type !== 'end'">
                    删除节点
                  </el-button>
                </el-form>
              </div>

              <div v-else-if="selectedTransitionIndex !== -1 && selectedTransition">
                <el-form label-width="100px">
                  <el-form-item label="连线条件">
                    <el-input
                      v-model="selectedTransition.condition"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入连线条件"></el-input>
                  </el-form-item>
                  <el-alert
                    title="条件表达式说明"
                    type="info"
                    description="使用JavaScript表达式，可引用表单字段，如：form.amount > 1000"
                    :closable="false"
                    show-icon>
                  </el-alert>

                  <el-divider>操作</el-divider>
                  <el-button
                    type="danger"
                    @click="removeTransition">
                    删除连线
                  </el-button>
                </el-form>
              </div>

              <div v-else>
                <el-empty description="请选择一个节点或连线进行配置" />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="workflowDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveWorkflow" :loading="saving">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 工作流预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="工作流预览"
      width="80%">
      <div v-if="previewWorkflow">
        <h2>{{ previewWorkflow.title }}</h2>
        <p v-if="previewWorkflow.description">{{ previewWorkflow.description }}</p>

        <el-divider>基本信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工作流名称">{{ previewWorkflow.title }}</el-descriptions-item>
          <el-descriptions-item label="版本">{{ previewWorkflow.version }}</el-descriptions-item>
          <el-descriptions-item label="关联表单">{{ previewWorkflow.formTemplate ? previewWorkflow.formTemplate.title : '-' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(previewWorkflow.status)">
              {{ getStatusText(previewWorkflow.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建者">{{ previewWorkflow.creator ? previewWorkflow.creator.full_name : '-' }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(previewWorkflow.updated_at) }}</el-descriptions-item>
        </el-descriptions>

        <el-divider>节点信息</el-divider>
        <el-table :data="previewWorkflow.nodes" border>
          <el-table-column prop="name" label="节点名称" width="150" />
          <el-table-column prop="node_type" label="节点类型" width="100">
            <template #default="scope">
              {{ getNodeTypeText(scope.row.node_type) }}
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" />
        </el-table>

        <el-divider>流转路径</el-divider>
        <el-table :data="previewWorkflow.transitions" border>
          <el-table-column label="源节点" width="150">
            <template #default="scope">
              {{ scope.row.sourceNode ? scope.row.sourceNode.name : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="目标节点" width="150">
            <template #default="scope">
              {{ scope.row.targetNode ? scope.row.targetNode.name : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="condition" label="条件" />
        </el-table>
      </div>
    </el-dialog>

    <!-- 工作流版本对话框 -->
    <el-dialog
      v-model="versionsDialogVisible"
      title="工作流版本历史"
      width="60%">
      <el-table :data="workflowVersions" border v-loading="versionsLoading">
        <el-table-column prop="version" label="版本号" width="100" />
        <el-table-column label="创建者" width="120">
          <template #default="scope">
            {{ scope.row.creator ? scope.row.creator.full_name : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button
              size="small"
              @click="handlePreviewVersion(scope.row)"
              type="primary">
              预览
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { workflowApi, formApi, userApi, departmentApi } from '@/api'
import {
  CircleCheck, CircleClose, Document, Setting, Connection,
  Finished, Switch, Timer, Select, Check
} from '@element-plus/icons-vue'

// 数据
const workflows = ref([])
const forms = ref([])
const users = ref([])
const departments = ref([])
const roles = ref([])
const workflowVersions = ref([])
const loading = ref(false)
const versionsLoading = ref(false)
const saving = ref(false)
const workflowDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const versionsDialogVisible = ref(false)
const isEdit = ref(false)
const currentWorkflowId = ref(null)
const selectedNodeIndex = ref(-1)
const selectedTransitionIndex = ref(-1)
const previewWorkflow = ref(null)
const canvasRef = ref(null)

// 工作流信息
const workflowInfo = reactive({
  title: '',
  description: '',
  form_template_id: null,
  status: 'draft'
})

// 工作流节点
const workflowNodes = ref([])

// 工作流转换
const workflowTransitions = ref([])

// 临时连线
const tempConnection = reactive({
  active: false,
  sourceNode: null,
  sourceX: 0,
  sourceY: 0,
  targetX: 0,
  targetY: 0
})

// 拖拽状态
const dragState = reactive({
  isDragging: false,
  nodeIndex: -1,
  startX: 0,
  startY: 0,
  offsetX: 0,
  offsetY: 0
})

// 可用节点类型
const nodeTypes = [
  { type: 'start', label: '开始节点', icon: 'CircleCheck' },
  { type: 'end', label: '结束节点', icon: 'CircleClose' },
  { type: 'approval', label: '审批节点', icon: 'Document' },
  { type: 'task', label: '任务节点', icon: 'Setting' },
  { type: 'condition', label: '条件节点', icon: 'Connection' }
]

// 计算属性
const selectedNode = computed(() => {
  if (selectedNodeIndex.value >= 0 && selectedNodeIndex.value < workflowNodes.value.length) {
    return workflowNodes.value[selectedNodeIndex.value]
  }
  return null
})

const selectedTransition = computed(() => {
  if (selectedTransitionIndex.value >= 0 && selectedTransitionIndex.value < workflowTransitions.value.length) {
    return workflowTransitions.value[selectedTransitionIndex.value]
  }
  return null
})

const currentForm = ref(null)

// 初始化
onMounted(async () => {
  await fetchWorkflows()
  await fetchForms()
  await fetchUsers()
  await fetchDepartments()
  await fetchRoles()

  // 添加全局事件监听
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
})

// 获取工作流列表
const fetchWorkflows = async () => {
  loading.value = true
  try {
    const result = await workflowApi.getAllWorkflows()
    if (result.success) {
      workflows.value = result.data
    }
  } catch (error) {
    console.error('获取工作流列表失败:', error)
    ElMessage.error('获取工作流列表失败')
  } finally {
    loading.value = false
  }
}

// 获取表单列表
const fetchForms = async () => {
  try {
    const result = await formApi.getAllForms()
    if (result.success) {
      forms.value = result.data
    }
  } catch (error) {
    console.error('获取表单列表失败:', error)
    ElMessage.error('获取表单列表失败')
  }
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    const result = await userApi.getAllUsers()
    if (result.success) {
      users.value = result.data
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const result = await departmentApi.getAllDepartments()
    if (result.success) {
      departments.value = result.data
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 获取角色列表
const fetchRoles = async () => {
  try {
    // 假设有获取角色列表的API
    // const result = await roleApi.getAllRoles()
    // if (result.success) {
    //   roles.value = result.data
    // }

    // 临时使用模拟数据
    roles.value = [
      { id: 1, name: '管理员' },
      { id: 2, name: '部门经理' },
      { id: 3, name: '普通员工' }
    ]
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
  }
}

// 获取工作流版本历史
const fetchWorkflowVersions = async (workflowId) => {
  versionsLoading.value = true
  try {
    const result = await workflowApi.getWorkflowVersions(workflowId)
    if (result.success) {
      workflowVersions.value = result.data
    }
  } catch (error) {
    console.error('获取工作流版本历史失败:', error)
    ElMessage.error('获取工作流版本历史失败')
  } finally {
    versionsLoading.value = false
  }
}

// 创建工作流
const handleCreateWorkflow = () => {
  resetWorkflowDesigner()
  isEdit.value = false
  currentWorkflowId.value = null
  workflowDialogVisible.value = true

  // 添加开始节点
  nextTick(() => {
    addNode('start', 'Start', 100, 100)
  })
}

// 编辑工作流
const handleEditWorkflow = async (workflow) => {
  resetWorkflowDesigner()
  isEdit.value = true
  currentWorkflowId.value = workflow.id

  try {
    const result = await workflowApi.getWorkflowById(workflow.id)
    if (result.success) {
      const workflowData = result.data

      // 设置工作流基本信息
      workflowInfo.title = workflowData.title
      workflowInfo.description = workflowData.description || ''
      workflowInfo.form_template_id = workflowData.form_template_id
      workflowInfo.status = workflowData.status

      // 设置工作流节点
      if (workflowData.nodes && workflowData.nodes.length > 0) {
        workflowNodes.value = workflowData.nodes.map(node => {
          // 确保配置对象存在
          if (!node.config) {
            node.config = {}
          }

          return node
        })
      }

      // 设置工作流转换
      if (workflowData.transitions && workflowData.transitions.length > 0) {
        workflowTransitions.value = workflowData.transitions.map(transition => {
          return {
            source_node_key: transition.sourceNode.node_key,
            target_node_key: transition.targetNode.node_key,
            condition: transition.condition
          }
        })
      }

      workflowDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取工作流详情失败:', error)
    ElMessage.error('获取工作流详情失败')
  }
}

// 预览工作流
const handlePreviewWorkflow = async (workflow) => {
  try {
    const result = await workflowApi.previewWorkflow(workflow.id)
    if (result.success) {
      previewWorkflow.value = result.data
      previewDialogVisible.value = true
    }
  } catch (error) {
    console.error('预览工作流失败:', error)
    ElMessage.error('预览工作流失败')
  }
}

// 预览工作流版本
const handlePreviewVersion = (version) => {
  // 解析版本的schema
  const schema = version.schema

  previewWorkflow.value = {
    title: `${currentForm.value.title} (版本 ${version.version})`,
    description: currentForm.value.description,
    version: version.version,
    status: 'draft',
    creator: version.creator,
    updated_at: version.created_at,
    nodes: schema.nodes || [],
    transitions: schema.transitions || []
  }

  previewDialogVisible.value = true
}

// 查看工作流版本历史
const handleViewVersions = (workflow) => {
  currentWorkflowId.value = workflow.id
  currentForm.value = workflow
  fetchWorkflowVersions(workflow.id)
  versionsDialogVisible.value = true
}

// 删除工作流
const handleDeleteWorkflow = (workflow) => {
  ElMessageBox.confirm(
    '此操作将永久删除该工作流，是否继续?',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const result = await workflowApi.deleteWorkflow(workflow.id)
      if (result.success) {
        ElMessage.success('删除成功')
        fetchWorkflows()
      } else {
        ElMessage.error(result.message || '删除失败')
      }
    } catch (error) {
      console.error('删除工作流失败:', error)
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 拖拽开始
const handleDragStart = (event, node) => {
  event.dataTransfer.setData('nodeType', node.type)
}

// 拖拽悬停
const handleDragOver = (event) => {
  event.preventDefault()
}

// 拖拽放置
const handleDrop = (event) => {
  event.preventDefault()
  const nodeType = event.dataTransfer.getData('nodeType')

  if (nodeType) {
    // 获取画布相对位置
    const canvasRect = canvasRef.value.getBoundingClientRect()
    const x = event.clientX - canvasRect.left
    const y = event.clientY - canvasRect.top

    // 检查是否已存在开始或结束节点
    if (nodeType === 'start' && workflowNodes.value.some(node => node.node_type === 'start')) {
      ElMessage.warning('工作流中已存在开始节点')
      return
    }

    if (nodeType === 'end' && workflowNodes.value.some(node => node.node_type === 'end')) {
      ElMessage.warning('工作流中已存在结束节点')
      return
    }

    // 添加节点
    addNode(nodeType, getDefaultNodeName(nodeType), x, y)
  }
}

// 添加节点
const addNode = (nodeType, name, x, y) => {
  // 生成唯一节点键名
  const nodeKey = `node_${Date.now()}_${Math.floor(Math.random() * 1000)}`

  // 创建新节点
  const newNode = {
    node_key: nodeKey,
    node_type: nodeType,
    name: name,
    description: '',
    position_x: x,
    position_y: y,
    config: {}
  }

  // 为审批节点和任务节点添加默认配置
  if (nodeType === 'approval' || nodeType === 'task') {
    newNode.config = {
      assignee_type: 'user',
      assignee_id: null,
      assignee_role_id: null,
      assignee_department_id: null,
      due_days: 3,
      priority: 'normal'
    }
  }

  // 为条件节点添加默认配置
  if (nodeType === 'condition') {
    newNode.config = {
      condition: ''
    }
  }

  // 添加到节点列表
  workflowNodes.value.push(newNode)

  // 选中新添加的节点
  selectNode(workflowNodes.value.length - 1)
}

// 选择节点
const selectNode = (index) => {
  selectedNodeIndex.value = index
  selectedTransitionIndex.value = -1
}

// 取消选择节点
const deselectNode = () => {
  selectedNodeIndex.value = -1
  selectedTransitionIndex.value = -1
}

// 选择转换
const selectTransition = (index) => {
  selectedTransitionIndex.value = index
  selectedNodeIndex.value = -1
}

// 移除节点
const removeNode = () => {
  if (selectedNodeIndex.value === -1) return

  const nodeKey = workflowNodes.value[selectedNodeIndex.value].node_key

  // 移除与该节点相关的所有转换
  workflowTransitions.value = workflowTransitions.value.filter(
    transition => transition.source_node_key !== nodeKey && transition.target_node_key !== nodeKey
  )

  // 移除节点
  workflowNodes.value.splice(selectedNodeIndex.value, 1)

  // 取消选中
  selectedNodeIndex.value = -1
}

// 移除转换
const removeTransition = () => {
  if (selectedTransitionIndex.value === -1) return

  workflowTransitions.value.splice(selectedTransitionIndex.value, 1)
  selectedTransitionIndex.value = -1
}

// 开始拖动节点
const startDrag = (event, index) => {
  event.preventDefault()

  dragState.isDragging = true
  dragState.nodeIndex = index
  dragState.startX = event.clientX
  dragState.startY = event.clientY
  dragState.offsetX = 0
  dragState.offsetY = 0
}

// 处理鼠标移动
const handleMouseMove = (event) => {
  // 处理节点拖动
  if (dragState.isDragging && dragState.nodeIndex !== -1) {
    const dx = event.clientX - dragState.startX
    const dy = event.clientY - dragState.startY

    dragState.offsetX = dx
    dragState.offsetY = dy

    const node = workflowNodes.value[dragState.nodeIndex]
    node.position_x = Math.max(0, node.position_x + dx)
    node.position_y = Math.max(0, node.position_y + dy)

    dragState.startX = event.clientX
    dragState.startY = event.clientY
  }

  // 处理临时连线
  if (tempConnection.active) {
    const canvasRect = canvasRef.value.getBoundingClientRect()
    tempConnection.targetX = event.clientX - canvasRect.left
    tempConnection.targetY = event.clientY - canvasRect.top
  }
}

// 处理鼠标释放
const handleMouseUp = (event) => {
  // 结束节点拖动
  if (dragState.isDragging) {
    dragState.isDragging = false
    dragState.nodeIndex = -1
  }

  // 处理连线完成
  if (tempConnection.active) {
    const canvasRect = canvasRef.value.getBoundingClientRect()
    const x = event.clientX - canvasRect.left
    const y = event.clientY - canvasRect.top

    // 查找目标节点
    const targetNodeIndex = findNodeAtPosition(x, y)

    if (targetNodeIndex !== -1 && targetNodeIndex !== workflowNodes.value.indexOf(tempConnection.sourceNode)) {
      const targetNode = workflowNodes.value[targetNodeIndex]

      // 检查是否已存在相同的连线
      const existingTransition = workflowTransitions.value.find(
        t => t.source_node_key === tempConnection.sourceNode.node_key &&
             t.target_node_key === targetNode.node_key
      )

      if (!existingTransition) {
        // 添加新连线
        workflowTransitions.value.push({
          source_node_key: tempConnection.sourceNode.node_key,
          target_node_key: targetNode.node_key,
          condition: ''
        })
      }
    }

    // 重置临时连线
    tempConnection.active = false
    tempConnection.sourceNode = null
  }
}

// 开始创建连线
const startConnection = (node) => {
  tempConnection.active = true
  tempConnection.sourceNode = node

  const nodeEl = document.querySelector(`.workflow-node[data-key="${node.node_key}"]`)

  // 添加空检查，防止DOM元素未找到时出错
  if (!nodeEl) {
    console.warn(`未找到节点元素: ${node.node_key}`)
    // 使用节点的位置信息作为备选
    const nodeWidth = 120 // 节点宽度
    const nodeHeight = 60 // 节点高度
    tempConnection.sourceX = node.position_x + nodeWidth
    tempConnection.sourceY = node.position_y + nodeHeight / 2
    tempConnection.targetX = tempConnection.sourceX
    tempConnection.targetY = tempConnection.sourceY
    return
  }

  const nodeRect = nodeEl.getBoundingClientRect()
  const canvasRect = canvasRef.value.getBoundingClientRect()

  tempConnection.sourceX = node.position_x + nodeRect.width
  tempConnection.sourceY = node.position_y + nodeRect.height / 2
  tempConnection.targetX = tempConnection.sourceX
  tempConnection.targetY = tempConnection.sourceY
}

// 查找指定位置的节点
const findNodeAtPosition = (x, y) => {
  for (let i = 0; i < workflowNodes.value.length; i++) {
    const node = workflowNodes.value[i]
    const nodeWidth = 120
    const nodeHeight = 60

    if (
      x >= node.position_x &&
      x <= node.position_x + nodeWidth &&
      y >= node.position_y &&
      y <= node.position_y + nodeHeight
    ) {
      return i
    }
  }

  return -1
}

// 获取连线路径数据
const getPathData = (transition) => {
  const sourceNode = workflowNodes.value.find(node => node.node_key === transition.source_node_key)
  const targetNode = workflowNodes.value.find(node => node.node_key === transition.target_node_key)

  if (!sourceNode || !targetNode) return ''

  const sourceX = sourceNode.position_x + 120 // 节点宽度
  const sourceY = sourceNode.position_y + 30 // 节点高度的一半
  const targetX = targetNode.position_x
  const targetY = targetNode.position_y + 30 // 节点高度的一半

  // 计算控制点
  const controlX = (sourceX + targetX) / 2

  return `M ${sourceX} ${sourceY} C ${controlX} ${sourceY}, ${controlX} ${targetY}, ${targetX} ${targetY}`
}

// 获取临时连线路径数据
const getTempPathData = () => {
  if (!tempConnection.active) return ''

  // 计算控制点
  const controlX = (tempConnection.sourceX + tempConnection.targetX) / 2

  return `M ${tempConnection.sourceX} ${tempConnection.sourceY} C ${controlX} ${tempConnection.sourceY}, ${controlX} ${tempConnection.targetY}, ${tempConnection.targetX} ${tempConnection.targetY}`
}

// 清空设计
const handleClearDesign = () => {
  ElMessageBox.confirm(
    '确定要清空当前设计吗？此操作不可恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    workflowNodes.value = []
    workflowTransitions.value = []
    selectedNodeIndex.value = -1
    selectedTransitionIndex.value = -1

    // 添加开始节点
    addNode('start', 'Start', 100, 100)
  }).catch(() => {
    // 取消清空
  })
}

// 保存工作流
const saveWorkflow = async () => {
  // 验证工作流信息
  if (!workflowInfo.title) {
    ElMessage.error('请输入工作流名称')
    return
  }

  if (!workflowInfo.form_template_id) {
    ElMessage.error('请选择关联表单')
    return
  }

  // 验证节点
  if (workflowNodes.value.length < 2) {
    ElMessage.error('工作流至少需要包含开始和结束节点')
    return
  }

  // 验证是否包含开始和结束节点
  const hasStart = workflowNodes.value.some(node => node.node_type === 'start')
  const hasEnd = workflowNodes.value.some(node => node.node_type === 'end')

  if (!hasStart) {
    ElMessage.error('工作流必须包含开始节点')
    return
  }

  if (!hasEnd) {
    ElMessage.error('工作流必须包含结束节点')
    return
  }

  // 验证连线
  if (workflowTransitions.value.length === 0) {
    ElMessage.error('请至少添加一条连线')
    return
  }

  saving.value = true
  try {
    // 构建工作流数据
    const workflowData = {
      title: workflowInfo.title,
      description: workflowInfo.description,
      form_template_id: workflowInfo.form_template_id,
      status: workflowInfo.status,
      schema: {
        nodes: workflowNodes.value,
        transitions: workflowTransitions.value
      },
      nodes: workflowNodes.value,
      transitions: workflowTransitions.value.map(transition => ({
        source_node_key: transition.source_node_key,
        target_node_key: transition.target_node_key,
        condition: transition.condition
      }))
    }

    let result
    if (isEdit.value && currentWorkflowId.value) {
      // 更新工作流
      result = await workflowApi.updateWorkflow(currentWorkflowId.value, workflowData)
    } else {
      // 创建工作流
      result = await workflowApi.createWorkflow(workflowData)
    }

    if (result.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      workflowDialogVisible.value = false
      fetchWorkflows()
    } else {
      ElMessage.error(result.message || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error(isEdit.value ? '更新工作流失败:' : '创建工作流失败:', error)
    ElMessage.error(error.response?.data?.message || (isEdit.value ? '更新失败' : '创建失败'))
  } finally {
    saving.value = false
  }
}

// 重置工作流设计器
const resetWorkflowDesigner = () => {
  // 重置工作流信息
  workflowInfo.title = ''
  workflowInfo.description = ''
  workflowInfo.form_template_id = null
  workflowInfo.status = 'draft'

  // 重置节点和转换
  workflowNodes.value = []
  workflowTransitions.value = []
  selectedNodeIndex.value = -1
  selectedTransitionIndex.value = -1
}

// 获取默认节点名称
const getDefaultNodeName = (type) => {
  switch (type) {
    case 'start':
      return '开始'
    case 'end':
      return '结束'
    case 'approval':
      return '审批'
    case 'task':
      return '任务'
    case 'condition':
      return '条件'
    default:
      return '未命名节点'
  }
}

// 获取节点图标
const getNodeIcon = (type) => {
  switch (type) {
    case 'start':
      return 'CircleCheck'
    case 'end':
      return 'CircleClose'
    case 'approval':
      return 'Document'
    case 'task':
      return 'Setting'
    case 'condition':
      return 'Connection'
    default:
      return 'Document'
  }
}

// 获取节点类型文本
const getNodeTypeText = (type) => {
  switch (type) {
    case 'start':
      return '开始节点'
    case 'end':
      return '结束节点'
    case 'approval':
      return '审批节点'
    case 'task':
      return '任务节点'
    case 'condition':
      return '条件节点'
    default:
      return type
  }
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'info'
    case 'disabled':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    case 'disabled':
      return '已禁用'
    default:
      return status
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString()
}

// 检查是否可以删除
const canDelete = (workflow) => {
  const user = JSON.parse(localStorage.getItem('user') || '{}')
  return user.roles && user.roles.includes('admin') || (workflow.creator && workflow.creator.id === user.id)
}
</script>

<style scoped>
.workflow-design-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.workflow-designer {
  min-height: 600px;
}

.components-panel, .design-panel, .properties-panel {
  height: 100%;
}

.components-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: move;
  transition: all 0.3s;
}

.component-item:hover {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.workflow-canvas {
  position: relative;
  min-height: 600px;
  padding: 20px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.workflow-node {
  position: absolute;
  width: 120px;
  height: 60px;
  border: 2px solid #909399;
  border-radius: 4px;
  background-color: #f5f7fa;
  cursor: move;
  user-select: none;
  z-index: 10;
}

.workflow-node.active {
  border-color: #409eff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.6);
}

.workflow-node.start-node {
  border-color: #67c23a;
  background-color: #f0f9eb;
}

.workflow-node.end-node {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.workflow-node.approval-node {
  border-color: #e6a23c;
  background-color: #fdf6ec;
}

.workflow-node.condition-node {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.workflow-node.task-node {
  border-color: #909399;
  background-color: #f4f4f5;
}

.node-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.node-icon {
  margin-bottom: 5px;
}

.node-title {
  font-size: 14px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  padding: 0 5px;
}

.node-ports {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.port {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #409eff;
  z-index: 20;
}

.port-out {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  cursor: crosshair;
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.connections-layer path {
  pointer-events: auto;
  cursor: pointer;
}

.connections-layer path.active {
  stroke: #409eff;
  stroke-width: 3;
}

.temp-connection-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 6;
}
</style>
