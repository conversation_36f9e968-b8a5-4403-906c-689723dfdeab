# 工作流系统部署指南

## 1. 系统要求

### 1.1 硬件要求

- **CPU**: 2核心及以上
- **内存**: 4GB及以上
- **硬盘**: 20GB可用空间及以上
- **网络**: 稳定的网络连接

### 1.2 软件要求

- **操作系统**: Ubuntu 20.04 LTS或更高版本，CentOS 8或更高版本，Windows Server 2019或更高版本
- **Node.js**: v16.0.0或更高版本
- **PostgreSQL**: v12.0或更高版本
- **Nginx**: v1.18.0或更高版本（用于生产环境）
- **PM2**: v5.1.0或更高版本（用于生产环境）

## 2. 开发环境部署

### 2.1 准备工作

1. 安装Node.js和npm
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install -y nodejs npm
   
   # CentOS/RHEL
   sudo yum install -y nodejs npm
   
   # 更新npm到最新版本
   sudo npm install -g npm@latest
   
   # 安装n模块管理Node.js版本
   sudo npm install -g n
   
   # 安装最新的稳定版Node.js
   sudo n stable
   ```

2. 安装PostgreSQL
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install -y postgresql postgresql-contrib
   
   # CentOS/RHEL
   sudo yum install -y postgresql-server postgresql-contrib
   sudo postgresql-setup --initdb
   sudo systemctl start postgresql
   sudo systemctl enable postgresql
   ```

3. 配置PostgreSQL
   ```bash
   # 切换到postgres用户
   sudo -i -u postgres
   
   # 创建数据库用户
   createuser --interactive --pwprompt workflow_user
   # 输入密码并确认
   # 是否为超级用户？选择n
   # 是否允许创建数据库？选择y
   # 是否允许创建新角色？选择n
   
   # 创建数据库
   createdb --owner=workflow_user workflow_db
   
   # 退出postgres用户
   exit
   ```

### 2.2 获取代码

1. 克隆代码仓库
   ```bash
   git clone https://github.com/your-organization/workflow-system.git
   cd workflow-system
   ```

2. 或者解压下载的压缩包
   ```bash
   unzip workflow-system.zip -d /path/to/destination
   cd /path/to/destination/workflow-system
   ```

### 2.3 配置后端

1. 安装后端依赖
   ```bash
   cd backend
   npm install
   ```

2. 创建环境配置文件
   ```bash
   cp .env.example .env
   ```

3. 编辑环境配置文件
   ```bash
   # 使用文本编辑器编辑.env文件
   nano .env
   ```
   
   配置内容示例：
   ```
   # 服务器配置
   PORT=3001
   NODE_ENV=development
   
   # 数据库配置
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=workflow_db
   DB_USER=workflow_user
   DB_PASSWORD=your_password
   
   # JWT配置
   JWT_SECRET=your_jwt_secret_key
   JWT_EXPIRES_IN=24h
   
   # 跨域配置
   CORS_ORIGIN=http://localhost:5173
   ```

4. 运行数据库迁移
   ```bash
   npx sequelize-cli db:migrate
   ```

5. 运行数据库种子（可选，用于填充初始数据）
   ```bash
   npx sequelize-cli db:seed:all
   ```

### 2.4 配置前端

1. 安装前端依赖
   ```bash
   cd ../frontend
   npm install
   ```

2. 创建环境配置文件
   ```bash
   cp .env.example .env
   ```

3. 编辑环境配置文件
   ```bash
   # 使用文本编辑器编辑.env文件
   nano .env
   ```
   
   配置内容示例：
   ```
   VITE_API_BASE_URL=http://localhost:3001/api
   ```

### 2.5 启动开发服务器

1. 启动后端服务器
   ```bash
   cd ../backend
   npm run dev
   ```

2. 在另一个终端启动前端服务器
   ```bash
   cd ../frontend
   npm run dev
   ```

3. 访问应用
   - 前端: http://localhost:5173
   - 后端API: http://localhost:3001/api

## 3. 生产环境部署

### 3.1 准备工作

1. 安装Node.js和npm（同开发环境）

2. 安装PostgreSQL（同开发环境）

3. 安装Nginx
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install -y nginx
   
   # CentOS/RHEL
   sudo yum install -y nginx
   sudo systemctl start nginx
   sudo systemctl enable nginx
   ```

4. 安装PM2
   ```bash
   sudo npm install -g pm2
   ```

### 3.2 部署后端

1. 获取代码（同开发环境）

2. 安装后端依赖
   ```bash
   cd backend
   npm install --production
   ```

3. 创建并配置环境文件
   ```bash
   cp .env.example .env
   nano .env
   ```
   
   生产环境配置示例：
   ```
   # 服务器配置
   PORT=3001
   NODE_ENV=production
   
   # 数据库配置
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=workflow_db
   DB_USER=workflow_user
   DB_PASSWORD=your_secure_password
   
   # JWT配置
   JWT_SECRET=your_very_secure_jwt_secret_key
   JWT_EXPIRES_IN=8h
   
   # 跨域配置
   CORS_ORIGIN=https://your-domain.com
   ```

4. 运行数据库迁移
   ```bash
   npx sequelize-cli db:migrate
   ```

5. 使用PM2启动后端服务
   ```bash
   pm2 start src/app.js --name "workflow-backend"
   pm2 save
   pm2 startup
   ```

### 3.3 部署前端

1. 安装前端依赖
   ```bash
   cd ../frontend
   npm install
   ```

2. 创建并配置环境文件
   ```bash
   cp .env.example .env.production
   nano .env.production
   ```
   
   生产环境配置示例：
   ```
   VITE_API_BASE_URL=https://your-domain.com/api
   ```

3. 构建前端项目
   ```bash
   npm run build
   ```
   
   构建完成后，生成的静态文件将位于`dist`目录中。

4. 配置Nginx
   ```bash
   sudo nano /etc/nginx/sites-available/workflow-system
   ```
   
   配置内容示例：
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       # 重定向HTTP到HTTPS
       return 301 https://$host$request_uri;
   }
   
   server {
       listen 443 ssl;
       server_name your-domain.com;
       
       # SSL配置
       ssl_certificate /path/to/your/certificate.crt;
       ssl_certificate_key /path/to/your/private.key;
       
       # 前端静态文件
       location / {
           root /path/to/workflow-system/frontend/dist;
           index index.html;
           try_files $uri $uri/ /index.html;
       }
       
       # 后端API代理
       location /api {
           proxy_pass http://localhost:3001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

5. 启用Nginx配置
   ```bash
   sudo ln -s /etc/nginx/sites-available/workflow-system /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

### 3.4 配置HTTPS（推荐）

1. 安装Certbot
   ```bash
   # Ubuntu/Debian
   sudo apt install -y certbot python3-certbot-nginx
   
   # CentOS/RHEL
   sudo yum install -y certbot python3-certbot-nginx
   ```

2. 获取SSL证书
   ```bash
   sudo certbot --nginx -d your-domain.com
   ```

3. 配置自动续期
   ```bash
   sudo systemctl status certbot.timer
   ```

### 3.5 系统监控与维护

1. 使用PM2监控后端服务
   ```bash
   # 查看服务状态
   pm2 status
   
   # 查看日志
   pm2 logs workflow-backend
   
   # 重启服务
   pm2 restart workflow-backend
   ```

2. 配置日志轮转
   ```bash
   sudo nano /etc/logrotate.d/workflow-system
   ```
   
   配置内容示例：
   ```
   /path/to/workflow-system/backend/logs/*.log {
       daily
       missingok
       rotate 14
       compress
       delaycompress
       notifempty
       create 0640 www-data www-data
   }
   ```

3. 设置数据库备份
   ```bash
   # 创建备份脚本
   nano /path/to/backup.sh
   ```
   
   脚本内容示例：
   ```bash
   #!/bin/bash
   
   BACKUP_DIR="/path/to/backups"
   TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
   BACKUP_FILE="$BACKUP_DIR/workflow_db_$TIMESTAMP.sql"
   
   # 创建备份目录
   mkdir -p $BACKUP_DIR
   
   # 备份数据库
   pg_dump -U workflow_user -h localhost workflow_db > $BACKUP_FILE
   
   # 压缩备份文件
   gzip $BACKUP_FILE
   
   # 删除30天前的备份
   find $BACKUP_DIR -name "workflow_db_*.sql.gz" -mtime +30 -delete
   ```
   
   设置执行权限并添加到crontab：
   ```bash
   chmod +x /path/to/backup.sh
   
   # 编辑crontab
   crontab -e
   
   # 添加以下行（每天凌晨2点执行备份）
   0 2 * * * /path/to/backup.sh
   ```

## 4. Docker部署（可选）

### 4.1 准备工作

1. 安装Docker和Docker Compose
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install -y docker.io docker-compose
   
   # CentOS/RHEL
   sudo yum install -y docker docker-compose
   sudo systemctl start docker
   sudo systemctl enable docker
   ```

2. 将当前用户添加到docker组（可选，避免每次使用sudo）
   ```bash
   sudo usermod -aG docker $USER
   # 重新登录以使更改生效
   ```

### 4.2 创建Docker配置文件

1. 创建docker-compose.yml文件
   ```bash
   nano docker-compose.yml
   ```
   
   配置内容示例：
   ```yaml
   version: '3'
   
   services:
     # PostgreSQL数据库
     db:
       image: postgres:14
       container_name: workflow-db
       restart: always
       environment:
         POSTGRES_USER: workflow_user
         POSTGRES_PASSWORD: your_secure_password
         POSTGRES_DB: workflow_db
       volumes:
         - postgres_data:/var/lib/postgresql/data
       ports:
         - "5432:5432"
   
     # 后端API服务
     backend:
       build:
         context: ./backend
         dockerfile: Dockerfile
       container_name: workflow-backend
       restart: always
       depends_on:
         - db
       environment:
         NODE_ENV: production
         PORT: 3001
         DB_HOST: db
         DB_PORT: 5432
         DB_NAME: workflow_db
         DB_USER: workflow_user
         DB_PASSWORD: your_secure_password
         JWT_SECRET: your_very_secure_jwt_secret_key
         JWT_EXPIRES_IN: 8h
         CORS_ORIGIN: https://your-domain.com
       ports:
         - "3001:3001"
   
     # 前端Web服务
     frontend:
       build:
         context: ./frontend
         dockerfile: Dockerfile
       container_name: workflow-frontend
       restart: always
       depends_on:
         - backend
       ports:
         - "80:80"
         - "443:443"
       volumes:
         - ./nginx/conf:/etc/nginx/conf.d
         - ./nginx/ssl:/etc/nginx/ssl
   
   volumes:
     postgres_data:
   ```

2. 为后端创建Dockerfile
   ```bash
   nano backend/Dockerfile
   ```
   
   配置内容示例：
   ```dockerfile
   FROM node:16-alpine
   
   WORKDIR /app
   
   COPY package*.json ./
   
   RUN npm install --production
   
   COPY . .
   
   RUN npx sequelize-cli db:migrate
   
   EXPOSE 3001
   
   CMD ["node", "src/app.js"]
   ```

3. 为前端创建Dockerfile
   ```bash
   nano frontend/Dockerfile
   ```
   
   配置内容示例：
   ```dockerfile
   # 构建阶段
   FROM node:16-alpine as build-stage
   
   WORKDIR /app
   
   COPY package*.json ./
   
   RUN npm install
   
   COPY . .
   
   RUN npm run build
   
   # 生产阶段
   FROM nginx:stable-alpine as production-stage
   
   COPY --from=build-stage /app/dist /usr/share/nginx/html
   
   EXPOSE 80
   
   CMD ["nginx", "-g", "daemon off;"]
   ```

4. 创建Nginx配置文件
   ```bash
   mkdir -p nginx/conf
   nano nginx/conf/default.conf
   ```
   
   配置内容示例：
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       # 重定向HTTP到HTTPS
       return 301 https://$host$request_uri;
   }
   
   server {
       listen 443 ssl;
       server_name your-domain.com;
       
       # SSL配置
       ssl_certificate /etc/nginx/ssl/certificate.crt;
       ssl_certificate_key /etc/nginx/ssl/private.key;
       
       # 前端静态文件
       location / {
           root /usr/share/nginx/html;
           index index.html;
           try_files $uri $uri/ /index.html;
       }
       
       # 后端API代理
       location /api {
           proxy_pass http://backend:3001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

### 4.3 启动Docker容器

1. 构建并启动容器
   ```bash
   docker-compose up -d
   ```

2. 查看容器状态
   ```bash
   docker-compose ps
   ```

3. 查看容器日志
   ```bash
   docker-compose logs -f
   ```

### 4.4 更新部署

1. 拉取最新代码
   ```bash
   git pull
   ```

2. 重新构建并启动容器
   ```bash
   docker-compose down
   docker-compose build
   docker-compose up -d
   ```

## 5. 系统升级

### 5.1 升级准备

1. 备份数据库
   ```bash
   pg_dump -U workflow_user -h localhost workflow_db > workflow_db_backup.sql
   ```

2. 备份配置文件
   ```bash
   cp backend/.env backend/.env.backup
   cp frontend/.env frontend/.env.backup
   ```

### 5.2 升级步骤

1. 拉取最新代码
   ```bash
   git pull
   ```

2. 安装依赖
   ```bash
   cd backend
   npm install
   
   cd ../frontend
   npm install
   ```

3. 运行数据库迁移
   ```bash
   cd ../backend
   npx sequelize-cli db:migrate
   ```

4. 构建前端
   ```bash
   cd ../frontend
   npm run build
   ```

5. 重启服务
   ```bash
   # 如果使用PM2
   cd ../backend
   pm2 restart workflow-backend
   
   # 如果使用Docker
   docker-compose down
   docker-compose up -d
   ```

### 5.3 回滚步骤

如果升级过程中出现问题，可以按照以下步骤回滚：

1. 恢复数据库
   ```bash
   psql -U workflow_user -h localhost workflow_db < workflow_db_backup.sql
   ```

2. 恢复配置文件
   ```bash
   cp backend/.env.backup backend/.env
   cp frontend/.env.backup frontend/.env
   ```

3. 切换回旧版本代码
   ```bash
   git checkout <previous-version-tag>
   ```

4. 重新安装依赖和构建
   ```bash
   cd backend
   npm install
   
   cd ../frontend
   npm install
   npm run build
   ```

5. 重启服务
   ```bash
   # 如果使用PM2
   cd ../backend
   pm2 restart workflow-backend
   
   # 如果使用Docker
   docker-compose down
   docker-compose up -d
   ```

## 6. 故障排除

### 6.1 常见问题

#### 6.1.1 数据库连接失败

**症状**: 后端服务启动失败，日志中显示数据库连接错误。

**解决方案**:
1. 检查PostgreSQL服务是否运行
   ```bash
   sudo systemctl status postgresql
   ```

2. 检查数据库用户和密码是否正确
   ```bash
   psql -U workflow_user -h localhost workflow_db
   # 输入密码
   ```

3. 检查数据库配置
   ```bash
   cat backend/.env
   # 确认DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD是否正确
   ```

#### 6.1.2 前端无法连接后端API

**症状**: 前端页面加载，但无法获取数据或执行操作。

**解决方案**:
1. 检查后端服务是否运行
   ```bash
   # 如果使用PM2
   pm2 status
   
   # 如果使用Docker
   docker-compose ps
   ```

2. 检查API基础URL配置
   ```bash
   cat frontend/.env
   # 确认VITE_API_BASE_URL是否正确
   ```

3. 检查CORS配置
   ```bash
   cat backend/.env
   # 确认CORS_ORIGIN是否包含前端域名
   ```

4. 检查网络连接
   ```bash
   curl http://localhost:3001/api/health
   # 应返回正常状态信息
   ```

#### 6.1.3 Nginx配置错误

**症状**: 访问网站显示502 Bad Gateway或其他错误。

**解决方案**:
1. 检查Nginx配置
   ```bash
   sudo nginx -t
   ```

2. 检查Nginx错误日志
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

3. 确认后端服务正在运行并监听正确的端口
   ```bash
   netstat -tulpn | grep 3001
   ```

4. 重启Nginx
   ```bash
   sudo systemctl restart nginx
   ```

### 6.2 日志查看

#### 6.2.1 后端日志

```bash
# 如果使用PM2
pm2 logs workflow-backend

# 如果使用Docker
docker-compose logs -f backend

# 直接查看日志文件
tail -f backend/logs/app.log
```

#### 6.2.2 前端日志

```bash
# 在浏览器中打开开发者工具，查看Console标签

# Nginx访问日志
sudo tail -f /var/log/nginx/access.log

# Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

#### 6.2.3 数据库日志

```bash
# PostgreSQL日志
sudo tail -f /var/log/postgresql/postgresql-12-main.log
```

### 6.3 性能优化

#### 6.3.1 数据库优化

1. 添加索引
   ```sql
   -- 为常用查询字段添加索引
   CREATE INDEX idx_workflow_instances_status ON workflow_instances(status);
   CREATE INDEX idx_workflow_tasks_assignee_id ON workflow_tasks(assignee_id);
   ```

2. 优化查询
   ```sql
   -- 使用EXPLAIN ANALYZE分析查询性能
   EXPLAIN ANALYZE SELECT * FROM workflow_instances WHERE status = 'running';
   ```

3. 配置PostgreSQL
   ```bash
   sudo nano /etc/postgresql/12/main/postgresql.conf
   ```
   
   常用优化参数：
   ```
   # 内存配置
   shared_buffers = 1GB                  # 系统内存的25%
   work_mem = 32MB                       # 根据并发连接数调整
   maintenance_work_mem = 256MB          # 维护操作使用的内存
   
   # 写入性能
   wal_buffers = 16MB                    # WAL缓冲区大小
   checkpoint_timeout = 15min            # 检查点超时
   max_wal_size = 1GB                    # 最大WAL大小
   
   # 查询优化
   effective_cache_size = 3GB            # 系统缓存估计值，通常为系统内存的75%
   random_page_cost = 1.1                # 使用SSD时设置较低
   ```

#### 6.3.2 Node.js优化

1. 启用压缩
   ```javascript
   // 在app.js中添加
   const compression = require('compression');
   app.use(compression());
   ```

2. 使用PM2集群模式
   ```bash
   pm2 start src/app.js --name "workflow-backend" -i max
   ```

#### 6.3.3 Nginx优化

1. 启用Gzip压缩
   ```nginx
   # 在nginx配置中添加
   gzip on;
   gzip_comp_level 5;
   gzip_min_length 256;
   gzip_proxied any;
   gzip_vary on;
   gzip_types
     application/javascript
     application/json
     application/x-javascript
     text/css
     text/javascript
     text/plain
     text/xml;
   ```

2. 配置缓存
   ```nginx
   # 在nginx配置中添加
   location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
       expires 30d;
       add_header Cache-Control "public, no-transform";
   }
   ```

## 7. 安全配置

### 7.1 防火墙配置

```bash
# Ubuntu/Debian
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 7.2 SSL/TLS配置

1. 使用强密码套件
   ```nginx
   # 在nginx配置中添加
   ssl_protocols TLSv1.2 TLSv1.3;
   ssl_prefer_server_ciphers on;
   ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
   ssl_session_timeout 1d;
   ssl_session_cache shared:SSL:10m;
   ssl_session_tickets off;
   ```

2. 配置HSTS
   ```nginx
   # 在nginx配置中添加
   add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
   ```

### 7.3 安全头部配置

```nginx
# 在nginx配置中添加
add_header X-Content-Type-Options nosniff;
add_header X-Frame-Options SAMEORIGIN;
add_header X-XSS-Protection "1; mode=block";
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self'";
```

### 7.4 数据库安全

1. 限制数据库访问
   ```bash
   sudo nano /etc/postgresql/12/main/pg_hba.conf
   ```
   
   配置示例：
   ```
   # TYPE  DATABASE        USER            ADDRESS                 METHOD
   local   all             postgres                                peer
   local   workflow_db     workflow_user                           md5
   host    workflow_db     workflow_user   127.0.0.1/32            md5
   host    workflow_db     workflow_user   ::1/128                 md5
   ```

2. 配置PostgreSQL只监听本地连接
   ```bash
   sudo nano /etc/postgresql/12/main/postgresql.conf
   ```
   
   设置：
   ```
   listen_addresses = 'localhost'
   ```

## 8. 附录

### 8.1 常用命令参考

```bash
# 启动后端开发服务器
cd backend
npm run dev

# 启动前端开发服务器
cd frontend
npm run dev

# 构建前端生产版本
cd frontend
npm run build

# 使用PM2启动后端
cd backend
pm2 start src/app.js --name "workflow-backend"

# 查看PM2进程状态
pm2 status

# 重启PM2进程
pm2 restart workflow-backend

# 查看PM2日志
pm2 logs workflow-backend

# 启动Docker容器
docker-compose up -d

# 停止Docker容器
docker-compose down

# 查看Docker容器状态
docker-compose ps

# 查看Docker容器日志
docker-compose logs -f

# 数据库备份
pg_dump -U workflow_user -h localhost workflow_db > backup.sql

# 数据库恢复
psql -U workflow_user -h localhost workflow_db < backup.sql

# 检查Nginx配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 8.2 环境变量参考

#### 后端环境变量

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| PORT | 服务器监听端口 | 3001 |
| NODE_ENV | 运行环境 | production |
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 5432 |
| DB_NAME | 数据库名称 | workflow_db |
| DB_USER | 数据库用户 | workflow_user |
| DB_PASSWORD | 数据库密码 | your_secure_password |
| JWT_SECRET | JWT密钥 | your_very_secure_jwt_secret_key |
| JWT_EXPIRES_IN | JWT过期时间 | 8h |
| CORS_ORIGIN | 允许的跨域来源 | https://your-domain.com |

#### 前端环境变量

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| VITE_API_BASE_URL | 后端API基础URL | https://your-domain.com/api |

### 8.3 目录结构参考

```
workflow-system/
├── backend/                # 后端项目
│   ├── src/                # 源代码
│   │   ├── config/         # 配置文件
│   │   ├── controllers/    # 控制器
│   │   ├── middlewares/    # 中间件
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由
│   │   ├── services/       # 服务
│   │   ├── utils/          # 工具函数
│   │   └── app.js          # 应用入口
│   ├── migrations/         # 数据库迁移
│   ├── seeders/            # 数据库种子
│   ├── .env                # 环境变量
│   └── package.json        # 依赖配置
├── frontend/               # 前端项目
│   ├── public/             # 静态资源
│   ├── src/                # 源代码
│   │   ├── api/            # API接口
│   │   ├── assets/         # 资源文件
│   │   ├── components/     # 公共组件
│   │   ├── router/         # 路由配置
│   │   ├── store/          # 状态管理
│   │   ├── utils/          # 工具函数
│   │   ├── views/          # 页面组件
│   │   ├── App.vue         # 根组件
│   │   └── main.js         # 入口文件
│   ├── .env                # 环境变量
│   └── package.json        # 依赖配置
├── nginx/                  # Nginx配置（Docker部署）
│   ├── conf/               # 配置文件
│   └── ssl/                # SSL证书
├── docker-compose.yml      # Docker Compose配置
└── README.md               # 项目说明
```

### 8.4 故障排除清单

| 问题 | 可能原因 | 解决方案 |
|------|---------|----------|
| 后端服务无法启动 | 端口被占用 | 更改PORT环境变量或关闭占用端口的进程 |
| 后端服务无法启动 | 数据库连接失败 | 检查数据库配置和连接信息 |
| 前端构建失败 | 依赖问题 | 删除node_modules目录并重新安装依赖 |
| 前端无法连接后端 | API基础URL配置错误 | 检查VITE_API_BASE_URL环境变量 |
| 前端无法连接后端 | CORS配置错误 | 检查后端CORS_ORIGIN环境变量 |
| 登录失败 | JWT密钥配置错误 | 检查JWT_SECRET环境变量 |
| 数据库迁移失败 | 数据库权限不足 | 确保数据库用户有足够权限 |
| Nginx返回502错误 | 后端服务未运行 | 检查后端服务状态并启动 |
| Nginx返回502错误 | 代理配置错误 | 检查Nginx配置中的proxy_pass设置 |
