'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 修改 workflow_task_histories 表的 task_id 字段，允许为空
    await queryInterface.changeColumn('workflow_task_histories', 'task_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'workflow_tasks',
        key: 'id'
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    // 恢复 workflow_task_histories 表的 task_id 字段，不允许为空
    await queryInterface.changeColumn('workflow_task_histories', 'task_id', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_tasks',
        key: 'id'
      }
    });
  }
};
