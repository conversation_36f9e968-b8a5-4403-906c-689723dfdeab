/**
 * Direct Express test
 * Run with: node direct-express-test.js
 */

const express = require('express');
const app = express();
const PORT = 3003;

// 中间件
app.use(express.json());

// 直接打印请求和响应
app.use((req, res, next) => {
  // 直接使用console.log打印请求信息
  console.log('\n==========================================');
  console.log('请求URL:', req.method, req.originalUrl || req.url);
  console.log('请求体:', req.body);
  
  // 保存原始的json方法
  const originalJson = res.json;
  
  // 重写json方法
  res.json = function(data) {
    // 直接使用console.log打印响应信息
    console.log('响应状态码:', res.statusCode);
    console.log('响应数据:', data);
    console.log('==========================================');
    
    // 调用原始方法
    return originalJson.call(this, data);
  };
  
  next();
});

// 测试路由
app.get('/test', (req, res) => {
  res.json({ message: 'Test route works!' });
});

// 测试登录路由
app.post('/login', (req, res) => {
  const { username, password } = req.body;
  
  if (username === 'admin' && password === 'admin123') {
    res.json({
      success: true,
      message: '登录成功',
      token: 'test-token',
      user: {
        id: 1,
        username: 'admin',
        roles: ['admin']
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: '用户名或密码不正确'
    });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`测试服务器运行在 http://localhost:${PORT}`);
});
