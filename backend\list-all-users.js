/**
 * List all users script
 */
const { sequelize } = require('./src/models');

async function listAllUsers() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Get all users
    const users = await sequelize.query(
      'SELECT id, username, email, full_name, status FROM users',
      {
        type: sequelize.QueryTypes.SELECT
      }
    );

    console.log(`Found ${users.length} users:`);
    users.forEach(user => {
      console.log(`- ID: ${user.id}, Username: ${user.username}, Email: ${user.email}, Status: ${user.status}`);
    });

    // Get all roles
    const roles = await sequelize.query(
      'SELECT id, name, display_name FROM roles',
      {
        type: sequelize.QueryTypes.SELECT
      }
    );

    console.log(`\nFound ${roles.length} roles:`);
    roles.forEach(role => {
      console.log(`- ID: ${role.id}, Name: ${role.name}, Display Name: ${role.display_name}`);
    });

    // Get user roles
    const userRoles = await sequelize.query(
      'SELECT ur.user_id, u.username, ur.role_id, r.name as role_name FROM user_roles ur JOIN users u ON ur.user_id = u.id JOIN roles r ON ur.role_id = r.id',
      {
        type: sequelize.QueryTypes.SELECT
      }
    );

    console.log(`\nFound ${userRoles.length} user-role assignments:`);
    userRoles.forEach(ur => {
      console.log(`- User ID: ${ur.user_id}, Username: ${ur.username}, Role ID: ${ur.role_id}, Role Name: ${ur.role_name}`);
    });
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

listAllUsers();
