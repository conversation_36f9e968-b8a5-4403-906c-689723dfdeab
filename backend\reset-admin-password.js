/**
 * Reset admin password script
 * Run with: node reset-admin-password.js
 */

const bcrypt = require('bcryptjs');
const { sequelize, User } = require('./src/models');

async function resetAdminPassword() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Find admin user
    const adminUser = await User.findOne({ where: { username: 'admin' } });
    
    if (!adminUser) {
      console.log('Admin user not found. Please run create-admin-user.js first.');
      return;
    }
    
    console.log('Resetting admin password...');
    
    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);
    
    // Update the password directly in the database to bypass any hooks
    await sequelize.query(`
      UPDATE users 
      SET password = '${hashedPassword}' 
      WHERE username = 'admin'
    `);
    
    console.log('Admin password reset successfully.');
    console.log('Username: admin');
    console.log('Password: admin123');
  } catch (error) {
    console.error('Error resetting admin password:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

resetAdminPassword();
