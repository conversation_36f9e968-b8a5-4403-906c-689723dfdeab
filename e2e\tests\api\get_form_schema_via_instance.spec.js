import { test, expect } from '@playwright/test';

const API_BASE_URL = 'http://localhost:3001/api';
const USERNAME = 'admin';
const PASSWORD = 'admin123';

// Test data based on our findings for instance 81
const TEST_INSTANCE_ID = 81;
const EXPECTED_WORKFLOW_TEMPLATE_ID_FOR_INSTANCE = 8; 
const EXPECTED_FORM_TEMPLATE_ID_IN_WORKFLOW_TEMPLATE = 10;

let authToken;

test.beforeAll(async ({ request }) => {
  const loginResponse = await request.post(`${API_BASE_URL}/users/login`, {
    data: {
      username: USERNAME,
      password: PASSWORD,
    },
  });
  expect(loginResponse.ok(), `<PERSON><PERSON> failed with status ${loginResponse.status()}: ${await loginResponse.text()}`).toBeTruthy();
  const loginBody = await loginResponse.json();
  expect(loginBody.success, 'Login was not successful').toBe(true);
  expect(loginBody.token, 'Auth token not found in login response').toBeDefined();
  authToken = loginBody.token;
});

test.describe('Retrieve Form Template Schema via Workflow Instance', () => {
  test('should fetch instance, then its workflow template, and verify the linked form template schema', async ({ request }) => {
    // 1. Fetch the workflow instance
    const instanceResponse = await request.get(`${API_BASE_URL}/workflows/instances/${TEST_INSTANCE_ID}`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });
    expect(instanceResponse.ok(), `Fetching instance ${TEST_INSTANCE_ID} failed: ${await instanceResponse.text()}`).toBeTruthy();
    const instanceBody = await instanceResponse.json();
    expect(instanceBody.success, 'Instance API call .success was not true').toBe(true);
    expect(instanceBody.data, 'Instance data not found').toBeDefined();
    expect(instanceBody.data.id, 'Instance ID mismatch').toBe(TEST_INSTANCE_ID);
    expect(instanceBody.data.workflow_template_id, 'Instance workflow_template_id mismatch or not found').toBe(EXPECTED_WORKFLOW_TEMPLATE_ID_FOR_INSTANCE);

    const workflowTemplateId = instanceBody.data.workflow_template_id;

    // 2. Fetch the workflow template using the ID from the instance
    const templateResponse = await request.get(`${API_BASE_URL}/workflows/${workflowTemplateId}`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });
    expect(templateResponse.ok(), `Fetching workflow template ${workflowTemplateId} failed: ${await templateResponse.text()}`).toBeTruthy();
    const templateBody = await templateResponse.json();
    expect(templateBody.success, 'Workflow template API call .success was not true').toBe(true);
    expect(templateBody.data, 'Workflow template data not found').toBeDefined();
    expect(templateBody.data.id, 'Workflow template ID mismatch').toBe(workflowTemplateId);
    expect(templateBody.data.form_template_id, 'Workflow template form_template_id mismatch or not found').toBe(EXPECTED_FORM_TEMPLATE_ID_IN_WORKFLOW_TEMPLATE);

    // 3. Verify the Form Template and its Schema
    const formTemplate = templateBody.data.formTemplate;
    expect(formTemplate, 'formTemplate object not found in workflow template response').toBeDefined();
    expect(formTemplate.id, 'Form template ID mismatch').toBe(EXPECTED_FORM_TEMPLATE_ID_IN_WORKFLOW_TEMPLATE);

    const formSchema = formTemplate.schema;
    expect(formSchema, 'formTemplate.schema not found').toBeDefined();
    expect(typeof formSchema, 'formTemplate.schema is not an object').toBe('object');

    // Verify schema structure (fields and formProps)
    expect(formSchema.fields, 'formSchema.fields not found or not an array').toBeInstanceOf(Array);
    
    // Specific checks for Form Template ID 10, which is linked to instance 81 via workflow template 8
    if (formTemplate.id === EXPECTED_FORM_TEMPLATE_ID_IN_WORKFLOW_TEMPLATE && EXPECTED_FORM_TEMPLATE_ID_IN_WORKFLOW_TEMPLATE === 10) {
        expect(formSchema.fields.length, 'Form template 10 should have 3 fields').toBe(3);
        
        const field1 = formSchema.fields.find(f => f.field_key === 'field_1746972411639_246');
        expect(field1, 'Field with key field_1746972411639_246 not found in form template 10').toBeDefined();
        if (field1) {
            expect(field1.label, 'Label for field_1746972411639_246 mismatch').toBe('数字输入');
            expect(field1.field_type, 'Field type for field_1746972411639_246 mismatch').toBe('number');
        }

        const field2 = formSchema.fields.find(f => f.field_key === 'field_1746972421061_664');
        expect(field2, 'Field with key field_1746972421061_664 not found in form template 10').toBeDefined();
         if (field2) {
            expect(field2.label, 'Label for field_1746972421061_664 mismatch').toBe('下拉选择');
            expect(field2.field_type, 'Field type for field_1746972421061_664 mismatch').toBe('select');
        }

        const field3 = formSchema.fields.find(f => f.field_key === 'field_1746972422576_437');
        expect(field3, 'Field with key field_1746972422576_437 not found in form template 10').toBeDefined();
        if (field3) {
            expect(field3.label, 'Label for field_1746972422576_437 mismatch').toBe('单选框组');
            expect(field3.field_type, 'Field type for field_1746972422576_437 mismatch').toBe('radio');
        }
    } else {
        // Generic check if it's not form template 10
         expect(formSchema.fields.length, `Expected at least one field in formSchema.fields for template ${formTemplate.id}`).toBeGreaterThanOrEqual(0);
    }

    expect(formSchema.formProps, 'formSchema.formProps not found or not an object').toBeDefined();
    expect(typeof formSchema.formProps, 'formSchema.formProps is not an object').toBe('object');
    
    if (formTemplate.id === EXPECTED_FORM_TEMPLATE_ID_IN_WORKFLOW_TEMPLATE && EXPECTED_FORM_TEMPLATE_ID_IN_WORKFLOW_TEMPLATE === 10) {
        expect(formSchema.formProps.layout, 'formSchema.formProps.layout for template 10 mismatch').toBe('vertical');
        expect(formSchema.formProps.submitButtonText, 'formSchema.formProps.submitButtonText for template 10 mismatch').toBe('提交');
    }
  });
}); 