const { test, expect } = require('@playwright/test');
const { login, navigateToModule } = require('../utils/test-helpers');

test.describe('Performance Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await login(page, 'admin');
  });

  test('should load home page within acceptable time', async ({ page }) => {
    // Start performance measurement
    const startTime = Date.now();
    
    // Navigate to home page
    await page.goto('/');
    
    // Wait for main content to be visible
    await page.waitForSelector('h1');
    
    // Calculate load time
    const loadTime = Date.now() - startTime;
    
    // Log performance data
    console.log(`Home page load time: ${loadTime}ms`);
    
    // Assert load time is within acceptable range (adjust threshold as needed)
    expect(loadTime).toBeLessThan(3001); // 3 seconds
    
    // Verify page is fully loaded
    await expect(page.locator('h1')).toContainText('欢迎使用工作流系统');
  });

  test('should render workflow designer within acceptable time', async ({ page }) => {
    // Navigate to workflow design page
    await navigateToModule(page, '工作流设计');
    
    // Click on create workflow button
    const startTime = Date.now();
    await page.click('button:has-text("创建工作流")');
    
    // Wait for designer to load
    await page.waitForSelector('.workflow-canvas');
    
    // Calculate render time
    const renderTime = Date.now() - startTime;
    
    // Log performance data
    console.log(`Workflow designer render time: ${renderTime}ms`);
    
    // Assert render time is within acceptable range
    expect(renderTime).toBeLessThan(5000); // 5 seconds
    
    // Verify designer is interactive
    await page.locator('.node-types .node-type-item').first().click();
    await page.locator('.workflow-canvas').click();
    
    // Verify node was added
    await expect(page.locator('.workflow-canvas .workflow-node')).toBeVisible();
  });

  test('should handle form submission with acceptable response time', async ({ page }) => {
    // Navigate to department page
    await navigateToModule(page, '部门配置');
    
    // Click add department button
    await page.click('button:has-text("添加部门")');
    
    // Fill department form
    const deptName = `性能测试部门${Date.now()}`;
    const deptCode = `PERF${Date.now()}`;
    await page.fill('input[placeholder="请输入部门名称"]', deptName);
    await page.fill('input[placeholder="请输入部门代码"]', deptCode);
    await page.fill('textarea[placeholder="请输入部门描述"]', '这是一个性能测试部门');
    
    // Submit form and measure response time
    const startTime = Date.now();
    await page.click('.el-dialog__footer button:has-text("确定")');
    
    // Wait for success message
    try {
      await page.waitForSelector('.el-message--success', { timeout: 10000 });
      
      // Calculate response time
      const responseTime = Date.now() - startTime;
      
      // Log performance data
      console.log(`Form submission response time: ${responseTime}ms`);
      
      // Assert response time is within acceptable range
      expect(responseTime).toBeLessThan(3001); // 3 seconds
    } catch (error) {
      // If success message doesn't appear, check for error message
      const errorVisible = await page.locator('.el-message--error').isVisible();
      if (errorVisible) {
        console.log('Form submission failed with error');
      } else {
        throw error;
      }
    }
  });

  test('should load and render tables with large data sets efficiently', async ({ page }) => {
    // Navigate to workflow form page (which typically has tables)
    await navigateToModule(page, '工作流填写');
    
    // Measure time to render the table
    const startTime = Date.now();
    
    // Wait for table to be visible
    await page.waitForSelector('.el-table');
    
    // Calculate render time
    const renderTime = Date.now() - startTime;
    
    // Log performance data
    console.log(`Table render time: ${renderTime}ms`);
    
    // Assert render time is within acceptable range
    expect(renderTime).toBeLessThan(2000); // 2 seconds
    
    // Verify table is interactive
    await page.click('.el-tabs__item').filter({ hasText: '我发起的工作流' });
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('我发起的工作流');
  });

  test('should handle multiple rapid interactions without performance degradation', async ({ page }) => {
    // Navigate to form design page
    await navigateToModule(page, '表单设计器');
    
    // Click on create form button
    await page.click('button:has-text("创建表单")');
    
    // Wait for designer to load
    await page.waitForSelector('.component-list');
    
    // Perform multiple rapid interactions
    const startTime = Date.now();
    
    // Add multiple components in quick succession
    const components = ['单行文本', '多行文本', '数字输入', '单选框组', '复选框组'];
    
    for (const component of components) {
      await page.locator('.component-item').filter({ hasText: component }).click();
    }
    
    // Calculate interaction time
    const interactionTime = Date.now() - startTime;
    
    // Log performance data
    console.log(`Multiple component addition time: ${interactionTime}ms`);
    
    // Assert interaction time is within acceptable range
    expect(interactionTime).toBeLessThan(3001); // 3 seconds
    
    // Verify all components were added
    const fieldCount = await page.locator('.form-canvas .form-field').count();
    expect(fieldCount).toBeGreaterThanOrEqual(components.length);
  });
});
