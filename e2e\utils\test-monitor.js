/**
 * Test Monitoring Service
 * Monitors test execution and performance
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class TestMonitor {
  constructor(config = {}) {
    this.enabled = config.enabled !== false;
    this.logDir = config.logDir || path.join(__dirname, '../test-logs');
    this.metricsFile = config.metricsFile || path.join(this.logDir, 'test-metrics.json');
    this.logFile = config.logFile || path.join(this.logDir, `test-log-${Date.now()}.json`);
    this.testRunId = config.testRunId || this.generateTestRunId();
    this.startTime = Date.now();
    this.metrics = {
      testRunId: this.testRunId,
      startTime: new Date(this.startTime).toISOString(),
      endTime: null,
      duration: 0,
      tests: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        flaky: 0
      },
      performance: {
        cpu: [],
        memory: [],
        responseTime: []
      },
      events: []
    };
    
    // Create log directory if it doesn't exist
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
    
    // Start monitoring if enabled
    if (this.enabled) {
      this.startMonitoring();
    }
  }

  /**
   * Generate a unique test run ID
   * @returns {string} - Unique test run ID
   */
  generateTestRunId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * Start monitoring
   */
  startMonitoring() {
    console.log(`Test monitoring started (Run ID: ${this.testRunId})`);
    
    // Record initial system metrics
    this.recordSystemMetrics();
    
    // Set up interval for system metrics collection
    this.metricsInterval = setInterval(() => {
      this.recordSystemMetrics();
    }, 5000); // Every 5 seconds
    
    // Log start event
    this.logEvent('monitor:start', {
      message: 'Test monitoring started',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }
    
    // Record final metrics
    const endTime = Date.now();
    this.metrics.endTime = new Date(endTime).toISOString();
    this.metrics.duration = endTime - this.startTime;
    
    // Log stop event
    this.logEvent('monitor:stop', {
      message: 'Test monitoring stopped',
      timestamp: new Date().toISOString(),
      duration: this.metrics.duration
    });
    
    // Save metrics
    this.saveMetrics();
    
    console.log(`Test monitoring stopped (Duration: ${this.metrics.duration / 1000}s)`);
  }

  /**
   * Record system metrics
   */
  recordSystemMetrics() {
    const cpuUsage = process.cpuUsage();
    const memoryUsage = process.memoryUsage();
    
    // Calculate CPU usage percentage
    const cpuPercent = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds
    
    // Record metrics
    this.metrics.performance.cpu.push({
      timestamp: Date.now(),
      value: cpuPercent
    });
    
    this.metrics.performance.memory.push({
      timestamp: Date.now(),
      rss: memoryUsage.rss / (1024 * 1024), // Convert to MB
      heapTotal: memoryUsage.heapTotal / (1024 * 1024),
      heapUsed: memoryUsage.heapUsed / (1024 * 1024),
      external: memoryUsage.external / (1024 * 1024)
    });
  }

  /**
   * Record response time
   * @param {string} endpoint - API endpoint
   * @param {number} responseTime - Response time in milliseconds
   */
  recordResponseTime(endpoint, responseTime) {
    this.metrics.performance.responseTime.push({
      timestamp: Date.now(),
      endpoint,
      value: responseTime
    });
  }

  /**
   * Update test metrics
   * @param {object} metrics - Test metrics
   */
  updateTestMetrics(metrics) {
    this.metrics.tests = {
      ...this.metrics.tests,
      ...metrics
    };
  }

  /**
   * Log an event
   * @param {string} type - Event type
   * @param {object} data - Event data
   */
  logEvent(type, data = {}) {
    const event = {
      type,
      timestamp: Date.now(),
      data
    };
    
    this.metrics.events.push(event);
    
    // Save event to log file
    this.appendToLog(event);
  }

  /**
   * Append event to log file
   * @param {object} event - Event to log
   */
  appendToLog(event) {
    try {
      // Create log file if it doesn't exist
      if (!fs.existsSync(this.logFile)) {
        fs.writeFileSync(this.logFile, JSON.stringify({
          testRunId: this.testRunId,
          events: []
        }, null, 2));
      }
      
      // Read existing log
      const log = JSON.parse(fs.readFileSync(this.logFile, 'utf8'));
      
      // Append event
      log.events.push(event);
      
      // Write updated log
      fs.writeFileSync(this.logFile, JSON.stringify(log, null, 2));
    } catch (error) {
      console.error('Failed to append to log:', error.message);
    }
  }

  /**
   * Save metrics to file
   */
  saveMetrics() {
    try {
      // Create metrics file if it doesn't exist
      if (!fs.existsSync(this.metricsFile)) {
        fs.writeFileSync(this.metricsFile, JSON.stringify({
          runs: []
        }, null, 2));
      }
      
      // Read existing metrics
      const metricsData = JSON.parse(fs.readFileSync(this.metricsFile, 'utf8'));
      
      // Add current run metrics
      metricsData.runs.push(this.metrics);
      
      // Limit to last 100 runs
      if (metricsData.runs.length > 100) {
        metricsData.runs = metricsData.runs.slice(-100);
      }
      
      // Write updated metrics
      fs.writeFileSync(this.metricsFile, JSON.stringify(metricsData, null, 2));
    } catch (error) {
      console.error('Failed to save metrics:', error.message);
    }
  }

  /**
   * Get system information
   * @returns {object} - System information
   */
  getSystemInfo() {
    return {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      cpus: os.cpus().length,
      totalMemory: os.totalmem() / (1024 * 1024 * 1024), // GB
      freeMemory: os.freemem() / (1024 * 1024 * 1024), // GB
      uptime: os.uptime()
    };
  }

  /**
   * Get test metrics
   * @returns {object} - Test metrics
   */
  getMetrics() {
    return this.metrics;
  }

  /**
   * Get performance metrics
   * @returns {object} - Performance metrics
   */
  getPerformanceMetrics() {
    return this.metrics.performance;
  }

  /**
   * Get events
   * @returns {Array<object>} - Events
   */
  getEvents() {
    return this.metrics.events;
  }

  /**
   * Generate performance report
   * @returns {object} - Performance report
   */
  generatePerformanceReport() {
    // Calculate average CPU usage
    const avgCpu = this.metrics.performance.cpu.reduce((sum, metric) => sum + metric.value, 0) / 
                  (this.metrics.performance.cpu.length || 1);
    
    // Calculate average memory usage
    const avgMemory = this.metrics.performance.memory.reduce((sum, metric) => sum + metric.rss, 0) / 
                     (this.metrics.performance.memory.length || 1);
    
    // Calculate average response time
    const avgResponseTime = this.metrics.performance.responseTime.reduce((sum, metric) => sum + metric.value, 0) / 
                           (this.metrics.performance.responseTime.length || 1);
    
    // Calculate max values
    const maxCpu = Math.max(...this.metrics.performance.cpu.map(metric => metric.value));
    const maxMemory = Math.max(...this.metrics.performance.memory.map(metric => metric.rss));
    const maxResponseTime = Math.max(...this.metrics.performance.responseTime.map(metric => metric.value));
    
    return {
      testRunId: this.testRunId,
      duration: this.metrics.duration,
      cpu: {
        avg: avgCpu,
        max: maxCpu,
        samples: this.metrics.performance.cpu.length
      },
      memory: {
        avg: avgMemory,
        max: maxMemory,
        samples: this.metrics.performance.memory.length
      },
      responseTime: {
        avg: avgResponseTime,
        max: maxResponseTime,
        samples: this.metrics.performance.responseTime.length
      },
      system: this.getSystemInfo()
    };
  }
}

module.exports = TestMonitor;
