/**
 * API Tests for Authentication Endpoints
 */

const { test, expect } = require('@playwright/test');
const ApiTestClient = require('../../utils/api-test-client');

test.describe('Authentication API Tests', () => {
  let apiClient;

  test.beforeEach(() => {
    apiClient = new ApiTestClient();
  });

  test('should login with valid credentials', async () => {
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };

    const startTime = Date.now();
    const response = await apiClient.post('/users/login', loginData);
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    apiClient.verifySchema(response.data.data, ['token', 'user']);
    apiClient.verifyResponseTime(startTime, 2000);

    // Verify user data
    expect(response.data.data.user.username).toBe(loginData.username);
    expect(response.data.data.token).toBeTruthy();
  });

  test('should fail login with invalid credentials', async () => {
    const loginData = {
      username: 'invalid',
      password: 'invalid'
    };

    const response = await apiClient.post('/users/login', loginData);
    
    // Verify response
    apiClient.verifyStatus(response, 401);
    apiClient.verifyError(response);
  });

  test('should require all login fields', async () => {
    // Missing password
    const missingPasswordData = {
      username: 'admin'
    };

    const missingPasswordResponse = await apiClient.post('/users/login', missingPasswordData);
    apiClient.verifyStatus(missingPasswordResponse, 400);
    apiClient.verifyError(missingPasswordResponse);

    // Missing username
    const missingUsernameData = {
      password: 'admin123'
    };

    const missingUsernameResponse = await apiClient.post('/users/login', missingUsernameData);
    apiClient.verifyStatus(missingUsernameResponse, 400);
    apiClient.verifyError(missingUsernameResponse);
  });

  test('should get current user with valid token', async () => {
    // First login to get token
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };

    const loginResponse = await apiClient.post('/users/login', loginData);
    const token = loginResponse.data.data.token;
    
    // Set token for next request
    apiClient.setAuthToken(token);
    
    // Get current user
    const response = await apiClient.get('/users/me');
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify user data
    expect(response.data.data.username).toBe(loginData.username);
  });

  test('should fail to get current user with invalid token', async () => {
    // Set invalid token
    apiClient.setAuthToken('invalid-token');
    
    // Get current user
    const response = await apiClient.get('/users/me');
    
    // Verify response
    apiClient.verifyStatus(response, 401);
    apiClient.verifyError(response);
  });
});
