<template>
  <div class="test-analytics">
    <h1 class="page-title">测试分析</h1>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 错误提示 -->
    <el-alert
      v-if="error"
      :title="error"
      type="error"
      show-icon
      :closable="false"
      class="error-alert"
    />

    <template v-if="!loading && !error">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-icon success">
              <el-icon><i-ep-data-line /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ summary?.totalRuns || 0 }}</div>
              <div class="stat-label">总测试运行</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-icon primary">
              <el-icon><i-ep-check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ successRate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-icon warning">
              <el-icon><i-ep-timer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ analytics?.summary?.averageDuration.toFixed(2) || 0 }}s</div>
              <div class="stat-label">平均耗时</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-icon danger">
              <el-icon><i-ep-warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ analytics?.summary?.failedTests || 0 }}</div>
              <div class="stat-label">失败测试</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>测试成功率趋势</span>
                <el-select v-model="trendDays" size="small" @change="fetchTrendData">
                  <el-option label="最近7天" :value="7" />
                  <el-option label="最近14天" :value="14" />
                  <el-option label="最近30天" :value="30" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <div id="successRateChart" class="chart"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>测试类型分布</span>
              </div>
            </template>
            <div class="chart-container">
              <div id="testTypeChart" class="chart"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>测试执行时间趋势</span>
              </div>
            </template>
            <div class="chart-container">
              <div id="durationChart" class="chart"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>测试结果分布</span>
              </div>
            </template>
            <div class="chart-container">
              <div id="testResultsChart" class="chart"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 最常失败的测试 -->
      <el-card class="failed-tests-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>最常失败的测试</span>
          </div>
        </template>
        <el-table :data="mostFailedTests" stripe style="width: 100%">
          <el-table-column prop="file" label="测试文件" min-width="200" />
          <el-table-column prop="title" label="测试标题" min-width="300" />
          <el-table-column prop="count" label="失败次数" width="100" />
          <el-table-column label="失败率" width="150">
            <template #default="scope">
              <el-progress
                :percentage="calculateFailureRate(scope.row.count)"
                :color="getFailureRateColor(scope.row.count)"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 最近测试运行 -->
      <el-card class="recent-runs-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>最近测试运行</span>
            <el-button type="primary" size="small" @click="$router.push('/test-analytics/runs')">
              查看所有
            </el-button>
          </div>
        </template>
        <el-table :data="recentRuns" stripe style="width: 100%">
          <el-table-column prop="testRunId" label="运行ID" min-width="150" />
          <el-table-column prop="testType" label="测试类型" width="120" />
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.success ? 'success' : 'danger'">
                {{ scope.row.success ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="耗时" width="100">
            <template #default="scope">
              {{ scope.row.duration ? scope.row.duration.toFixed(2) + 's' : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="timestamp" label="时间" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                link
                @click="$router.push(`/test-analytics/runs/${scope.row.id || scope.row.testRunId}`)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useTestAnalyticsStore } from '../../stores/test-analytics'
import * as echarts from 'echarts/core'
import { LineChart, BarChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  LineChart,
  BarChart,
  PieChart,
  CanvasRenderer
])

const store = useTestAnalyticsStore()
const trendDays = ref(7)

// 计算属性
const loading = computed(() => store.loading)
const error = computed(() => store.error)
const summary = computed(() => store.summary)
const analytics = computed(() => store.analytics)
const successRate = computed(() => store.successRate)
const testTypeDistribution = computed(() => store.testTypeDistribution)
const recentRuns = computed(() => store.recentRuns)
const mostFailedTests = computed(() => store.mostFailedTests)
const trends = computed(() => store.trends)

// 图表实例
let successRateChart = null
let testTypeChart = null
let durationChart = null
let testResultsChart = null

// 初始化图表
const initCharts = () => {
  // 成功率趋势图
  successRateChart = echarts.init(document.getElementById('successRateChart'))

  // 测试类型分布图
  testTypeChart = echarts.init(document.getElementById('testTypeChart'))

  // 执行时间趋势图
  durationChart = echarts.init(document.getElementById('durationChart'))

  // 测试结果分布图
  testResultsChart = echarts.init(document.getElementById('testResultsChart'))

  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    successRateChart.resize()
    testTypeChart.resize()
    durationChart.resize()
    testResultsChart.resize()
  })
}

// 更新图表数据
const updateCharts = () => {
  // 更新成功率趋势图
  if (successRateChart && trends.value) {
    successRateChart.setOption({
      title: {
        text: '测试成功率趋势'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: trends.value.dates
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '成功率',
          type: 'line',
          data: trends.value.successRate,
          smooth: true,
          lineStyle: {
            width: 3
          },
          itemStyle: {
            color: '#67C23A'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(103, 194, 58, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(103, 194, 58, 0.1)'
                }
              ]
            }
          }
        }
      ]
    })
  }

  // 更新测试类型分布图
  if (testTypeChart && testTypeDistribution.value) {
    const data = testTypeDistribution.value.map(item => ({
      name: item.testType || '未知',
      value: item.count
    }))

    testTypeChart.setOption({
      title: {
        text: '测试类型分布'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: data.map(item => item.name)
      },
      series: [
        {
          name: '测试类型',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        }
      ]
    })
  }

  // 更新执行时间趋势图
  if (durationChart && trends.value) {
    durationChart.setOption({
      title: {
        text: '测试执行时间趋势'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          return `${params[0].axisValue}<br/>${params[0].marker}${params[0].seriesName}: ${params[0].value.toFixed(2)}s`
        }
      },
      xAxis: {
        type: 'category',
        data: trends.value.dates
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}s'
        }
      },
      series: [
        {
          name: '平均执行时间',
          type: 'bar',
          data: trends.value.averageDuration,
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    })
  }

  // 更新测试结果分布图
  if (testResultsChart && analytics.value && analytics.value.summary) {
    const { passedTests, failedTests, skippedTests } = analytics.value.summary

    testResultsChart.setOption({
      title: {
        text: '测试结果分布'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: ['通过', '失败', '跳过']
      },
      series: [
        {
          name: '测试结果',
          type: 'pie',
          radius: '70%',
          data: [
            { value: passedTests, name: '通过', itemStyle: { color: '#67C23A' } },
            { value: failedTests, name: '失败', itemStyle: { color: '#F56C6C' } },
            { value: skippedTests, name: '跳过', itemStyle: { color: '#E6A23C' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }
}

// 计算失败率
const calculateFailureRate = (count) => {
  if (!summary.value || !summary.value.totalRuns) return 0
  return Math.min(100, Math.round((count / summary.value.totalRuns) * 100))
}

// 获取失败率颜色
const getFailureRateColor = (count) => {
  const rate = calculateFailureRate(count)
  if (rate < 30) return '#E6A23C'
  if (rate < 60) return '#F56C6C'
  return '#F56C6C'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 获取趋势数据
const fetchTrendData = async () => {
  await store.fetchTrends({ days: trendDays.value })
  updateCharts()
}

// 监听趋势数据变化
watch(trends, () => {
  updateCharts()
})

// 监听分析数据变化
watch(analytics, () => {
  updateCharts()
})

// 监听测试类型分布变化
watch(testTypeDistribution, () => {
  updateCharts()
})

// 组件挂载时初始化
onMounted(async () => {
  // 获取数据
  await Promise.all([
    store.fetchSummary(),
    store.fetchAnalytics(),
    store.fetchTrends({ days: trendDays.value })
  ])

  // 初始化图表
  initCharts()

  // 更新图表数据
  updateCharts()
})
</script>

<style scoped>
.test-analytics {
  padding: 20px;
}

.page-title {
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 500;
}

.loading-container {
  padding: 20px;
}

.error-alert {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  height: 100px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: 24px;
  margin-right: 16px;
}

.stat-icon.success {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.stat-icon.primary {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.stat-icon.warning {
  background-color: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.stat-icon.danger {
  background-color: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.failed-tests-card,
.recent-runs-card {
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .stat-card {
    margin-bottom: 16px;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
