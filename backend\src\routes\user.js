const express = require('express');
const router = express.Router();
const userController = require('../controllers/user');

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: 用户管理API
 */

/**
 * @swagger
 * /users/register:
 *   post:
 *     summary: 用户注册
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - password
 *               - email
 *               - full_name
 *             properties:
 *               username:
 *                 type: string
 *                 example: "zhangsan"
 *               password:
 *                 type: string
 *                 format: password
 *                 example: "Password123"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               full_name:
 *                 type: string
 *                 example: "张三"
 *               phone:
 *                 type: string
 *                 example: "13800138000"
 *               avatar:
 *                 type: string
 *                 example: "/uploads/avatars/default.png"
 *     responses:
 *       201:
 *         description: 用户注册成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 3
 *                     username:
 *                       type: string
 *                       example: "zhangsan"
 *                     email:
 *                       type: string
 *                       example: "<EMAIL>"
 *                     full_name:
 *                       type: string
 *                       example: "张三"
 *       400:
 *         description: 请求参数错误
 *       409:
 *         description: 用户名或邮箱已存在
 *       500:
 *         description: 服务器错误
 */
// 注册路由已在app.js中直接定义，这里不再重复注册
// router.post('/register', userController.register);

/**
 * @swagger
 * /users/login:
 *   post:
 *     summary: 用户登录
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - password
 *             properties:
 *               username:
 *                 type: string
 *                 example: "admin"
 *               password:
 *                 type: string
 *                 format: password
 *                 example: "admin123"
 *     responses:
 *       200:
 *         description: 登录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     token:
 *                       type: string
 *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                     user:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                           example: 1
 *                         username:
 *                           type: string
 *                           example: "admin"
 *                         full_name:
 *                           type: string
 *                           example: "系统管理员"
 *                         email:
 *                           type: string
 *                           example: "<EMAIL>"
 *                         roles:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: ["admin"]
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 用户名或密码不正确
 *       500:
 *         description: 服务器错误
 */
// 登录路由已在app.js中直接定义，这里不再重复注册
// router.post('/login', userController.login);

/**
 * @swagger
 * /users/me:
 *   get:
 *     summary: 获取当前登录用户信息
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取用户信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     username:
 *                       type: string
 *                       example: "admin"
 *                     email:
 *                       type: string
 *                       example: "<EMAIL>"
 *                     full_name:
 *                       type: string
 *                       example: "系统管理员"
 *                     avatar:
 *                       type: string
 *                       example: "/uploads/avatars/admin.png"
 *                     roles:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           name:
 *                             type: string
 *                             example: "admin"
 *                           display_name:
 *                             type: string
 *                             example: "系统管理员"
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/me', userController.getCurrentUser);

// 更新用户信息
router.put('/:id', userController.updateUser);

// 获取所有用户
router.get('/', userController.getAllUsers);

// 获取单个用户
router.get('/:id', userController.getUserById);

// 删除用户
router.delete('/:id', userController.deleteUser);

// 修改密码
router.put('/:id/password', userController.changePassword);

// 分配角色
router.post('/:id/roles', userController.assignRoles);

// 重置密码（管理员）
router.post('/:id/reset-password', userController.resetPassword);

module.exports = router;
