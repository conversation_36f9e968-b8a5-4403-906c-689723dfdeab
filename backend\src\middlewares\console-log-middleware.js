/**
 * 简单的控制台日志中间件
 * 使用console.log直接打印请求和响应信息
 */
function consoleLogMiddleware(req, res, next) {
  // 记录请求开始时间
  const startTime = process.hrtime();
  
  // 打印请求信息
  console.log(`\n[REQUEST] ${req.method} ${req.url}`);
  
  // 打印请求体 (如果不是GET请求)
  if (req.method !== 'GET') {
    console.log('[REQUEST BODY]', req.body);
  }
  
  // 打印查询参数 (如果有)
  if (Object.keys(req.query).length > 0) {
    console.log('[QUERY PARAMS]', req.query);
  }
  
  // 捕获响应结束事件
  res.on('finish', () => {
    // 计算请求处理时间
    const hrTime = process.hrtime(startTime);
    const responseTime = hrTime[0] * 1000 + hrTime[1] / 1000000;
    
    // 打印响应信息
    console.log(`[RESPONSE] ${res.statusCode} (${responseTime.toFixed(2)}ms)`);
    console.log('------------------------------');
  });
  
  next();
}

module.exports = consoleLogMiddleware;
