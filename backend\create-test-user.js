/**
 * Create test user script
 */
const bcrypt = require('bcryptjs');
const { sequelize, User, Role } = require('./src/models');

async function createTestUser() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Check if user role exists
    let userRole = await Role.findOne({ where: { name: 'user' } });
    
    if (!userRole) {
      console.log('User role not found. Creating...');
      userRole = await Role.create({
        name: 'user',
        display_name: '普通用户',
        description: '普通用户，拥有基本权限'
      });
    }

    // Create a simple test user
    const username = 'testuser';
    const password = '123456';
    
    // Check if user already exists
    let user = await User.findOne({ where: { username } });
    
    if (user) {
      console.log(`User ${username} already exists. Deleting...`);
      await user.destroy();
    }
    
    console.log(`Creating user ${username} with password ${password}...`);
    
    // Hash the password manually
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    console.log(`Generated password hash: ${hashedPassword}`);
    
    // Create the user
    user = await User.create({
      username,
      password: hashedPassword,
      email: `${username}@example.com`,
      full_name: 'Test User',
      status: 'active'
    });
    
    // Assign user role
    await user.addRole(userRole);
    
    console.log(`User ${username} created successfully with ID ${user.id}.`);
    
    // Verify the password
    const isMatch = await bcrypt.compare(password, user.password);
    console.log(`Password verification: ${isMatch}`);
    
    // Try to verify with a different password
    const wrongMatch = await bcrypt.compare('wrong', user.password);
    console.log(`Wrong password verification: ${wrongMatch}`);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

createTestUser();
