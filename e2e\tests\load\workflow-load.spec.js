/**
 * Load Tests for Workflow System
 * Tests the system's performance under load
 */

const { test, expect } = require('@playwright/test');
const ApiTestClient = require('../../utils/api-test-client');
const TestDataManager = require('../../utils/test-data-manager');

test.describe('Workflow System Load Tests', () => {
  let apiClient;
  let testDataManager;
  let authToken;
  let formId;
  let workflowId;

  // Number of concurrent users to simulate
  const CONCURRENT_USERS = 5;
  
  // Number of workflow instances to create per user
  const INSTANCES_PER_USER = 3;
  
  // Maximum acceptable response time in milliseconds
  const MAX_RESPONSE_TIME = 3001;

  test.beforeAll(async () => {
    // Login to get auth token
    apiClient = new ApiTestClient();
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };

    const loginResponse = await apiClient.post('/users/login', loginData);
    authToken = loginResponse.data.data.token;
    apiClient.setAuthToken(authToken);
    
    // Create test data manager
    testDataManager = new TestDataManager({
      baseURL: apiClient.baseURL,
      authToken
    });
    
    // Create a form for load tests
    try {
      const form = await testDataManager.createForm({
        title: `Load Test Form ${Date.now()}`
      });
      formId = form.id;
      console.log(`Created form with ID: ${formId}`);
      
      // Create a workflow for load tests
      const workflow = await testDataManager.createWorkflow({
        title: `Load Test Workflow ${Date.now()}`,
        form_template_id: formId
      });
      workflowId = workflow.id;
      console.log(`Created workflow with ID: ${workflowId}`);
    } catch (error) {
      console.error('Failed to create test data:', error.message);
    }
  });

  test.afterAll(async () => {
    // Clean up test data
    await testDataManager.cleanup();
  });

  test('should handle concurrent workflow instance creation', async () => {
    // Skip if no workflow was created
    test.skip(!workflowId, 'No workflow created');
    
    // Create promises for concurrent requests
    const promises = [];
    const responseTimes = [];
    const results = {
      success: 0,
      failure: 0,
      totalTime: 0
    };
    
    console.log(`Starting load test with ${CONCURRENT_USERS} users, each creating ${INSTANCES_PER_USER} workflow instances`);
    
    // Start timer for overall test
    const overallStartTime = Date.now();
    
    // Create workflow instances concurrently
    for (let user = 0; user < CONCURRENT_USERS; user++) {
      for (let instance = 0; instance < INSTANCES_PER_USER; instance++) {
        const instanceData = {
          title: `Load Test Instance User${user}-${instance}-${Date.now()}`,
          form_data: {
            test_text: `Load Test Value User${user}-${instance}`,
            test_textarea: `Load Test Description User${user}-${instance}`,
            test_select: 'option1'
          }
        };
        
        // Create promise for this request
        const promise = (async () => {
          const startTime = Date.now();
          try {
            const response = await apiClient.post(`/workflows/${workflowId}/instances`, instanceData);
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            responseTimes.push(responseTime);
            results.totalTime += responseTime;
            
            if (response.success) {
              results.success++;
              return { success: true, responseTime, instanceId: response.data.data.id };
            } else {
              results.failure++;
              return { success: false, responseTime, error: response.error };
            }
          } catch (error) {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            responseTimes.push(responseTime);
            results.totalTime += responseTime;
            results.failure++;
            
            return { success: false, responseTime, error: error.message };
          }
        })();
        
        promises.push(promise);
      }
    }
    
    // Wait for all requests to complete
    const responses = await Promise.all(promises);
    
    // Calculate overall test time
    const overallEndTime = Date.now();
    const overallTestTime = overallEndTime - overallStartTime;
    
    // Calculate statistics
    const totalRequests = CONCURRENT_USERS * INSTANCES_PER_USER;
    const avgResponseTime = results.totalTime / totalRequests;
    const maxResponseTime = Math.max(...responseTimes);
    const minResponseTime = Math.min(...responseTimes);
    
    // Sort response times and calculate percentiles
    responseTimes.sort((a, b) => a - b);
    const p50 = responseTimes[Math.floor(responseTimes.length * 0.5)];
    const p90 = responseTimes[Math.floor(responseTimes.length * 0.9)];
    const p95 = responseTimes[Math.floor(responseTimes.length * 0.95)];
    const p99 = responseTimes[Math.floor(responseTimes.length * 0.99)];
    
    // Log results
    console.log('Load Test Results:');
    console.log(`Total Requests: ${totalRequests}`);
    console.log(`Successful Requests: ${results.success}`);
    console.log(`Failed Requests: ${results.failure}`);
    console.log(`Success Rate: ${(results.success / totalRequests * 100).toFixed(2)}%`);
    console.log(`Overall Test Time: ${overallTestTime}ms`);
    console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
    console.log(`Min Response Time: ${minResponseTime}ms`);
    console.log(`Max Response Time: ${maxResponseTime}ms`);
    console.log(`P50 Response Time: ${p50}ms`);
    console.log(`P90 Response Time: ${p90}ms`);
    console.log(`P95 Response Time: ${p95}ms`);
    console.log(`P99 Response Time: ${p99}ms`);
    
    // Save results to file
    const fs = require('fs');
    const path = require('path');
    const resultsDir = path.join(__dirname, '../../load-test-results');
    
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    const resultsFile = path.join(resultsDir, `load-test-results-${Date.now()}.json`);
    fs.writeFileSync(
      resultsFile,
      JSON.stringify({
        testConfig: {
          concurrentUsers: CONCURRENT_USERS,
          instancesPerUser: INSTANCES_PER_USER,
          totalRequests,
          maxResponseTime: MAX_RESPONSE_TIME
        },
        results: {
          success: results.success,
          failure: results.failure,
          successRate: (results.success / totalRequests * 100).toFixed(2),
          overallTestTime,
          avgResponseTime,
          minResponseTime,
          maxResponseTime,
          p50,
          p90,
          p95,
          p99
        },
        detailedResults: responses
      }, null, 2)
    );
    
    // Verify performance meets requirements
    expect(results.success, `Expected all requests to succeed, but ${results.failure} failed`).toBe(totalRequests);
    expect(avgResponseTime, `Average response time ${avgResponseTime}ms exceeds maximum ${MAX_RESPONSE_TIME}ms`).toBeLessThanOrEqual(MAX_RESPONSE_TIME);
    expect(p95, `95th percentile response time ${p95}ms exceeds maximum ${MAX_RESPONSE_TIME}ms`).toBeLessThanOrEqual(MAX_RESPONSE_TIME);
  });

  test('should handle concurrent workflow task processing', async ({ browser }) => {
    // Skip if no workflow was created
    test.skip(!workflowId, 'No workflow created');
    
    // Create a workflow instance first
    const instanceData = {
      title: `Task Load Test Instance ${Date.now()}`,
      form_data: {
        test_text: 'Task Load Test Value',
        test_textarea: 'Task Load Test Description',
        test_select: 'option1'
      }
    };
    
    const instanceResponse = await apiClient.post(`/workflows/${workflowId}/instances`, instanceData);
    
    // Skip if instance creation failed
    test.skip(!instanceResponse.success, 'Failed to create workflow instance');
    
    const instanceId = instanceResponse.data.data.id;
    console.log(`Created workflow instance with ID: ${instanceId}`);
    
    // Get tasks for this instance
    const tasksResponse = await apiClient.get('/workflows/tasks/todo');
    
    // Skip if no tasks found
    test.skip(!tasksResponse.success || tasksResponse.data.data.tasks.length === 0, 'No tasks found');
    
    // Find task for our instance
    const task = tasksResponse.data.data.tasks.find(t => t.workflow_instance_id === instanceId);
    
    // Skip if no task found for our instance
    test.skip(!task, 'No task found for test instance');
    
    const taskId = task.id;
    console.log(`Found task with ID: ${taskId}`);
    
    // Number of concurrent users to simulate for task processing
    const CONCURRENT_TASK_USERS = 3;
    
    // Create contexts for concurrent users
    const contexts = [];
    const results = {
      success: 0,
      failure: 0,
      totalTime: 0
    };
    
    console.log(`Starting task load test with ${CONCURRENT_TASK_USERS} concurrent users`);
    
    // Start timer for overall test
    const overallStartTime = Date.now();
    
    // Create browser contexts for concurrent users
    for (let i = 0; i < CONCURRENT_TASK_USERS; i++) {
      const context = await browser.newContext();
      const page = await context.newPage();
      
      // Login
      await page.goto('/login');
      await page.fill('input[placeholder="用户名"]', 'admin');
      await page.fill('input[placeholder="密码"]', 'admin123');
      await page.click('.login-button');
      await page.waitForURL(/.*\//);
      
      contexts.push({ context, page });
    }
    
    // Process task concurrently
    const promises = contexts.map(async ({ page }, index) => {
      const startTime = Date.now();
      try {
        // Navigate to workflow process page
        await page.goto('/workflow-process');
        
        // Wait for table to load
        await page.waitForSelector('.el-table__row');
        
        // Find and click on process button for our task
        const taskRow = page.locator('.el-table__row').filter({ hasText: instanceData.title });
        await taskRow.locator('button:has-text("处理")').click();
        
        // Wait for dialog to appear
        await page.waitForSelector('.el-dialog');
        
        // Fill comments
        await page.fill('textarea[placeholder="请输入处理意见"]', `Load Test User ${index} - ${Date.now()}`);
        
        // Select approve operation
        await page.click('.operation-buttons button:has-text("同意")');
        
        // Submit form
        await page.click('button:has-text("提交")');
        
        // Check for success or error message
        const successMessage = await page.locator('.el-message--success').isVisible({ timeout: 5000 });
        const errorMessage = await page.locator('.el-message--error').isVisible({ timeout: 1000 });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        results.totalTime += responseTime;
        
        if (successMessage) {
          results.success++;
          return { success: true, responseTime, user: index };
        } else if (errorMessage) {
          results.failure++;
          return { success: false, responseTime, error: 'Error message shown', user: index };
        } else {
          results.failure++;
          return { success: false, responseTime, error: 'No message shown', user: index };
        }
      } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        results.totalTime += responseTime;
        results.failure++;
        
        return { success: false, responseTime, error: error.message, user: index };
      }
    });
    
    // Wait for all requests to complete
    const responses = await Promise.all(promises);
    
    // Close all contexts
    for (const { context } of contexts) {
      await context.close();
    }
    
    // Calculate overall test time
    const overallEndTime = Date.now();
    const overallTestTime = overallEndTime - overallStartTime;
    
    // Calculate statistics
    const totalRequests = CONCURRENT_TASK_USERS;
    const avgResponseTime = results.totalTime / totalRequests;
    
    // Log results
    console.log('Task Load Test Results:');
    console.log(`Total Requests: ${totalRequests}`);
    console.log(`Successful Requests: ${results.success}`);
    console.log(`Failed Requests: ${results.failure}`);
    console.log(`Success Rate: ${(results.success / totalRequests * 100).toFixed(2)}%`);
    console.log(`Overall Test Time: ${overallTestTime}ms`);
    console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
    
    // Save results to file
    const fs = require('fs');
    const path = require('path');
    const resultsDir = path.join(__dirname, '../../load-test-results');
    
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    const resultsFile = path.join(resultsDir, `task-load-test-results-${Date.now()}.json`);
    fs.writeFileSync(
      resultsFile,
      JSON.stringify({
        testConfig: {
          concurrentUsers: CONCURRENT_TASK_USERS,
          totalRequests,
          maxResponseTime: MAX_RESPONSE_TIME
        },
        results: {
          success: results.success,
          failure: results.failure,
          successRate: (results.success / totalRequests * 100).toFixed(2),
          overallTestTime,
          avgResponseTime
        },
        detailedResults: responses
      }, null, 2)
    );
    
    // Verify at least one request succeeded
    // Note: We can't expect all to succeed since only one user can process a task
    expect(results.success, 'Expected at least one request to succeed').toBeGreaterThan(0);
  });
});
