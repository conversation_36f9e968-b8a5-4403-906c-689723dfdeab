const { test, expect } = require('@playwright/test');
const { login, navigateToModule } = require('../utils/test-helpers');

test.describe('Form Design Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await login(page, 'admin');
    // Navigate to form design page
    await navigateToModule(page, '表单设计器');
  });

  test('should display form list', async ({ page }) => {
    // Verify form list tab is active
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('表单列表');
    
    // Verify form table is visible
    await expect(page.locator('.el-table')).toBeVisible();
  });

  test('should open form designer', async ({ page }) => {
    // Click on create form button
    await page.click('button:has-text("创建表单")');
    
    // Verify form designer tab is active
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('表单设计');
    
    // Verify component list is visible
    await expect(page.locator('.component-list')).toBeVisible();
    
    // Verify design canvas is visible
    await expect(page.locator('.form-canvas')).toBeVisible();
    
    // Verify property panel is visible
    await expect(page.locator('.property-panel')).toBeVisible();
  });

  test('should add components to form', async ({ page }) => {
    // Click on create form button
    await page.click('button:has-text("创建表单")');
    
    // Wait for form designer to load
    await page.waitForSelector('.component-list');
    
    // Add input component
    await page.locator('.component-item').filter({ hasText: '单行文本' }).click();
    
    // Verify component is added to canvas
    await expect(page.locator('.form-canvas .form-field')).toBeVisible();
    
    // Add textarea component
    await page.locator('.component-item').filter({ hasText: '多行文本' }).click();
    
    // Verify second component is added
    await expect(page.locator('.form-canvas .form-field')).toHaveCount(2);
  });

  test('should edit component properties', async ({ page }) => {
    // Click on create form button
    await page.click('button:has-text("创建表单")');
    
    // Wait for form designer to load
    await page.waitForSelector('.component-list');
    
    // Add input component
    await page.locator('.component-item').filter({ hasText: '单行文本' }).click();
    
    // Click on the added component to select it
    await page.locator('.form-canvas .form-field').click();
    
    // Edit label property
    const newLabel = '测试字段' + Date.now();
    await page.fill('.property-panel input[placeholder="请输入字段标签"]', newLabel);
    
    // Verify label is updated in the form
    await expect(page.locator('.form-canvas .form-field label')).toContainText(newLabel);
  });

  test('should save form template', async ({ page }) => {
    // Click on create form button
    await page.click('button:has-text("创建表单")');
    
    // Wait for form designer to load
    await page.waitForSelector('.component-list');
    
    // Add input component
    await page.locator('.component-item').filter({ hasText: '单行文本' }).click();
    
    // Fill form info
    const formName = '测试表单' + Date.now();
    await page.fill('input[placeholder="请输入表单名称"]', formName);
    await page.fill('textarea[placeholder="请输入表单描述"]', '这是一个测试表单');
    
    // Save form
    await page.click('button:has-text("保存")');
    
    // Verify success message
    await expect(page.locator('.el-message--success')).toBeVisible();
    
    // Switch to form list tab
    await page.locator('.el-tabs__item').filter({ hasText: '表单列表' }).click();
    
    // Verify new form appears in the list
    await expect(page.locator('.el-table__row').filter({ hasText: formName })).toBeVisible();
  });
});
