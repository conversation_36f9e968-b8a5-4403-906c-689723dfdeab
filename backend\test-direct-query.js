/**
 * Direct database query test
 * Run with: node test-direct-query.js
 */

const { sequelize, WorkflowInstance, WorkflowTemplate, FormTemplate } = require('./src/models');

async function testDirectQuery() {
  try {
    console.log('=== Testing Direct Database Queries ===');
    
    // Raw SQL query to get workflow instance 81
    console.log('\n1. Raw SQL query for workflow instance 81...');
    const [instanceResults] = await sequelize.query(
      'SELECT * FROM workflow_instances WHERE id = 81'
    );
    
    if (instanceResults.length === 0) {
      console.log('Workflow instance 81 not found using raw SQL');
      return;
    }
    
    const instanceRow = instanceResults[0];
    console.log('Found workflow instance:');
    console.log(`- ID: ${instanceRow.id}`);
    console.log(`- Title: ${instanceRow.title}`);
    console.log(`- Workflow template ID: ${instanceRow.workflow_template_id}`);
    
    // Raw SQL query to get workflow template
    console.log(`\n2. Raw SQL query for workflow template ${instanceRow.workflow_template_id}...`);
    const [templateResults] = await sequelize.query(
      `SELECT * FROM workflow_templates WHERE id = ${instanceRow.workflow_template_id}`
    );
    
    if (templateResults.length === 0) {
      console.log(`Workflow template ${instanceRow.workflow_template_id} not found using raw SQL`);
      return;
    }
    
    const templateRow = templateResults[0];
    console.log('Found workflow template:');
    console.log(`- ID: ${templateRow.id}`);
    console.log(`- Title: ${templateRow.title}`);
    console.log(`- Form template ID: ${templateRow.form_template_id}`);
    
    // Raw SQL query to get form template
    if (templateRow.form_template_id) {
      console.log(`\n3. Raw SQL query for form template ${templateRow.form_template_id}...`);
      const [formResults] = await sequelize.query(
        `SELECT * FROM form_templates WHERE id = ${templateRow.form_template_id}`
      );
      
      if (formResults.length === 0) {
        console.log(`Form template ${templateRow.form_template_id} not found using raw SQL`);
      } else {
        const formRow = formResults[0];
        console.log('Found form template:');
        console.log(`- ID: ${formRow.id}`);
        console.log(`- Title: ${formRow.title}`);
        console.log(`- Has schema: ${!!formRow.schema}`);
      }
    } else {
      console.log('No form template ID in workflow template');
    }
    
    // Now try with Sequelize models
    console.log('\n4. Using Sequelize models...');
    const instance = await WorkflowInstance.findByPk(81);
    
    if (!instance) {
      console.log('Workflow instance 81 not found using Sequelize');
      return;
    }
    
    console.log('Found workflow instance:');
    console.log(`- ID: ${instance.id}`);
    console.log(`- Title: ${instance.title}`);
    console.log(`- Workflow template ID: ${instance.workflow_template_id}`);
    
    const template = await WorkflowTemplate.findByPk(instance.workflow_template_id);
    
    if (!template) {
      console.log(`Workflow template ${instance.workflow_template_id} not found using Sequelize`);
      return;
    }
    
    console.log('Found workflow template:');
    console.log(`- ID: ${template.id}`);
    console.log(`- Title: ${template.title}`);
    console.log(`- Form template ID: ${template.form_template_id}`);
    
    if (template.form_template_id) {
      const formTemplate = await FormTemplate.findByPk(template.form_template_id);
      
      if (!formTemplate) {
        console.log(`Form template ${template.form_template_id} not found using Sequelize`);
      } else {
        console.log('Found form template:');
        console.log(`- ID: ${formTemplate.id}`);
        console.log(`- Title: ${formTemplate.title}`);
        console.log(`- Has schema: ${!!formTemplate.schema}`);
      }
    } else {
      console.log('No form template ID in workflow template');
    }
    
    console.log('\n=== Test completed ===');
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testDirectQuery(); 