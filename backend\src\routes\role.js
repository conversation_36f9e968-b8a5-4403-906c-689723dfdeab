const express = require('express');
const router = express.Router();
const roleController = require('../controllers/role');
const { authenticate } = require('../middlewares/auth');

/**
 * @swagger
 * tags:
 *   name: Roles
 *   description: 角色管理API
 */

/**
 * @swagger
 * /roles:
 *   get:
 *     summary: 获取所有角色
 *     tags: [Roles]
 *     responses:
 *       200:
 *         description: 成功获取角色列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: "admin"
 *                       display_name:
 *                         type: string
 *                         example: "管理员"
 *                       description:
 *                         type: string
 *                         example: "系统管理员"
 *       500:
 *         description: 服务器错误
 */
router.get('/', roleController.getAllRoles);

/**
 * @swagger
 * /roles/{id}:
 *   get:
 *     summary: 获取单个角色信息
 *     tags: [Roles]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 角色ID
 *     responses:
 *       200:
 *         description: 成功获取角色信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     name:
 *                       type: string
 *                       example: "admin"
 *                     display_name:
 *                       type: string
 *                       example: "管理员"
 *                     description:
 *                       type: string
 *                       example: "系统管理员"
 *                     permissions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           name:
 *                             type: string
 *                             example: "create_user"
 *                           description:
 *                             type: string
 *                             example: "创建用户"
 *       404:
 *         description: 角色不存在
 *       500:
 *         description: 服务器错误
 */
router.get('/:id', roleController.getRoleById);

/**
 * @swagger
 * /roles:
 *   post:
 *     summary: 创建新角色
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - display_name
 *             properties:
 *               name:
 *                 type: string
 *                 example: "editor"
 *               display_name:
 *                 type: string
 *                 example: "编辑"
 *               description:
 *                 type: string
 *                 example: "内容编辑"
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 3]
 *     responses:
 *       201:
 *         description: 角色创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 3
 *                     name:
 *                       type: string
 *                       example: "editor"
 *                     display_name:
 *                       type: string
 *                       example: "编辑"
 *                     description:
 *                       type: string
 *                       example: "内容编辑"
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 没有权限
 *       500:
 *         description: 服务器错误
 */
router.post('/', authenticate, roleController.createRole);

/**
 * @swagger
 * /roles/{id}:
 *   put:
 *     summary: 更新角色信息
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 角色ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: "editor_updated"
 *               display_name:
 *                 type: string
 *                 example: "内容编辑"
 *               description:
 *                 type: string
 *                 example: "负责内容编辑与审核"
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 3, 4]
 *     responses:
 *       200:
 *         description: 角色更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 3
 *                     name:
 *                       type: string
 *                       example: "editor_updated"
 *                     display_name:
 *                       type: string
 *                       example: "内容编辑"
 *                     description:
 *                       type: string
 *                       example: "负责内容编辑与审核"
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 没有权限
 *       404:
 *         description: 角色不存在
 *       500:
 *         description: 服务器错误
 */
router.put('/:id', authenticate, roleController.updateRole);

/**
 * @swagger
 * /roles/{id}:
 *   delete:
 *     summary: 删除角色
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 角色ID
 *     responses:
 *       200:
 *         description: 角色删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "角色删除成功"
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 没有权限
 *       404:
 *         description: 角色不存在
 *       500:
 *         description: 服务器错误
 */
router.delete('/:id', authenticate, roleController.deleteRole);

module.exports = router; 