/**
 * Fix database script
 * Run with: node fix-database.js
 */

const { sequelize } = require('./src/models');

async function fixDatabase() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    console.log('Modifying workflow_task_histories table...');
    
    // Execute raw SQL to modify the task_id column to allow NULL values
    await sequelize.query(`
      ALTER TABLE workflow_task_histories 
      ALTER COLUMN task_id DROP NOT NULL;
    `);
    
    console.log('Database schema updated successfully.');
  } catch (error) {
    console.error('Error fixing database:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

fixDatabase();
