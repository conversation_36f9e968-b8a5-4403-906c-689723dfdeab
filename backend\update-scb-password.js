/**
 * Update SCB user password script
 */
const bcrypt = require('bcryptjs');
const { sequelize } = require('./src/models');

async function updateScbPassword() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Generate a new password hash
    const password = 'password123';
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    console.log(`Generated password hash: ${hashedPassword}`);
    
    // Update the password directly in the database
    const result = await sequelize.query(
      'UPDATE users SET password = :password WHERE username = :username',
      {
        replacements: { password: hashedPassword, username: 'scb' },
        type: sequelize.QueryTypes.UPDATE
      }
    );
    
    console.log(`Update result:`, result);
    
    // Verify the password was updated
    const users = await sequelize.query(
      'SELECT id, username, password FROM users WHERE username = :username',
      {
        replacements: { username: 'scb' },
        type: sequelize.QueryTypes.SELECT
      }
    );
    
    if (users && users.length > 0) {
      const user = users[0];
      console.log(`User found: ID=${user.id}, Username=${user.username}`);
      console.log(`Updated password hash: ${user.password}`);
    } else {
      console.log('User not found.');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

updateScbPassword();
