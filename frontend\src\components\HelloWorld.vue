<script setup>
import { ref } from 'vue'

defineProps({
  msg: String,
})

const count = ref(0)
</script>

<template>
  <div class="hello-container">
    <div class="header-section">
      <h1 class="title">{{ msg }}</h1>
      <div class="subtitle">开启您的工作流管理之旅</div>
    </div>

    <div class="cards-container">
      <div class="feature-card">
        <div class="card-content">
          <h2>互动计数器</h2>
          <div class="counter-display">{{ count }}</div>
          <button class="action-button" type="button" @click="count++">点击增加</button>
          <p class="card-text">
            尝试点击按钮，体验实时数据更新的交互效果
          </p>
        </div>
      </div>

      <div class="feature-card">
        <div class="card-content">
          <h2>快速开始</h2>
          <p class="card-text">
            修改 <code>components/HelloWorld.vue</code> 来自定义此组件
          </p>
          <a href="https://vuejs.org/guide/quick-start.html#local" target="_blank" class="card-link">
            探索 Vue 官方文档
          </a>
        </div>
      </div>

      <div class="feature-card">
        <div class="card-content">
          <h2>深入学习</h2>
          <p class="card-text">
            查看工作流系统的核心功能和使用方法
          </p>
          <a href="https://vuejs.org/guide/scaling-up/tooling.html#ide-support" target="_blank" class="card-link">
            查看更多开发指南
          </a>
        </div>
      </div>
    </div>
    
    <footer class="footer">
      <p>由 Vue 3 和 Vite 驱动</p>
    </footer>
  </div>
</template>

<style scoped>
.hello-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-2xl);
  font-family: var(--font-family);
}

.header-section {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(120deg, var(--primary-color), var(--primary-color-light), #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: var(--spacing-xs);
}

.subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacing-lg);
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color-split);
  box-shadow: var(--box-shadow-card);
  transition: transform 0.3s var(--animation-timing-function), 
              box-shadow 0.3s var(--animation-timing-function);
  overflow: hidden;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-dropdown);
  border-color: var(--primary-color-bg);
}

.card-content {
  padding: var(--spacing-xl);
}

.card-content h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-lg);
  color: var(--text-color);
}

.counter-display {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: var(--spacing-lg) 0;
  color: var(--primary-color);
}

.action-button {
  background: linear-gradient(to right, var(--primary-color), var(--primary-color-light));
  color: white;
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-full);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.3s var(--animation-timing-function);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
  margin-bottom: var(--spacing-lg);
}

.action-button:hover {
  background: linear-gradient(to right, var(--primary-color-dark), var(--primary-color));
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.6);
  transform: translateY(-2px);
}

.card-text {
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-color-secondary);
  margin-bottom: var(--spacing-lg);
}

.card-link {
  display: inline-block;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  position: relative;
  transition: color 0.3s var(--animation-timing-function);
}

.card-link:hover {
  color: var(--primary-color-light);
}

.card-link::after {
  content: '';
  position: absolute;
  width: 100%;
  transform: scaleX(0);
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--primary-color-light);
  transform-origin: bottom right;
  transition: transform 0.3s var(--animation-timing-function);
}

.card-link:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

code {
  background-color: var(--primary-color-bg);
  color: var(--primary-color);
  padding: 0.2em 0.4em;
  border-radius: var(--border-radius-sm);
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.9em;
}

.footer {
  text-align: center;
  color: var(--text-color-secondary);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color-split);
}

/* 适应暗黑模式 */
:global(.dark-mode) .feature-card {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

:global(.dark-mode) .card-content h2 {
  color: var(--text-color);
}

:global(.dark-mode) .counter-display {
  color: var(--primary-color-light);
}

:global(.dark-mode) .action-button {
  background: linear-gradient(to right, var(--primary-color), var(--primary-color-light));
}

:global(.dark-mode) .action-button:hover {
  background: linear-gradient(to right, var(--primary-color-light), #60a5fa);
}

:global(.dark-mode) code {
  background-color: rgba(59, 130, 246, 0.15);
  color: var(--primary-color-light);
}

@media (max-width: 768px) {
  .cards-container {
    grid-template-columns: 1fr;
  }
  
  .title {
    font-size: var(--font-size-2xl);
  }
  
  .subtitle {
    font-size: var(--font-size-lg);
  }
  
  .hello-container {
    padding: var(--spacing-xl);
  }
}
</style>
