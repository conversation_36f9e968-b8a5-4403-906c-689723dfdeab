'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // 获取管理员用户ID
    const users = await queryInterface.sequelize.query(
      `SELECT id, username FROM users WHERE username = 'admin';`,
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const adminId = users[0]?.id;

    if (adminId) {
      // 插入表单模板数据
      await queryInterface.bulkInsert('form_templates', [
        {
          title: '请假申请表',
          description: '用于员工请假申请',
          creator_id: adminId,
          version: 1,
          status: 'published',
          schema: JSON.stringify({
            fields: [
              {
                id: 'name',
                type: 'text',
                label: '申请人姓名',
                required: true,
                placeholder: '请输入姓名'
              },
              {
                id: 'department',
                type: 'select',
                label: '所属部门',
                required: true,
                options: [
                  { label: '研发部', value: 'rd' },
                  { label: '市场部', value: 'marketing' },
                  { label: '人事部', value: 'hr' }
                ]
              },
              {
                id: 'leaveType',
                type: 'radio',
                label: '请假类型',
                required: true,
                options: [
                  { label: '事假', value: 'personal' },
                  { label: '病假', value: 'sick' },
                  { label: '年假', value: 'annual' },
                  { label: '其他', value: 'other' }
                ]
              },
              {
                id: 'startDate',
                type: 'date',
                label: '开始日期',
                required: true
              },
              {
                id: 'endDate',
                type: 'date',
                label: '结束日期',
                required: true
              },
              {
                id: 'reason',
                type: 'textarea',
                label: '请假原因',
                required: true,
                placeholder: '请详细说明请假原因'
              },
              {
                id: 'attachment',
                type: 'file',
                label: '附件',
                required: false,
                accept: '.pdf,.doc,.docx,.jpg,.png'
              }
            ]
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          title: '报销申请表',
          description: '用于员工报销申请',
          creator_id: adminId,
          version: 1,
          status: 'published',
          schema: JSON.stringify({
            fields: [
              {
                id: 'name',
                type: 'text',
                label: '申请人姓名',
                required: true,
                placeholder: '请输入姓名'
              },
              {
                id: 'department',
                type: 'select',
                label: '所属部门',
                required: true,
                options: [
                  { label: '研发部', value: 'rd' },
                  { label: '市场部', value: 'marketing' },
                  { label: '人事部', value: 'hr' }
                ]
              },
              {
                id: 'expenseDate',
                type: 'date',
                label: '费用发生日期',
                required: true
              },
              {
                id: 'expenseType',
                type: 'select',
                label: '费用类型',
                required: true,
                options: [
                  { label: '差旅费', value: 'travel' },
                  { label: '办公用品', value: 'office' },
                  { label: '业务招待', value: 'business' },
                  { label: '其他', value: 'other' }
                ]
              },
              {
                id: 'amount',
                type: 'number',
                label: '报销金额',
                required: true,
                placeholder: '请输入金额'
              },
              {
                id: 'description',
                type: 'textarea',
                label: '费用说明',
                required: true,
                placeholder: '请详细说明费用用途'
              },
              {
                id: 'receipt',
                type: 'file',
                label: '发票/收据',
                required: true,
                accept: '.pdf,.jpg,.png'
              }
            ]
          }),
          created_at: new Date(),
          updated_at: new Date()
        }
      ], {});
    }
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.bulkDelete('form_templates', null, {});
  }
};
