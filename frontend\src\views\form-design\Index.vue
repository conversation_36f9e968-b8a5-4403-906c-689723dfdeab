<template>
  <div class="form-design-container">
    <el-row :gutter="20" class="mb-4">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>表单设计器</span>
              <el-button
                type="primary"
                @click="handleCreateForm">
                创建表单
              </el-button>
            </div>
          </template>

          <el-table
            :data="forms"
            border
            v-loading="loading">
            <el-table-column prop="title" label="表单名称" width="180" />
            <el-table-column prop="description" label="描述" />
            <el-table-column label="创建者" width="120">
              <template #default="scope">
                {{ scope.row.creator ? scope.row.creator.full_name : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="version" label="版本" width="80" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="更新时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.updated_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="scope">
                <el-button
                  size="small"
                  @click="handleEditForm(scope.row)"
                  type="primary">
                  编辑
                </el-button>
                <el-button
                  size="small"
                  @click="handlePreviewForm(scope.row)"
                  type="success">
                  预览
                </el-button>
                <el-button
                  size="small"
                  @click="handleViewVersions(scope.row)"
                  type="info">
                  版本
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="handleDeleteForm(scope.row)"
                  v-if="canDelete(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 表单设计对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEdit ? '编辑表单' : '创建表单'"
      width="80%"
      fullscreen>
      <div class="form-designer">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="components-panel">
              <template #header>
                <div class="card-header">
                  <span>组件面板</span>
                </div>
              </template>
              <div class="components-list">
                <div
                  v-for="component in componentList"
                  :key="component.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="handleDragStart($event, component)">
                  <el-icon>
                    <component :is="component.icon"></component>
                  </el-icon>
                  <span>{{ component.label }}</span>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card class="design-panel">
              <template #header>
                <div class="card-header">
                  <span>设计面板</span>
                  <div>
                    <el-button @click="showFormPropertiesPanel" type="primary" plain size="small" style="margin-right: 10px">
                      表单属性
                    </el-button>
                    <el-button @click="handleClearDesign" type="danger" plain size="small">
                      清空
                    </el-button>
                  </div>
                </div>
              </template>

              <div
                class="form-canvas"
                @dragover="handleDragOver"
                @drop="handleDrop">
                <el-form
                  :model="formData"
                  :label-width="formInfo.labelWidth"
                  :label-position="formInfo.labelPosition"
                  :class="'form-layout-' + formInfo.layout">
                  <template v-if="formFields.length === 0">
                    <div class="empty-tip">
                      <el-empty description="拖拽左侧组件到此处进行表单设计" />
                    </div>
                  </template>

                  <div
                    v-for="(field, index) in formFields"
                    :key="index"
                    class="form-field-item"
                    :class="{ 'active': selectedFieldIndex === index }"
                    @click="selectField(index)">
                    <div class="field-actions">
                      <el-button-group>
                        <el-button
                          type="primary"
                          size="small"
                          icon="ArrowUp"
                          @click.stop="moveField(index, 'up')"
                          :disabled="index === 0"></el-button>
                        <el-button
                          type="primary"
                          size="small"
                          icon="ArrowDown"
                          @click.stop="moveField(index, 'down')"
                          :disabled="index === formFields.length - 1"></el-button>
                        <el-button
                          type="danger"
                          size="small"
                          icon="Delete"
                          @click.stop="removeField(index)"></el-button>
                      </el-button-group>
                    </div>

                    <el-form-item :label="field.label">
                      <!-- 文本输入框 -->
                      <el-input
                        v-if="field.field_type === 'input'"
                        v-model="formData[field.field_key]"
                        :placeholder="field.placeholder || '请输入' + field.label"></el-input>

                      <!-- 文本域 -->
                      <el-input
                        v-else-if="field.field_type === 'textarea'"
                        v-model="formData[field.field_key]"
                        type="textarea"
                        :rows="3"
                        :placeholder="field.placeholder || '请输入' + field.label"></el-input>

                      <!-- 数字输入框 -->
                      <el-input-number
                        v-else-if="field.field_type === 'number'"
                        v-model="formData[field.field_key]"
                        :placeholder="field.placeholder || '请输入' + field.label"></el-input-number>

                      <!-- 单选框组 -->
                      <el-radio-group
                        v-else-if="field.field_type === 'radio'"
                        v-model="formData[field.field_key]">
                        <el-radio
                          v-for="option in field.options"
                          :key="option.value"
                          :label="option.value">
                          {{ option.label }}
                        </el-radio>
                      </el-radio-group>

                      <!-- 复选框组 -->
                      <el-checkbox-group
                        v-else-if="field.field_type === 'checkbox'"
                        v-model="formData[field.field_key]">
                        <el-checkbox
                          v-for="option in field.options"
                          :key="option.value"
                          :label="option.value">
                          {{ option.label }}
                        </el-checkbox>
                      </el-checkbox-group>

                      <!-- 下拉选择框 -->
                      <el-select
                        v-else-if="field.field_type === 'select'"
                        v-model="formData[field.field_key]"
                        :placeholder="field.placeholder || '请选择' + field.label"
                        style="width: 100%">
                        <el-option
                          v-for="option in field.options"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"></el-option>
                      </el-select>

                      <!-- 日期选择器 -->
                      <el-date-picker
                        v-else-if="field.field_type === 'date'"
                        v-model="formData[field.field_key]"
                        type="date"
                        :placeholder="field.placeholder || '请选择日期'"
                        style="width: 100%"></el-date-picker>

                      <!-- 时间选择器 -->
                      <el-time-picker
                        v-else-if="field.field_type === 'time'"
                        v-model="formData[field.field_key]"
                        :placeholder="field.placeholder || '请选择时间'"
                        style="width: 100%"></el-time-picker>

                      <!-- 日期时间选择器 -->
                      <el-date-picker
                        v-else-if="field.field_type === 'datetime'"
                        v-model="formData[field.field_key]"
                        type="datetime"
                        :placeholder="field.placeholder || '请选择日期时间'"
                        style="width: 100%"></el-date-picker>

                      <!-- 开关 -->
                      <el-switch
                        v-else-if="field.field_type === 'switch'"
                        v-model="formData[field.field_key]"></el-switch>

                      <!-- 滑块 -->
                      <el-slider
                        v-else-if="field.field_type === 'slider'"
                        v-model="formData[field.field_key]"></el-slider>

                      <!-- 评分 -->
                      <el-rate
                        v-else-if="field.field_type === 'rate'"
                        v-model="formData[field.field_key]"></el-rate>
                    </el-form-item>
                  </div>

                  <!-- 表单按钮 -->
                  <div class="form-buttons" v-if="formFields.length > 0">
                    <el-form-item>
                      <el-button type="primary">{{ formInfo.submitButtonText }}</el-button>
                      <el-button v-if="formInfo.showResetButton">{{ formInfo.resetButtonText }}</el-button>
                      <el-button v-if="formInfo.showCancelButton">{{ formInfo.cancelButtonText }}</el-button>
                    </el-form-item>
                  </div>
                </el-form>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="properties-panel">
              <template #header>
                <div class="card-header">
                  <span>属性面板</span>
                  <div v-if="selectedFieldIndex !== -1">
                    <el-radio-group v-model="showFormProperties" size="small">
                      <el-radio-button :label="true">表单属性</el-radio-button>
                      <el-radio-button :label="false">字段属性</el-radio-button>
                    </el-radio-group>
                  </div>
                </div>
              </template>

              <div v-if="showFormProperties">
                <el-tabs>
                  <el-tab-pane label="基本信息">
                    <el-form label-width="100px">
                      <el-form-item label="表单标题">
                        <el-input v-model="formInfo.title" placeholder="请输入表单标题"></el-input>
                      </el-form-item>
                      <el-form-item label="表单描述">
                        <el-input
                          v-model="formInfo.description"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入表单描述"></el-input>
                      </el-form-item>
                      <el-form-item label="表单状态">
                        <el-select v-model="formInfo.status" style="width: 100%">
                          <el-option label="草稿" value="draft"></el-option>
                          <el-option label="已发布" value="published"></el-option>
                          <el-option label="已禁用" value="disabled"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-form>
                  </el-tab-pane>

                  <el-tab-pane label="布局设置">
                    <el-form label-width="100px">
                      <el-form-item label="表单布局">
                        <el-tooltip content="选择表单的整体布局方式" placement="top">
                          <el-radio-group v-model="formInfo.layout">
                            <el-radio-button label="vertical">垂直布局</el-radio-button>
                            <el-radio-button label="horizontal">水平布局</el-radio-button>
                            <el-radio-button label="inline">行内布局</el-radio-button>
                          </el-radio-group>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="标签宽度">
                        <el-tooltip content="设置表单标签的宽度，例如：120px" placement="top">
                          <el-input v-model="formInfo.labelWidth" placeholder="例如：120px"></el-input>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="标签位置">
                        <el-tooltip content="设置表单标签的对齐方式" placement="top">
                          <el-radio-group v-model="formInfo.labelPosition">
                            <el-radio-button label="left">左对齐</el-radio-button>
                            <el-radio-button label="right">右对齐</el-radio-button>
                            <el-radio-button label="top">顶部对齐</el-radio-button>
                          </el-radio-group>
                        </el-tooltip>
                      </el-form-item>
                    </el-form>
                  </el-tab-pane>

                  <el-tab-pane label="提交设置">
                    <el-form label-width="100px">
                      <el-form-item label="提交方式">
                        <el-tooltip content="设置表单提交的HTTP方法" placement="top">
                          <el-select v-model="formInfo.submitMethod" style="width: 100%">
                            <el-option label="POST" value="post"></el-option>
                            <el-option label="GET" value="get"></el-option>
                            <el-option label="PUT" value="put"></el-option>
                            <el-option label="DELETE" value="delete"></el-option>
                          </el-select>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="提交地址">
                        <el-tooltip content="设置表单提交的目标URL" placement="top">
                          <el-input v-model="formInfo.submitUrl" placeholder="请输入表单提交地址"></el-input>
                        </el-tooltip>
                      </el-form-item>
                      <el-divider>按钮设置</el-divider>
                      <el-form-item label="提交按钮">
                        <el-tooltip content="设置提交按钮的显示文本" placement="top">
                          <el-input v-model="formInfo.submitButtonText" placeholder="提交按钮文本"></el-input>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="显示重置">
                        <el-tooltip content="是否显示重置按钮" placement="top">
                          <el-switch v-model="formInfo.showResetButton"></el-switch>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="重置按钮" v-if="formInfo.showResetButton">
                        <el-tooltip content="设置重置按钮的显示文本" placement="top">
                          <el-input v-model="formInfo.resetButtonText" placeholder="重置按钮文本"></el-input>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="显示取消">
                        <el-tooltip content="是否显示取消按钮" placement="top">
                          <el-switch v-model="formInfo.showCancelButton"></el-switch>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="取消按钮" v-if="formInfo.showCancelButton">
                        <el-tooltip content="设置取消按钮的显示文本" placement="top">
                          <el-input v-model="formInfo.cancelButtonText" placeholder="取消按钮文本"></el-input>
                        </el-tooltip>
                      </el-form-item>
                    </el-form>
                  </el-tab-pane>
                </el-tabs>
              </div>

              <div v-else-if="selectedField && !showFormProperties">
                <el-form label-width="100px">
                  <el-form-item label="字段标签">
                    <el-input v-model="selectedField.label" placeholder="请输入字段标签"></el-input>
                  </el-form-item>
                  <el-form-item label="字段键名">
                    <el-input v-model="selectedField.field_key" placeholder="请输入字段键名"></el-input>
                  </el-form-item>
                  <el-form-item label="占位文本" v-if="['input', 'textarea', 'select', 'date', 'time', 'datetime'].includes(selectedField.field_type)">
                    <el-input v-model="selectedField.placeholder" placeholder="请输入占位文本"></el-input>
                  </el-form-item>
                  <el-form-item label="默认值">
                    <el-input v-model="selectedField.default_value" placeholder="请输入默认值"></el-input>
                  </el-form-item>
                  <el-form-item label="是否必填">
                    <el-switch v-model="selectedField.is_required"></el-switch>
                  </el-form-item>

                  <!-- 选项配置（单选、多选、下拉） -->
                  <template v-if="['radio', 'checkbox', 'select'].includes(selectedField.field_type)">
                    <el-divider>选项配置</el-divider>
                    <div
                      v-for="(option, optIndex) in selectedField.options"
                      :key="optIndex"
                      class="option-item">
                      <el-row :gutter="10">
                        <el-col :span="10">
                          <el-input v-model="option.label" placeholder="选项标签"></el-input>
                        </el-col>
                        <el-col :span="10">
                          <el-input v-model="option.value" placeholder="选项值"></el-input>
                        </el-col>
                        <el-col :span="4">
                          <el-button
                            type="danger"
                            icon="Delete"
                            circle
                            @click="removeOption(optIndex)"></el-button>
                        </el-col>
                      </el-row>
                    </div>
                    <el-button
                      type="primary"
                      icon="Plus"
                      @click="addOption"
                      style="margin-top: 10px">
                      添加选项
                    </el-button>
                  </template>

                  <!-- 验证规则配置 -->
                  <el-divider>验证规则</el-divider>
                  <el-form-item label="验证规则" v-if="['input', 'textarea', 'number'].includes(selectedField.field_type)">
                    <el-select
                      v-model="selectedField.validation_rules.type"
                      placeholder="请选择验证类型"
                      style="width: 100%">
                      <el-option label="无" value=""></el-option>
                      <el-option label="邮箱" value="email"></el-option>
                      <el-option label="手机号" value="phone"></el-option>
                      <el-option label="URL" value="url"></el-option>
                      <el-option label="数字" value="number"></el-option>
                      <el-option label="整数" value="integer"></el-option>
                      <el-option label="正则表达式" value="regexp"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="正则表达式" v-if="selectedField.validation_rules && selectedField.validation_rules.type === 'regexp'">
                    <el-input v-model="selectedField.validation_rules.pattern" placeholder="请输入正则表达式"></el-input>
                  </el-form-item>
                  <el-form-item label="错误提示" v-if="selectedField.validation_rules && selectedField.validation_rules.type">
                    <el-input v-model="selectedField.validation_rules.message" placeholder="请输入错误提示"></el-input>
                  </el-form-item>
                </el-form>
              </div>
              <div v-else-if="!showFormProperties && !selectedField">
                <el-empty description="请选择一个字段进行配置">
                  <template #extra>
                    <el-button type="primary" @click="showFormPropertiesPanel">
                      返回表单属性
                    </el-button>
                  </template>
                </el-empty>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveForm" :loading="saving">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 表单预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="表单预览"
      width="60%">
      <div v-if="previewForm">
        <h2>{{ previewForm.title }}</h2>
        <p v-if="previewForm.description">{{ previewForm.description }}</p>

        <el-form
          :model="previewData"
          :label-width="previewForm.schema?.formProps?.labelWidth || '120px'"
          :label-position="previewForm.schema?.formProps?.labelPosition || 'right'"
          :class="'form-layout-' + (previewForm.schema?.formProps?.layout || 'vertical')">
          <template v-for="field in previewFields" :key="field.field_key">
            <el-form-item
              :label="field.label"
              :prop="field.field_key"
              :rules="getFieldRules(field)">

              <!-- 文本输入框 -->
              <el-input
                v-if="field.field_type === 'input'"
                v-model="previewData[field.field_key]"
                :placeholder="field.placeholder || '请输入' + field.label"></el-input>

              <!-- 文本域 -->
              <el-input
                v-else-if="field.field_type === 'textarea'"
                v-model="previewData[field.field_key]"
                type="textarea"
                :rows="3"
                :placeholder="field.placeholder || '请输入' + field.label"></el-input>

              <!-- 数字输入框 -->
              <el-input-number
                v-else-if="field.field_type === 'number'"
                v-model="previewData[field.field_key]"
                :placeholder="field.placeholder || '请输入' + field.label"></el-input-number>

              <!-- 单选框组 -->
              <el-radio-group
                v-else-if="field.field_type === 'radio'"
                v-model="previewData[field.field_key]">
                <el-radio
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.value">
                  {{ option.label }}
                </el-radio>
              </el-radio-group>

              <!-- 复选框组 -->
              <el-checkbox-group
                v-else-if="field.field_type === 'checkbox'"
                v-model="previewData[field.field_key]">
                <el-checkbox
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.value">
                  {{ option.label }}
                </el-checkbox>
              </el-checkbox-group>

              <!-- 下拉选择框 -->
              <el-select
                v-else-if="field.field_type === 'select'"
                v-model="previewData[field.field_key]"
                :placeholder="field.placeholder || '请选择' + field.label"
                style="width: 100%">
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"></el-option>
              </el-select>

              <!-- 日期选择器 -->
              <el-date-picker
                v-else-if="field.field_type === 'date'"
                v-model="previewData[field.field_key]"
                type="date"
                :placeholder="field.placeholder || '请选择日期'"
                style="width: 100%"></el-date-picker>

              <!-- 时间选择器 -->
              <el-time-picker
                v-else-if="field.field_type === 'time'"
                v-model="previewData[field.field_key]"
                :placeholder="field.placeholder || '请选择时间'"
                style="width: 100%"></el-time-picker>

              <!-- 日期时间选择器 -->
              <el-date-picker
                v-else-if="field.field_type === 'datetime'"
                v-model="previewData[field.field_key]"
                type="datetime"
                :placeholder="field.placeholder || '请选择日期时间'"
                style="width: 100%"></el-date-picker>

              <!-- 开关 -->
              <el-switch
                v-else-if="field.field_type === 'switch'"
                v-model="previewData[field.field_key]"></el-switch>

              <!-- 滑块 -->
              <el-slider
                v-else-if="field.field_type === 'slider'"
                v-model="previewData[field.field_key]"></el-slider>

              <!-- 评分 -->
              <el-rate
                v-else-if="field.field_type === 'rate'"
                v-model="previewData[field.field_key]"></el-rate>
            </el-form-item>
          </template>

          <!-- 表单按钮 -->
          <div class="form-buttons">
            <el-form-item>
              <el-button type="primary">
                {{ previewForm.schema?.formProps?.submitButtonText || '提交' }}
              </el-button>
              <el-button v-if="previewForm.schema?.formProps?.showResetButton !== false">
                {{ previewForm.schema?.formProps?.resetButtonText || '重置' }}
              </el-button>
              <el-button v-if="previewForm.schema?.formProps?.showCancelButton !== false">
                {{ previewForm.schema?.formProps?.cancelButtonText || '取消' }}
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-dialog>

    <!-- 表单版本对话框 -->
    <el-dialog
      v-model="versionsDialogVisible"
      title="表单版本历史"
      width="60%">
      <el-table :data="formVersions" border v-loading="versionsLoading">
        <el-table-column prop="version" label="版本号" width="100" />
        <el-table-column label="创建者" width="120">
          <template #default="scope">
            {{ scope.row.creator ? scope.row.creator.full_name : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button
              size="small"
              @click="handlePreviewVersion(scope.row)"
              type="primary">
              预览
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formApi } from '@/api'
import {
  Document, Edit, Switch, Calendar, Timer, Select,
  Check, Odometer, Histogram, Star, Menu
} from '@element-plus/icons-vue'

// 数据
const forms = ref([])
const formVersions = ref([])
const loading = ref(false)
const versionsLoading = ref(false)
const saving = ref(false)
const formDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const versionsDialogVisible = ref(false)
const isEdit = ref(false)
const currentFormId = ref(null)
const selectedFieldIndex = ref(-1)
const previewForm = ref(null)
const previewFields = ref([])
const previewData = ref({})
const showFormProperties = ref(true) // 控制是否显示表单属性

// 表单信息
const formInfo = reactive({
  title: '',
  description: '',
  status: 'draft',
  layout: 'vertical',
  labelWidth: '120px',
  labelPosition: 'right',
  submitMethod: 'post',
  submitUrl: '',
  showResetButton: true,
  showCancelButton: true,
  submitButtonText: '提交',
  resetButtonText: '重置',
  cancelButtonText: '取消'
})

// 表单字段
const formFields = ref([])

// 表单数据
const formData = reactive({})

// 可用组件列表
const componentList = [
  { type: 'input', label: '单行文本', icon: 'Document' },
  { type: 'textarea', label: '多行文本', icon: 'Document' },
  { type: 'number', label: '数字输入', icon: 'Odometer' },
  { type: 'radio', label: '单选框组', icon: 'Check' },
  { type: 'checkbox', label: '复选框组', icon: 'Menu' },
  { type: 'select', label: '下拉选择', icon: 'Select' },
  { type: 'date', label: '日期选择', icon: 'Calendar' },
  { type: 'time', label: '时间选择', icon: 'Timer' },
  { type: 'datetime', label: '日期时间', icon: 'Calendar' },
  { type: 'switch', label: '开关', icon: 'Switch' },
  { type: 'slider', label: '滑块', icon: 'Histogram' },
  { type: 'rate', label: '评分', icon: 'Star' },
  { type: 'file', label: '文件上传', icon: 'Upload' }
]

// 计算属性
const selectedField = computed(() => {
  if (selectedFieldIndex.value >= 0 && selectedFieldIndex.value < formFields.value.length) {
    return formFields.value[selectedFieldIndex.value]
  }
  return null
})

// 初始化
onMounted(async () => {
  await fetchForms()
})

// 获取表单列表
const fetchForms = async () => {
  loading.value = true
  try {
    const result = await formApi.getAllForms()
    if (result.success) {
      forms.value = result.data
    }
  } catch (error) {
    console.error('获取表单列表失败:', error)
    ElMessage.error('获取表单列表失败')
  } finally {
    loading.value = false
  }
}

// 获取表单版本历史
const fetchFormVersions = async (formId) => {
  versionsLoading.value = true
  try {
    const result = await formApi.getFormVersions(formId)
    if (result.success) {
      formVersions.value = result.data
    }
  } catch (error) {
    console.error('获取表单版本历史失败:', error)
    ElMessage.error('获取表单版本历史失败')
  } finally {
    versionsLoading.value = false
  }
}

// 创建表单
const handleCreateForm = () => {
  resetFormDesigner()
  isEdit.value = false
  currentFormId.value = null
  formDialogVisible.value = true
}

// 编辑表单
const handleEditForm = async (form) => {
  resetFormDesigner()
  isEdit.value = true
  currentFormId.value = form.id

  try {
    const result = await formApi.getFormById(form.id)
    if (result.success) {
      const formData = result.data

      // 设置表单基本信息
      formInfo.title = formData.title
      formInfo.description = formData.description || ''
      formInfo.status = formData.status

      // 设置表单高级属性（如果存在）
      if (formData.schema && formData.schema.formProps) {
        formInfo.layout = formData.schema.formProps.layout || 'vertical'
        formInfo.labelWidth = formData.schema.formProps.labelWidth || '120px'
        formInfo.labelPosition = formData.schema.formProps.labelPosition || 'right'
        formInfo.submitMethod = formData.schema.formProps.submitMethod || 'post'
        formInfo.submitUrl = formData.schema.formProps.submitUrl || ''
        formInfo.showResetButton = formData.schema.formProps.showResetButton !== undefined ?
          formData.schema.formProps.showResetButton : true
        formInfo.showCancelButton = formData.schema.formProps.showCancelButton !== undefined ?
          formData.schema.formProps.showCancelButton : true
        formInfo.submitButtonText = formData.schema.formProps.submitButtonText || '提交'
        formInfo.resetButtonText = formData.schema.formProps.resetButtonText || '重置'
        formInfo.cancelButtonText = formData.schema.formProps.cancelButtonText || '取消'
      }

      // 设置表单字段
      if (formData.fields && formData.fields.length > 0) {
        formFields.value = formData.fields.map(field => {
          // 确保验证规则对象存在
          if (!field.validation_rules) {
            field.validation_rules = {}
          }

          // 确保选项数组存在
          if (['radio', 'checkbox', 'select'].includes(field.field_type) && !field.options) {
            field.options = [{ label: '选项1', value: '1' }]
          }

          return field
        }).sort((a, b) => a.order_index - b.order_index)
      }

      formDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取表单详情失败:', error)
    ElMessage.error('获取表单详情失败')
  }
}

// 预览表单
const handlePreviewForm = async (form) => {
  try {
    const result = await formApi.previewForm(form.id)
    if (result.success) {
      previewForm.value = result.data
      previewFields.value = result.data.fields || []

      // 确保schema存在
      if (!previewForm.value.schema) {
        previewForm.value.schema = {}
      }

      // 确保formProps存在
      if (!previewForm.value.schema.formProps) {
        previewForm.value.schema.formProps = {
          layout: 'vertical',
          labelWidth: '120px',
          labelPosition: 'right',
          submitButtonText: '提交',
          resetButtonText: '重置',
          cancelButtonText: '取消',
          showResetButton: true,
          showCancelButton: true
        }
      }

      // 初始化预览数据
      previewData.value = {}
      previewFields.value.forEach(field => {
        if (field.default_value) {
          previewData.value[field.field_key] = field.default_value
        } else if (field.field_type === 'checkbox') {
          previewData.value[field.field_key] = []
        } else {
          previewData.value[field.field_key] = undefined
        }
      })

      previewDialogVisible.value = true
    }
  } catch (error) {
    console.error('预览表单失败:', error)
    ElMessage.error('预览表单失败')
  }
}

// 预览表单版本
const handlePreviewVersion = (version) => {
  // 解析版本的schema
  const schema = version.schema

  if (currentForm.value) {
    previewForm.value = {
      title: `${currentForm.value.title} (版本 ${version.version})`,
      description: currentForm.value.description,
      schema: schema || {}
    }

    // 确保formProps存在
    if (!previewForm.value.schema.formProps) {
      previewForm.value.schema.formProps = {
        layout: 'vertical',
        labelWidth: '120px',
        labelPosition: 'right',
        submitButtonText: '提交',
        resetButtonText: '重置',
        cancelButtonText: '取消',
        showResetButton: true,
        showCancelButton: true
      }
    }

    // 这里需要根据实际的schema结构来设置预览字段
    // 假设schema中包含fields数组
    if (schema && schema.fields) {
      previewFields.value = schema.fields

      // 初始化预览数据
      previewData.value = {}
      previewFields.value.forEach(field => {
        if (field.default_value) {
          previewData.value[field.field_key] = field.default_value
        } else if (field.field_type === 'checkbox') {
          previewData.value[field.field_key] = []
        } else {
          previewData.value[field.field_key] = undefined
        }
      })

      previewDialogVisible.value = true
    }
  }
}

// 查看表单版本历史
const handleViewVersions = (form) => {
  currentFormId.value = form.id
  currentForm.value = form
  fetchFormVersions(form.id)
  versionsDialogVisible.value = true
}

// 删除表单
const handleDeleteForm = (form) => {
  ElMessageBox.confirm(
    '此操作将永久删除该表单，是否继续?',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const result = await formApi.deleteForm(form.id)
      if (result.success) {
        ElMessage.success('删除成功')
        fetchForms()
      } else {
        ElMessage.error(result.message || '删除失败')
      }
    } catch (error) {
      console.error('删除表单失败:', error)
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 拖拽开始
const handleDragStart = (event, component) => {
  event.dataTransfer.setData('componentType', component.type)
}

// 拖拽悬停
const handleDragOver = (event) => {
  event.preventDefault()
}

// 拖拽放置
const handleDrop = (event) => {
  event.preventDefault()
  const componentType = event.dataTransfer.getData('componentType')

  if (componentType) {
    // 生成唯一字段键名
    const fieldKey = `field_${Date.now()}_${Math.floor(Math.random() * 1000)}`

    // 创建新字段
    const newField = {
      field_key: fieldKey,
      field_type: componentType,
      label: getDefaultLabel(componentType),
      placeholder: '',
      default_value: '',
      is_required: false,
      order_index: formFields.value.length,
      validation_rules: {}
    }

    // 为选择类型的字段添加默认选项
    if (['radio', 'checkbox', 'select'].includes(componentType)) {
      newField.options = [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' }
      ]
    }

    // 添加到字段列表
    formFields.value.push(newField)

    // 选中新添加的字段
    selectField(formFields.value.length - 1)
  }
}

// 选择字段
const selectField = (index) => {
  selectedFieldIndex.value = index
  showFormProperties.value = false
}

// 切换到表单属性
const showFormPropertiesPanel = () => {
  showFormProperties.value = true
}

// 切换到字段属性
const showFieldPropertiesPanel = () => {
  showFormProperties.value = false
}

// 移动字段
const moveField = (index, direction) => {
  if (direction === 'up' && index > 0) {
    // 向上移动
    const temp = formFields.value[index]
    formFields.value[index] = formFields.value[index - 1]
    formFields.value[index - 1] = temp

    // 更新排序索引
    formFields.value[index].order_index = index
    formFields.value[index - 1].order_index = index - 1

    // 更新选中的字段索引
    selectedFieldIndex.value = index - 1
  } else if (direction === 'down' && index < formFields.value.length - 1) {
    // 向下移动
    const temp = formFields.value[index]
    formFields.value[index] = formFields.value[index + 1]
    formFields.value[index + 1] = temp

    // 更新排序索引
    formFields.value[index].order_index = index
    formFields.value[index + 1].order_index = index + 1

    // 更新选中的字段索引
    selectedFieldIndex.value = index + 1
  }
}

// 移除字段
const removeField = (index) => {
  formFields.value.splice(index, 1)

  // 更新剩余字段的排序索引
  formFields.value.forEach((field, idx) => {
    field.order_index = idx
  })

  // 如果删除的是当前选中的字段，则取消选中
  if (selectedFieldIndex.value === index) {
    selectedFieldIndex.value = -1
  } else if (selectedFieldIndex.value > index) {
    // 如果删除的字段在当前选中字段之前，则更新选中索引
    selectedFieldIndex.value--
  }
}

// 添加选项
const addOption = () => {
  if (selectedField.value && ['radio', 'checkbox', 'select'].includes(selectedField.value.field_type)) {
    if (!selectedField.value.options) {
      selectedField.value.options = []
    }

    const newOptionIndex = selectedField.value.options.length + 1
    selectedField.value.options.push({
      label: `选项${newOptionIndex}`,
      value: `${newOptionIndex}`
    })
  }
}

// 移除选项
const removeOption = (index) => {
  if (selectedField.value && selectedField.value.options) {
    selectedField.value.options.splice(index, 1)
  }
}

// 清空设计
const handleClearDesign = () => {
  ElMessageBox.confirm(
    '确定要清空当前设计吗？此操作不可恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    formFields.value = []
    selectedFieldIndex.value = -1
  }).catch(() => {
    // 取消清空
  })
}

// 保存表单
const saveForm = async () => {
  // 验证表单信息
  if (!formInfo.title) {
    ElMessage.error('请输入表单标题')
    return
  }

  // 验证字段
  if (formFields.value.length === 0) {
    ElMessage.error('请至少添加一个字段')
    return
  }

  // 验证字段键名唯一性
  const fieldKeys = new Set()
  for (const field of formFields.value) {
    if (!field.field_key) {
      ElMessage.error(`请为 "${field.label}" 字段设置键名`)
      return
    }

    if (fieldKeys.has(field.field_key)) {
      ElMessage.error(`字段键名 "${field.field_key}" 重复，请修改`)
      return
    }

    fieldKeys.add(field.field_key)
  }

  saving.value = true
  try {
    // 构建表单数据
    const formData = {
      title: formInfo.title,
      description: formInfo.description,
      status: formInfo.status,
      schema: {
        fields: formFields.value,
        formProps: {
          layout: formInfo.layout,
          labelWidth: formInfo.labelWidth,
          labelPosition: formInfo.labelPosition,
          submitMethod: formInfo.submitMethod,
          submitUrl: formInfo.submitUrl,
          showResetButton: formInfo.showResetButton,
          showCancelButton: formInfo.showCancelButton,
          submitButtonText: formInfo.submitButtonText,
          resetButtonText: formInfo.resetButtonText,
          cancelButtonText: formInfo.cancelButtonText
        }
      },
      fields: formFields.value
    }

    let result
    if (isEdit.value && currentFormId.value) {
      // 更新表单
      result = await formApi.updateForm(currentFormId.value, formData)
    } else {
      // 创建表单
      result = await formApi.createForm(formData)
    }

    if (result.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      formDialogVisible.value = false
      fetchForms()
    } else {
      ElMessage.error(result.message || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error(isEdit.value ? '更新表单失败:' : '创建表单失败:', error)
    ElMessage.error(error.response?.data?.message || (isEdit.value ? '更新失败' : '创建失败'))
  } finally {
    saving.value = false
  }
}

// 重置表单设计器
const resetFormDesigner = () => {
  // 重置表单信息
  formInfo.title = ''
  formInfo.description = ''
  formInfo.status = 'draft'
  formInfo.layout = 'vertical'
  formInfo.labelWidth = '120px'
  formInfo.labelPosition = 'right'
  formInfo.submitMethod = 'post'
  formInfo.submitUrl = ''
  formInfo.showResetButton = true
  formInfo.showCancelButton = true
  formInfo.submitButtonText = '提交'
  formInfo.resetButtonText = '重置'
  formInfo.cancelButtonText = '取消'

  // 重置字段
  formFields.value = []
  selectedFieldIndex.value = -1

  // 重置属性面板状态
  showFormProperties.value = true

  // 重置表单数据
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
}

// 获取字段验证规则
const getFieldRules = (field) => {
  const rules = []

  // 必填验证
  if (field.is_required) {
    rules.push({
      required: true,
      message: `请${['select', 'date', 'time', 'datetime'].includes(field.field_type) ? '选择' : '输入'}${field.label}`,
      trigger: ['select', 'date', 'time', 'datetime', 'radio', 'checkbox'].includes(field.field_type) ? 'change' : 'blur'
    })
  }

  // 其他验证规则
  if (field.validation_rules && field.validation_rules.type) {
    const type = field.validation_rules.type
    const message = field.validation_rules.message || `${field.label}格式不正确`

    switch (type) {
      case 'email':
        rules.push({
          type: 'email',
          message,
          trigger: 'blur'
        })
        break
      case 'phone':
        rules.push({
          pattern: /^1[3-9]\d{9}$/,
          message,
          trigger: 'blur'
        })
        break
      case 'url':
        rules.push({
          type: 'url',
          message,
          trigger: 'blur'
        })
        break
      case 'number':
        rules.push({
          type: 'number',
          message,
          trigger: 'blur'
        })
        break
      case 'integer':
        rules.push({
          type: 'integer',
          message,
          trigger: 'blur'
        })
        break
      case 'regexp':
        if (field.validation_rules.pattern) {
          rules.push({
            pattern: new RegExp(field.validation_rules.pattern),
            message,
            trigger: 'blur'
          })
        }
        break
    }
  }

  return rules
}

// 获取默认标签
const getDefaultLabel = (type) => {
  const componentInfo = componentList.find(c => c.type === type)
  return componentInfo ? componentInfo.label : '未命名字段'
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'info'
    case 'disabled':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    case 'disabled':
      return '已禁用'
    default:
      return status
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString()
}

// 检查是否可以删除
const canDelete = (form) => {
  const user = JSON.parse(localStorage.getItem('user') || '{}')
  return user.roles && user.roles.includes('admin') || (form.creator && form.creator.id === user.id)
}

// 当前表单（用于版本预览）
const currentForm = ref(null)
</script>

<style scoped>
.form-design-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.form-designer {
  min-height: 600px;
}

.components-panel, .design-panel, .properties-panel {
  height: 100%;
}

.components-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: move;
  transition: all 0.3s;
}

.component-item:hover {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.form-canvas {
  min-height: 500px;
  padding: 20px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.form-field-item {
  position: relative;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px dashed transparent;
  border-radius: 4px;
}

.form-field-item:hover {
  border-color: #dcdfe6;
}

.form-field-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.field-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: none;
}

.form-field-item:hover .field-actions,
.form-field-item.active .field-actions {
  display: block;
}

.option-item {
  margin-bottom: 10px;
}

/* 表单布局样式 */
.form-layout-vertical {
  display: flex;
  flex-direction: column;
}

.form-layout-horizontal .el-form-item {
  display: flex;
  align-items: center;
}

.form-layout-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.form-layout-inline .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  flex-grow: 1;
}

.form-buttons {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px dashed #dcdfe6;
}

/* 属性面板样式 */
.properties-panel .el-tabs__content {
  padding: 10px 0;
}

.properties-panel .el-radio-button__inner {
  padding: 8px 12px;
}

.properties-panel .card-header .el-radio-group {
  margin-left: 10px;
}

.properties-panel .el-empty {
  padding: 20px 0;
}

.properties-panel .el-empty__description {
  margin-top: 10px;
}

.properties-panel .el-empty__bottom {
  margin-top: 20px;
}
</style>
