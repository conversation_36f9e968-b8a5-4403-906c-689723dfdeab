const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     DepartmentUser:
 *       type: object
 *       required:
 *         - department_id
 *         - user_id
 *       properties:
 *         id:
 *           type: integer
 *           description: 部门用户关联ID
 *           example: 1
 *         department_id:
 *           type: integer
 *           description: 部门ID
 *           example: 1
 *         user_id:
 *           type: integer
 *           description: 用户ID
 *           example: 1
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class DepartmentUser extends Model {
    static associate(models) {
      // 部门
      DepartmentUser.belongsTo(models.Department, {
        foreignKey: 'department_id',
        as: 'department'
      });

      // 用户
      DepartmentUser.belongsTo(models.User, {
        foreignKey: 'user_id',
        as: 'user'
      });
    }
  }

  DepartmentUser.init({
    department_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'departments',
        key: 'id'
      }
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'DepartmentUser',
    tableName: 'department_users',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return DepartmentUser;
};
