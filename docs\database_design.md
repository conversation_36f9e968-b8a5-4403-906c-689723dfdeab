# 工作流系统数据库设计

## 概述
本文档描述了工作流系统的数据库设计，包括各个表的结构、关系和索引设计。数据库采用PostgreSQL，通过Sequelize ORM进行管理。

## 表结构设计

### 用户与权限相关表

#### 1. users（用户表）
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(100) NOT NULL,
  email VARCHAR(100) NOT NULL UNIQUE,
  full_name VARCHAR(100) NOT NULL,
  avatar VARCHAR(255),
  phone VARCHAR(20),
  status VARCHAR(20) DEFAULT 'active',
  last_login TIMESTAMP,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. roles（角色表）
```sql
CREATE TABLE roles (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. permissions（权限表）
```sql
CREATE TABLE permissions (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  resource VARCHAR(50) NOT NULL,
  action VARCHAR(50) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(resource, action)
);
```

#### 4. role_permissions（角色权限关联表）
```sql
CREATE TABLE role_permissions (
  role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  permission_id INTEGER NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (role_id, permission_id)
);
```

#### 5. user_roles（用户角色关联表）
```sql
CREATE TABLE user_roles (
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (user_id, role_id)
);
```

### 部门管理相关表

#### 6. departments（部门表）
```sql
CREATE TABLE departments (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(50) UNIQUE,
  parent_id INTEGER REFERENCES departments(id) ON DELETE SET NULL,
  manager_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
  description TEXT,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 7. department_users（部门用户关联表）
```sql
CREATE TABLE department_users (
  department_id INTEGER NOT NULL REFERENCES departments(id) ON DELETE CASCADE,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  is_primary BOOLEAN DEFAULT false,
  position VARCHAR(100),
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (department_id, user_id)
);
```

### 表单设计器相关表

#### 8. form_templates（表单模板表）
```sql
CREATE TABLE form_templates (
  id SERIAL PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  description TEXT,
  creator_id INTEGER NOT NULL REFERENCES users(id),
  version INTEGER NOT NULL DEFAULT 1,
  status VARCHAR(20) DEFAULT 'draft',
  schema JSONB NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 9. form_template_versions（表单模板版本表）
```sql
CREATE TABLE form_template_versions (
  id SERIAL PRIMARY KEY,
  form_template_id INTEGER NOT NULL REFERENCES form_templates(id) ON DELETE CASCADE,
  version INTEGER NOT NULL,
  schema JSONB NOT NULL,
  creator_id INTEGER NOT NULL REFERENCES users(id),
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(form_template_id, version)
);
```

#### 10. form_fields（表单字段表）
```sql
CREATE TABLE form_fields (
  id SERIAL PRIMARY KEY,
  form_template_id INTEGER NOT NULL REFERENCES form_templates(id) ON DELETE CASCADE,
  field_key VARCHAR(50) NOT NULL,
  field_type VARCHAR(50) NOT NULL,
  label VARCHAR(100) NOT NULL,
  placeholder VARCHAR(100),
  default_value TEXT,
  options JSONB,
  validation_rules JSONB,
  is_required BOOLEAN DEFAULT false,
  order_index INTEGER NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(form_template_id, field_key)
);
```

### 工作流设计相关表

#### 11. workflow_templates（工作流模板表）
```sql
CREATE TABLE workflow_templates (
  id SERIAL PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  description TEXT,
  form_template_id INTEGER REFERENCES form_templates(id) ON DELETE SET NULL,
  creator_id INTEGER NOT NULL REFERENCES users(id),
  version INTEGER NOT NULL DEFAULT 1,
  status VARCHAR(20) DEFAULT 'draft',
  schema JSONB NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 12. workflow_template_versions（工作流模板版本表）
```sql
CREATE TABLE workflow_template_versions (
  id SERIAL PRIMARY KEY,
  workflow_template_id INTEGER NOT NULL REFERENCES workflow_templates(id) ON DELETE CASCADE,
  version INTEGER NOT NULL,
  schema JSONB NOT NULL,
  creator_id INTEGER NOT NULL REFERENCES users(id),
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(workflow_template_id, version)
);
```

#### 13. workflow_nodes（工作流节点表）
```sql
CREATE TABLE workflow_nodes (
  id SERIAL PRIMARY KEY,
  workflow_template_id INTEGER NOT NULL REFERENCES workflow_templates(id) ON DELETE CASCADE,
  node_key VARCHAR(50) NOT NULL,
  node_type VARCHAR(50) NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  config JSONB,
  position_x INTEGER,
  position_y INTEGER,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(workflow_template_id, node_key)
);
```

#### 14. workflow_transitions（工作流转换表）
```sql
CREATE TABLE workflow_transitions (
  id SERIAL PRIMARY KEY,
  workflow_template_id INTEGER NOT NULL REFERENCES workflow_templates(id) ON DELETE CASCADE,
  source_node_id INTEGER NOT NULL REFERENCES workflow_nodes(id) ON DELETE CASCADE,
  target_node_id INTEGER NOT NULL REFERENCES workflow_nodes(id) ON DELETE CASCADE,
  condition JSONB,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 工作流实例与流转相关表

#### 15. workflow_instances（工作流实例表）
```sql
CREATE TABLE workflow_instances (
  id SERIAL PRIMARY KEY,
  workflow_template_id INTEGER NOT NULL REFERENCES workflow_templates(id),
  title VARCHAR(200) NOT NULL,
  initiator_id INTEGER NOT NULL REFERENCES users(id),
  current_node_id INTEGER REFERENCES workflow_nodes(id),
  status VARCHAR(20) DEFAULT 'running',
  form_data JSONB,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP
);
```

#### 16. workflow_tasks（工作流任务表）
```sql
CREATE TABLE workflow_tasks (
  id SERIAL PRIMARY KEY,
  workflow_instance_id INTEGER NOT NULL REFERENCES workflow_instances(id) ON DELETE CASCADE,
  node_id INTEGER NOT NULL REFERENCES workflow_nodes(id),
  assignee_id INTEGER REFERENCES users(id),
  assignee_role_id INTEGER REFERENCES roles(id),
  assignee_department_id INTEGER REFERENCES departments(id),
  status VARCHAR(20) DEFAULT 'pending',
  due_date TIMESTAMP,
  priority VARCHAR(20) DEFAULT 'normal',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP
);
```

#### 17. workflow_task_histories（工作流任务历史表）
```sql
CREATE TABLE workflow_task_histories (
  id SERIAL PRIMARY KEY,
  workflow_instance_id INTEGER NOT NULL REFERENCES workflow_instances(id) ON DELETE CASCADE,
  task_id INTEGER REFERENCES workflow_tasks(id) ON DELETE SET NULL,
  node_id INTEGER NOT NULL REFERENCES workflow_nodes(id),
  operator_id INTEGER NOT NULL REFERENCES users(id),
  operation VARCHAR(50) NOT NULL,
  comments TEXT,
  form_data JSONB,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 18. workflow_attachments（工作流附件表）
```sql
CREATE TABLE workflow_attachments (
  id SERIAL PRIMARY KEY,
  workflow_instance_id INTEGER NOT NULL REFERENCES workflow_instances(id) ON DELETE CASCADE,
  task_history_id INTEGER REFERENCES workflow_task_histories(id) ON DELETE SET NULL,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(255) NOT NULL,
  file_size INTEGER NOT NULL,
  file_type VARCHAR(100) NOT NULL,
  uploader_id INTEGER NOT NULL REFERENCES users(id),
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

## 索引设计

为了提高查询性能，我们为以下字段创建索引：

```sql
-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);

-- 部门表索引
CREATE INDEX idx_departments_parent_id ON departments(parent_id);
CREATE INDEX idx_departments_manager_id ON departments(manager_id);
CREATE INDEX idx_departments_status ON departments(status);

-- 表单模板索引
CREATE INDEX idx_form_templates_creator_id ON form_templates(creator_id);
CREATE INDEX idx_form_templates_status ON form_templates(status);

-- 工作流模板索引
CREATE INDEX idx_workflow_templates_form_template_id ON workflow_templates(form_template_id);
CREATE INDEX idx_workflow_templates_creator_id ON workflow_templates(creator_id);
CREATE INDEX idx_workflow_templates_status ON workflow_templates(status);

-- 工作流实例索引
CREATE INDEX idx_workflow_instances_workflow_template_id ON workflow_instances(workflow_template_id);
CREATE INDEX idx_workflow_instances_initiator_id ON workflow_instances(initiator_id);
CREATE INDEX idx_workflow_instances_current_node_id ON workflow_instances(current_node_id);
CREATE INDEX idx_workflow_instances_status ON workflow_instances(status);

-- 工作流任务索引
CREATE INDEX idx_workflow_tasks_workflow_instance_id ON workflow_tasks(workflow_instance_id);
CREATE INDEX idx_workflow_tasks_node_id ON workflow_tasks(node_id);
CREATE INDEX idx_workflow_tasks_assignee_id ON workflow_tasks(assignee_id);
CREATE INDEX idx_workflow_tasks_assignee_role_id ON workflow_tasks(assignee_role_id);
CREATE INDEX idx_workflow_tasks_assignee_department_id ON workflow_tasks(assignee_department_id);
CREATE INDEX idx_workflow_tasks_status ON workflow_tasks(status);
```

## 表关系说明

1. **用户与角色**：多对多关系，通过user_roles表关联
2. **角色与权限**：多对多关系，通过role_permissions表关联
3. **部门与用户**：多对多关系，通过department_users表关联
4. **部门层级**：自引用关系，通过parent_id字段实现
5. **表单模板与字段**：一对多关系
6. **表单模板与版本**：一对多关系
7. **工作流模板与节点**：一对多关系
8. **工作流模板与版本**：一对多关系
9. **工作流模板与表单模板**：多对一关系
10. **工作流实例与任务**：一对多关系
11. **工作流实例与任务历史**：一对多关系
12. **工作流实例与附件**：一对多关系

## 数据类型说明

1. **JSONB**：用于存储复杂的结构化数据，如表单模板的schema、工作流节点的配置等
2. **TIMESTAMP**：用于记录时间信息
3. **VARCHAR**：用于存储字符串，长度根据实际需求设置
4. **TEXT**：用于存储长文本，如描述信息
5. **INTEGER**：用于存储整数，如ID、版本号等
6. **BOOLEAN**：用于存储布尔值，如是否必填等
