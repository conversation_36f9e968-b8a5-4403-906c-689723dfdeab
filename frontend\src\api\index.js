import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response && error.response.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 用户相关API
export const userApi = {
  login(data) {
    // 直接使用axios而不是api实例，因为登录不需要token
    return axios.post('/api/users/login', data)
  },
  register(data) {
    return api.post('/users/register', data)
  },
  getCurrentUser() {
    return api.get('/users/me')
  },
  getAllUsers() {
    return api.get('/users')
  },
  getUserById(id) {
    return api.get(`/users/${id}`)
  },
  updateUser(id, data) {
    return api.put(`/users/${id}`, data)
  },
  deleteUser(id) {
    return api.delete(`/users/${id}`)
  },
  changePassword(id, data) {
    return api.put(`/users/${id}/password`, data)
  },
  resetPassword(id, data) {
    return api.post(`/users/${id}/reset-password`, data)
  },
  assignRoles(id, data) {
    return api.post(`/users/${id}/roles`, data)
  }
}

// 部门相关API
export const departmentApi = {
  getAllDepartments() {
    return api.get('/departments')
  },
  getDepartmentById(id) {
    return api.get(`/departments/${id}`)
  },
  createDepartment(data) {
    return api.post('/departments', data)
  },
  updateDepartment(id, data) {
    return api.put(`/departments/${id}`, data)
  },
  deleteDepartment(id) {
    return api.delete(`/departments/${id}`)
  },
  getDepartmentMembers(id) {
    return api.get(`/departments/${id}/members`)
  },
  addDepartmentMember(id, data) {
    return api.post(`/departments/${id}/members`, data)
  },
  updateDepartmentMember(id, userId, data) {
    return api.put(`/departments/${id}/members/${userId}`, data)
  },
  removeDepartmentMember(id, userId) {
    return api.delete(`/departments/${id}/members/${userId}`)
  }
}

// 表单相关API
export const formApi = {
  getAllForms() {
    return api.get('/forms')
  },
  getFormById(id) {
    return api.get(`/forms/${id}`)
  },
  createForm(data) {
    return api.post('/forms', data)
  },
  updateForm(id, data) {
    return api.put(`/forms/${id}`, data)
  },
  deleteForm(id) {
    return api.delete(`/forms/${id}`)
  },
  getFormVersions(id) {
    return api.get(`/forms/${id}/versions`)
  },
  previewForm(id) {
    return api.get(`/forms/${id}/preview`)
  }
}

// 工作流相关API
export const workflowApi = {
  getAllWorkflows() {
    return api.get('/workflows')
  },
  getWorkflowById(id) {
    return api.get(`/workflows/${id}`)
  },
  createWorkflow(data) {
    return api.post('/workflows', data)
  },
  updateWorkflow(id, data) {
    return api.put(`/workflows/${id}`, data)
  },
  deleteWorkflow(id) {
    return api.delete(`/workflows/${id}`)
  },
  getWorkflowVersions(id) {
    return api.get(`/workflows/${id}/versions`)
  },
  previewWorkflow(id) {
    return api.get(`/workflows/${id}/preview`)
  },
  startWorkflowInstance(id, data) {
    // 添加错误处理，如果服务器返回500错误，尝试重新提交但跳过任务历史记录
    return api.post(`/workflows/${id}/instances`, data)
      .catch(error => {
        console.error('启动工作流实例时发生错误:', error);

        // 如果是任务历史记录错误，尝试使用修改后的数据重新提交
        if (error.response &&
            error.response.status === 500 &&
            error.response.data &&
            error.response.data.error &&
            (error.response.data.error.includes('WorkflowTaskHistory') ||
             error.response.data.error.includes('task_id cannot be null'))) {

          console.log('检测到任务历史记录错误，尝试使用修改后的数据重新提交');

          // 添加标志，告诉后端跳过任务历史记录创建
          const modifiedData = {
            ...data,
            skip_task_history: true
          };

          console.log('使用修改后的数据重新提交:', {
            workflowId: id,
            skipTaskHistory: true,
            hasFormData: !!modifiedData.form_data
          });

          // 重新提交请求
          return api.post(`/workflows/${id}/instances`, modifiedData)
            .catch(retryError => {
              console.error('重试启动工作流实例失败:', retryError);
              throw retryError;
            });
        }

        // 其他错误直接抛出
        throw error;
      })
  },
  getWorkflowInstance(instanceId) {
    if (!instanceId) {
      console.error('获取工作流实例错误: 缺少实例ID');
      return Promise.reject(new Error('缺少工作流实例ID'));
    }
    
    // 尝试将非数字ID转换为数字
    let parsedId = instanceId;
    if (typeof instanceId === 'string') {
      const parsed = parseInt(instanceId, 10);
      if (!isNaN(parsed)) {
        parsedId = parsed;
      }
    }
    
    console.log(`开始获取工作流实例, ID(${typeof parsedId}): ${parsedId}`);
    
    return api.get(`/workflows/instances/${parsedId}`)
      .then(response => {
        console.log('获取工作流实例成功:', response);
        
        // 验证返回的数据结构
        if (!response || typeof response !== 'object') {
          console.error('API返回格式无效:', response);
          return {
            success: false,
            message: 'API返回格式无效',
            data: null
          };
        }
        
        // 规范化响应格式
        if (response.success === undefined) {
          console.log('规范化API响应格式');
          return {
            success: true,
            data: response
          };
        }
        
        return response;
      })
      .catch(error => {
        console.error('获取工作流实例错误:', error);
        
        // 输出更详细的错误信息以便调试
        if (error.response) {
          console.error('HTTP状态码:', error.response.status);
          console.error('错误详情:', error.response.data);
          console.error('请求头:', error.response.headers);
        } else if (error.request) {
          console.error('请求已发送但没有收到响应');
          console.error('请求详情:', error.request);
        } else {
          console.error('请求配置出错:', error.config);
        }
        
        // 规范化错误响应
        const errorResponse = {
          success: false,
          message: '获取工作流实例失败: ' + (error.response?.data?.message || error.message || '未知错误'),
          error: error
        };
        
        return Promise.reject(errorResponse);
      });
  },
  handleWorkflowTask(instanceId, taskId, data) {
    return api.post(`/workflows/instances/${instanceId}/tasks/${taskId}`, data)
      .catch(error => {
        console.error('处理工作流任务时发生错误:', error);

        // 如果是任务历史记录错误，尝试使用修改后的数据重新提交
        if (error.response &&
            error.response.status === 500 &&
            error.response.data &&
            error.response.data.error &&
            (error.response.data.error.includes('WorkflowTaskHistory') ||
             error.response.data.error.includes('task_id cannot be null'))) {

          console.log('检测到任务历史记录错误，尝试使用修改后的数据重新提交');

          // 添加标志，告诉后端跳过任务历史记录创建
          const modifiedData = {
            ...data,
            skip_task_history: true
          };

          console.log('使用修改后的数据重新提交:', {
            instanceId,
            taskId,
            skipTaskHistory: true
          });

          // 重新提交请求
          return api.post(`/workflows/instances/${instanceId}/tasks/${taskId}`, modifiedData)
            .catch(retryError => {
              console.error('重试处理工作流任务失败:', retryError);
              throw retryError;
            });
        }

        // 其他错误直接抛出
        throw error;
      })
  },
  getUserTodoTasks() {
    return api.get('/workflows/tasks/todo')
  },
  getUserDoneTasks() {
    return api.get('/workflows/tasks/done')
  },
  getUserInitiatedInstances() {
    return api.get('/workflows/instances/initiated')
  },

  // 工作流附件
  uploadAttachment(instanceId, formData) {
    return api.post(`/workflows/instances/${instanceId}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  getAttachments(instanceId) {
    return api.get(`/workflows/instances/${instanceId}/attachments`)
  },
  deleteAttachment(instanceId, attachmentId) {
    return api.delete(`/workflows/instances/${instanceId}/attachments/${attachmentId}`)
  },
  downloadAttachment(instanceId, attachmentId) {
    return api.get(`/workflows/instances/${instanceId}/attachments/${attachmentId}/download`, {
      responseType: 'blob'
    })
  }
}

// 角色相关API
export const roleApi = {
  getAllRoles() {
    return api.get('/roles')
  },
  getRoleById(id) {
    return api.get(`/roles/${id}`)
  },
  createRole(data) {
    return api.post('/roles', data)
  },
  updateRole(id, data) {
    return api.put(`/roles/${id}`, data)
  },
  deleteRole(id) {
    return api.delete(`/roles/${id}`)
  }
}

export default api
