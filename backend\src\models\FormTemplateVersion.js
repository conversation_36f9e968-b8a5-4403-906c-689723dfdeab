const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class FormTemplateVersion extends Model {
    static associate(models) {
      // 表单模板
      FormTemplateVersion.belongsTo(models.FormTemplate, {
        foreignKey: 'form_template_id',
        as: 'formTemplate'
      });

      // 创建者
      FormTemplateVersion.belongsTo(models.User, {
        foreignKey: 'creator_id',
        as: 'creator'
      });
    }
  }

  FormTemplateVersion.init({
    form_template_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'form_templates',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    version: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    schema: {
      type: DataTypes.JSONB,
      allowNull: false
    },
    creator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'FormTemplateVersion',
    tableName: 'form_template_versions',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
    indexes: [
      {
        unique: true,
        fields: ['form_template_id', 'version']
      }
    ]
  });

  return FormTemplateVersion;
};
