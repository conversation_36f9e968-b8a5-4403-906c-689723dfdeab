const { test, expect, devices } = require('@playwright/test');
const { TEST_USERS } = require('../utils/test-helpers');

test.describe('Responsive Design Tests', () => {
  // Test on mobile device
  test.describe('Mobile View', () => {
    test.use({ ...devices['iPhone 12'] });
    
    test('should display login page correctly on mobile', async ({ page }) => {
      // Navigate to login page
      await page.goto('/login');
      
      // Verify login form is visible and properly sized
      await expect(page.locator('.login-card')).toBeVisible();
      
      // Check if elements are properly stacked on mobile
      const loginCardBox = await page.locator('.login-card').boundingBox();
      expect(loginCardBox.width).toBeLessThan(500); // Should be narrower on mobile
      
      // Verify inputs are visible and usable
      await expect(page.locator('input[placeholder="用户名"]')).toBeVisible();
      await expect(page.locator('input[placeholder="密码"]')).toBeVisible();
      await expect(page.locator('.login-button')).toBeVisible();
    });
    
    test('should login and display home page correctly on mobile', async ({ page }) => {
      // Navigate to login page
      await page.goto('/login');
      
      // Login
      await page.fill('input[placeholder="用户名"]', TEST_USERS.admin.username);
      await page.fill('input[placeholder="密码"]', TEST_USERS.admin.password);
      await page.click('.login-button');
      
      // Wait for navigation to home page
      await page.waitForURL(/.*\//);
      
      // Verify home page elements are visible and properly sized
      await expect(page.locator('h1')).toContainText('欢迎使用工作流系统');
      
      // Check if module cards are stacked vertically on mobile
      const moduleCards = await page.locator('.module-card').all();
      if (moduleCards.length > 1) {
        const firstCard = await moduleCards[0].boundingBox();
        const secondCard = await moduleCards[1].boundingBox();
        
        // On mobile, cards should be stacked (second card should be below first)
        expect(secondCard.y).toBeGreaterThan(firstCard.y + firstCard.height - 10);
      }
    });
    
    test('should display tables correctly on mobile', async ({ page }) => {
      // Login
      await page.goto('/login');
      await page.fill('input[placeholder="用户名"]', TEST_USERS.admin.username);
      await page.fill('input[placeholder="密码"]', TEST_USERS.admin.password);
      await page.click('.login-button');
      await page.waitForURL(/.*\//);
      
      // Navigate to a page with tables
      await page.goto('/workflow-form');
      
      // Verify table is visible
      await expect(page.locator('.el-table')).toBeVisible();
      
      // Check if table is responsive (horizontal scroll might be present)
      const tableWidth = await page.locator('.el-table').evaluate(el => el.scrollWidth);
      const containerWidth = await page.locator('.el-table').evaluate(el => el.clientWidth);
      
      // Log the dimensions for debugging
      console.log(`Table scroll width: ${tableWidth}px, Container width: ${containerWidth}px`);
      
      // Either the table fits (equal widths) or it has horizontal scroll (scroll width > container width)
      expect(tableWidth >= containerWidth).toBeTruthy();
    });
  });
  
  // Test on tablet device
  test.describe('Tablet View', () => {
    test.use({ ...devices['iPad Pro 11'] });
    
    test('should display login page correctly on tablet', async ({ page }) => {
      // Navigate to login page
      await page.goto('/login');
      
      // Verify login form is visible and properly sized
      await expect(page.locator('.login-card')).toBeVisible();
      
      // Check if elements are properly laid out on tablet
      const loginCardBox = await page.locator('.login-card').boundingBox();
      expect(loginCardBox.width).toBeGreaterThan(300); // Should be wider on tablet
      
      // Verify inputs are visible and usable
      await expect(page.locator('input[placeholder="用户名"]')).toBeVisible();
      await expect(page.locator('input[placeholder="密码"]')).toBeVisible();
      await expect(page.locator('.login-button')).toBeVisible();
    });
    
    test('should display workflow designer correctly on tablet', async ({ page }) => {
      // Login
      await page.goto('/login');
      await page.fill('input[placeholder="用户名"]', TEST_USERS.admin.username);
      await page.fill('input[placeholder="密码"]', TEST_USERS.admin.password);
      await page.click('.login-button');
      await page.waitForURL(/.*\//);
      
      // Navigate to workflow design
      await page.goto('/workflow-design');
      
      // Click on create workflow button
      await page.click('button:has-text("创建工作流")');
      
      // Verify designer elements are visible
      await expect(page.locator('.workflow-canvas')).toBeVisible();
      await expect(page.locator('.node-types')).toBeVisible();
      
      // Check if canvas is properly sized for tablet
      const canvasBox = await page.locator('.workflow-canvas').boundingBox();
      expect(canvasBox.width).toBeGreaterThan(500); // Should be reasonably wide on tablet
    });
  });
  
  // Test on desktop
  test.describe('Desktop View', () => {
    test.use({ viewport: { width: 1920, height: 1080 } });
    
    test('should display home page correctly on desktop', async ({ page }) => {
      // Login
      await page.goto('/login');
      await page.fill('input[placeholder="用户名"]', TEST_USERS.admin.username);
      await page.fill('input[placeholder="密码"]', TEST_USERS.admin.password);
      await page.click('.login-button');
      await page.waitForURL(/.*\//);
      
      // Verify home page elements are visible and properly sized
      await expect(page.locator('h1')).toContainText('欢迎使用工作流系统');
      
      // Check if module cards are displayed in a grid on desktop
      const moduleCards = await page.locator('.module-card').all();
      if (moduleCards.length > 1) {
        const firstCard = await moduleCards[0].boundingBox();
        const secondCard = await moduleCards[1].boundingBox();
        
        // On desktop, cards should be in a row or grid
        // Either horizontally adjacent or in a grid pattern
        const horizontallyAdjacent = Math.abs(secondCard.y - firstCard.y) < 10;
        const inGrid = (secondCard.x > firstCard.x + firstCard.width - 10) || 
                      (secondCard.y > firstCard.y + firstCard.height - 10);
        
        expect(horizontallyAdjacent || inGrid).toBeTruthy();
      }
    });
    
    test('should display form designer correctly on desktop', async ({ page }) => {
      // Login
      await page.goto('/login');
      await page.fill('input[placeholder="用户名"]', TEST_USERS.admin.username);
      await page.fill('input[placeholder="密码"]', TEST_USERS.admin.password);
      await page.click('.login-button');
      await page.waitForURL(/.*\//);
      
      // Navigate to form design
      await page.goto('/form-design');
      
      // Click on create form button
      await page.click('button:has-text("创建表单")');
      
      // Verify designer elements are visible
      await expect(page.locator('.form-canvas')).toBeVisible();
      await expect(page.locator('.component-list')).toBeVisible();
      await expect(page.locator('.property-panel')).toBeVisible();
      
      // Check if panels are laid out horizontally on desktop
      const componentListBox = await page.locator('.component-list').boundingBox();
      const canvasBox = await page.locator('.form-canvas').boundingBox();
      const propertyPanelBox = await page.locator('.property-panel').boundingBox();
      
      // On desktop, these should be laid out horizontally
      // Component list should be to the left of canvas
      expect(componentListBox.x + componentListBox.width).toBeLessThanOrEqual(canvasBox.x + 10);
      
      // Property panel should be to the right of canvas
      expect(canvasBox.x + canvasBox.width).toBeLessThanOrEqual(propertyPanelBox.x + 10);
    });
  });
});
