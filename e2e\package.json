{"name": "workflow-system-e2e", "version": "1.0.0", "description": "End-to-end tests for workflow system", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:api": "playwright test tests/api/", "test:a11y": "playwright test tests/accessibility/", "test:security": "playwright test tests/security/", "test:load": "playwright test tests/load/", "test:smoke": "node ci-test.js --smoke", "test:regression": "node ci-test.js --regression", "test:visual": "node ci-test.js --visual", "test:advanced": "node advanced-test-runner.js", "test:advanced:monitor": "node advanced-test-runner.js --monitor --report", "test:advanced:full": "node advanced-test-runner.js --type=all --monitor --report --notify", "report": "playwright show-report", "analyze": "node analyze-results.js --html --json --trend", "clean": "<PERSON><PERSON><PERSON> playwright-report test-results accessibility-reports load-test-results advanced-reports"}, "devDependencies": {"@axe-core/playwright": "^4.7.3", "@playwright/test": "^1.52.0", "axios": "^1.6.2", "chart.js": "^4.4.0", "crypto": "^1.0.1", "rimraf": "^5.0.5", "wait-on": "^7.0.1"}}