const { test, expect } = require('@playwright/test');
const { TEST_USERS } = require('../utils/test-helpers');

test.describe('Login Tests', () => {
  test('should login successfully with admin credentials', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');

    // Fill login form with admin credentials
    await page.fill('input[placeholder="用户名"]', TEST_USERS.admin.username);
    await page.fill('input[placeholder="密码"]', TEST_USERS.admin.password);
    
    // Click login button
    await page.click('.login-button');

    // Verify login was successful - wait for navigation to home page
    await page.waitForURL(/.*\//);
    
    // Verify user info is displayed
    const userInfo = await page.locator('.user-info').textContent();
    expect(userInfo).toContain(TEST_USERS.admin.fullName);
  });

  test('should login successfully with user credentials', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');

    // Fill login form with user credentials
    await page.fill('input[placeholder="用户名"]', TEST_USERS.user.username);
    await page.fill('input[placeholder="密码"]', TEST_USERS.user.password);
    
    // Click login button
    await page.click('.login-button');

    // Verify login was successful - wait for navigation to home page
    await page.waitForURL(/.*\//);
    
    // Verify user info is displayed
    const userInfo = await page.locator('.user-info').textContent();
    expect(userInfo).toContain(TEST_USERS.user.fullName);
  });

  test('should show error message with invalid credentials', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');

    // Fill login form with invalid credentials
    await page.fill('input[placeholder="用户名"]', 'invalid');
    await page.fill('input[placeholder="密码"]', 'invalid');
    
    // Click login button
    await page.click('.login-button');

    // Verify error message is displayed
    const errorMessage = await page.locator('.el-message--error');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText('登录失败');
  });

  test('should remember username when "记住我" is checked', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');

    // Fill login form
    await page.fill('input[placeholder="用户名"]', TEST_USERS.admin.username);
    await page.fill('input[placeholder="密码"]', TEST_USERS.admin.password);
    
    // Check "记住我" checkbox
    await page.check('.remember-me input[type="checkbox"]');
    
    // Click login button
    await page.click('.login-button');

    // Wait for navigation to home page
    await page.waitForURL(/.*\//);
    
    // Navigate back to login page
    await page.goto('/login');
    
    // Verify username is remembered
    const username = await page.inputValue('input[placeholder="用户名"]');
    expect(username).toBe(TEST_USERS.admin.username);
  });
});
