const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     TestResult:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 测试结果ID
 *           example: 1
 *         testRunId:
 *           type: integer
 *           description: 测试运行ID
 *           example: 1
 *         file:
 *           type: string
 *           description: 测试文件
 *           example: "login.spec.js"
 *         title:
 *           type: string
 *           description: 测试标题
 *           example: "should login successfully"
 *         status:
 *           type: string
 *           description: 测试状态
 *           example: "passed"
 *         duration:
 *           type: number
 *           description: 测试持续时间（毫秒）
 *           example: 1500
 *         error:
 *           type: object
 *           description: 错误信息
 *           properties:
 *             message:
 *               type: string
 *               description: 错误消息
 *               example: "Expected true to be false"
 *             stack:
 *               type: string
 *               description: 错误堆栈
 *               example: "Error: Expected true to be false\n    at Object.<anonymous> (/app/tests/login.spec.js:25:27)"
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2025-05-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2025-05-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class TestResult extends Model {
    static associate(models) {
      // 测试运行
      TestResult.belongsTo(models.TestRun, {
        foreignKey: 'testRunId',
        as: 'testRun'
      });
    }
  }

  TestResult.init({
    testRunId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'test_runs',
        key: 'id'
      }
    },
    file: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    title: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    error: {
      type: DataTypes.JSONB,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'TestResult',
    tableName: 'test_results',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return TestResult;
};
