/**
 * 请求日志中间件
 * 记录请求URL、请求数据和响应数据
 */
const requestLogger = (req, res, next) => {
  // 获取请求开始时间
  const startTime = Date.now();

  // 记录请求信息
  const requestInfo = {
    method: req.method,
    url: req.originalUrl || req.url,
    ip: req.ip || req.connection.remoteAddress,
    requestBody: req.body,
    requestQuery: req.query,
    requestParams: req.params
  };

  // 直接打印请求信息到终端
  console.log('\n==============================');
  console.log('请求信息:');
  console.log('------------------------------');
  console.log(`请求方法: ${requestInfo.method}`);
  console.log(`请求地址: ${requestInfo.url}`);
  console.log(`客户端IP: ${requestInfo.ip}`);
  console.log('请求数据:');
  console.log(JSON.stringify(requestInfo.requestBody, null, 2));
  console.log('查询参数:');
  console.log(JSON.stringify(requestInfo.requestQuery, null, 2));
  console.log('路由参数:');
  console.log(JSON.stringify(requestInfo.requestParams, null, 2));

  // 捕获响应完成事件
  res.on('finish', () => {
    // 计算请求处理时间
    const responseTime = Date.now() - startTime;

    // 记录响应信息
    const responseInfo = {
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`
    };

    // 直接打印响应信息到终端
    console.log('------------------------------');
    console.log('响应信息:');
    console.log(`状态码: ${responseInfo.statusCode}`);
    console.log(`响应时间: ${responseInfo.responseTime}`);
    console.log('==============================\n');
  });

  next();
};

module.exports = requestLogger;
