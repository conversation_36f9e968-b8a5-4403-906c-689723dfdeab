require('dotenv').config();
const express = require('express');
const cors = require('cors');
const swaggerUi = require('swagger-ui-express');
const swaggerSpecs = require('./config/swagger');
const routes = require('./routes');
const simpleLogger = require('./middlewares/simple-logger');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors({
  origin: true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求和响应日志中间件
app.use(simpleLogger);

// HTTP协议版本检查中间件 - 仅对非API请求应用
app.use((req, res, next) => {
  // 只对非API请求设置Upgrade-Insecure-Requests头
  res.setHeader('Upgrade-Insecure-Requests', '1');
  next();
});


// Swagger文档
app.use('/swagger', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, { explorer: true }));
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, { explorer: true }));

// 身份验证中间件
const { authenticate } = require('./middlewares/auth');



// 测试路由
app.get('/api/test', (req, res) => {
  console.log('\n>>> 请求: GET /api/test');
  const response = { message: 'Test route works!' };
  console.log('<<< 响应数据:', response);
  res.json(response);
});

// 测试路由 - 带参数
app.get('/api/echo', (req, res) => {
  console.log('\n>>> 请求: GET /api/echo');
  console.log('>>> 查询参数:', req.query);
  const response = {
    message: 'Echo endpoint',
    params: req.query,
    timestamp: new Date().toISOString()
  };
  console.log('<<< 响应数据:', response);
  res.json(response);
});



// 登录路由 - 直接在这里定义，不经过认证中间件
app.post('/api/users/login', require('./controllers/user').login);

// 注册路由 - 直接在这里定义，不经过认证中间件
app.post('/api/users/register', require('./controllers/user').register);

// 其他API路由 - 需要认证
app.use('/api', authenticate, routes);

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  console.log('请求日志已启用，将在控制台显示请求和响应信息');
});

module.exports = app;
