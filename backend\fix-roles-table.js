/**
 * Fix roles table script
 * Run with: node fix-roles-table.js
 */

const { sequelize } = require('./src/models');

async function fixRolesTable() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    console.log('Adding display_name column to roles table...');
    
    // Execute raw SQL to add the display_name column
    await sequelize.query(`
      ALTER TABLE roles 
      ADD COLUMN IF NOT EXISTS display_name VARCHAR(100);
    `);
    
    // Update existing records to set display_name equal to name
    await sequelize.query(`
      UPDATE roles 
      SET display_name = name 
      WHERE display_name IS NULL;
    `);
    
    // Make display_name column NOT NULL
    await sequelize.query(`
      ALTER TABLE roles 
      ALTER COLUMN display_name SET NOT NULL;
    `);
    
    console.log('Roles table updated successfully.');
  } catch (error) {
    console.error('Error fixing roles table:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

fixRolesTable();
