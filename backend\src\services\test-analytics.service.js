const { TestRun, TestResult, TestMetric } = require('../models');
const { Op, Sequelize } = require('sequelize');

/**
 * Get analytics data
 * @param {Object} where - Filter conditions
 * @returns {Object} Analytics data
 */
exports.getAnalyticsData = async (where = {}) => {
  // Get test runs
  const testRuns = await TestRun.findAll({
    where,
    include: [
      {
        model: TestResult,
        as: 'results'
      },
      {
        model: TestMetric,
        as: 'metrics'
      }
    ],
    order: [['createdAt', 'DESC']]
  });
  
  // Calculate statistics
  const totalRuns = testRuns.length;
  const successfulRuns = testRuns.filter(run => run.success).length;
  const failedRuns = totalRuns - successfulRuns;
  const successRate = totalRuns > 0 ? (successfulRuns / totalRuns) * 100 : 0;
  
  // Calculate test statistics
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  let skippedTests = 0;
  
  testRuns.forEach(run => {
    if (run.testStats) {
      totalTests += run.testStats.total || 0;
      passedTests += run.testStats.passed || 0;
      failedTests += run.testStats.failed || 0;
      skippedTests += run.testStats.skipped || 0;
    }
  });
  
  // Calculate average duration
  const totalDuration = testRuns.reduce((sum, run) => sum + (run.duration || 0), 0);
  const averageDuration = totalRuns > 0 ? totalDuration / totalRuns : 0;
  
  // Get test types distribution
  const testTypeDistribution = {};
  testRuns.forEach(run => {
    const testType = run.testType || 'unknown';
    testTypeDistribution[testType] = (testTypeDistribution[testType] || 0) + 1;
  });
  
  // Get most failed tests
  const failedTestsMap = {};
  testRuns.forEach(run => {
    if (run.failedTests && Array.isArray(run.failedTests)) {
      run.failedTests.forEach(test => {
        const testId = `${test.file}:${test.title}`;
        failedTestsMap[testId] = (failedTestsMap[testId] || 0) + 1;
      });
    }
  });
  
  const mostFailedTests = Object.entries(failedTestsMap)
    .map(([testId, count]) => {
      const [file, title] = testId.split(':');
      return { file, title, count };
    })
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
  
  return {
    summary: {
      totalRuns,
      successfulRuns,
      failedRuns,
      successRate,
      totalTests,
      passedTests,
      failedTests,
      skippedTests,
      averageDuration
    },
    testTypeDistribution,
    mostFailedTests,
    recentRuns: testRuns.slice(0, 10).map(run => ({
      id: run.id,
      testRunId: run.testRunId,
      testType: run.testType,
      success: run.success,
      duration: run.duration,
      timestamp: run.createdAt,
      testStats: run.testStats
    }))
  };
};

/**
 * Get summary data
 * @returns {Object} Summary data
 */
exports.getSummaryData = async () => {
  // Get total runs
  const totalRuns = await TestRun.count();
  
  // Get successful runs
  const successfulRuns = await TestRun.count({
    where: { success: true }
  });
  
  // Calculate failed runs and success rate
  const failedRuns = totalRuns - successfulRuns;
  const successRate = totalRuns > 0 ? (successfulRuns / totalRuns) * 100 : 0;
  
  // Get latest run
  const latestRun = await TestRun.findOne({
    order: [['createdAt', 'DESC']],
    include: [
      {
        model: TestResult,
        as: 'results'
      }
    ]
  });
  
  // Get test type distribution
  const testTypeDistribution = await TestRun.findAll({
    attributes: [
      'testType',
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
    ],
    group: ['testType'],
    order: [[Sequelize.literal('count'), 'DESC']]
  });
  
  // Get recent runs
  const recentRuns = await TestRun.findAll({
    order: [['createdAt', 'DESC']],
    limit: 5
  });
  
  return {
    totalRuns,
    successfulRuns,
    failedRuns,
    successRate,
    latestRun: latestRun ? {
      id: latestRun.id,
      testRunId: latestRun.testRunId,
      testType: latestRun.testType,
      success: latestRun.success,
      duration: latestRun.duration,
      timestamp: latestRun.createdAt,
      testStats: latestRun.testStats
    } : null,
    testTypeDistribution: testTypeDistribution.map(item => ({
      testType: item.testType || 'unknown',
      count: parseInt(item.getDataValue('count'))
    })),
    recentRuns: recentRuns.map(run => ({
      id: run.id,
      testRunId: run.testRunId,
      testType: run.testType,
      success: run.success,
      duration: run.duration,
      timestamp: run.createdAt
    }))
  };
};

/**
 * Get paginated test runs
 * @param {Object} where - Filter conditions
 * @param {number} page - Page number
 * @param {number} limit - Page size
 * @returns {Object} Paginated test runs
 */
exports.getPaginatedTestRuns = async (where = {}, page = 1, limit = 10) => {
  // Calculate offset
  const offset = (page - 1) * limit;
  
  // Get test runs with pagination
  const { count, rows } = await TestRun.findAndCountAll({
    where,
    include: [
      {
        model: TestResult,
        as: 'results'
      }
    ],
    order: [['createdAt', 'DESC']],
    limit,
    offset
  });
  
  // Calculate total pages
  const totalPages = Math.ceil(count / limit);
  
  return {
    testRuns: rows,
    pagination: {
      total: count,
      page,
      limit,
      totalPages
    }
  };
};

/**
 * Get test run by ID
 * @param {string} id - Test run ID
 * @returns {Object} Test run
 */
exports.getTestRunById = async (id) => {
  return TestRun.findOne({
    where: { 
      [Op.or]: [
        { id },
        { testRunId: id }
      ]
    },
    include: [
      {
        model: TestResult,
        as: 'results'
      },
      {
        model: TestMetric,
        as: 'metrics'
      }
    ]
  });
};

/**
 * Create test run
 * @param {Object} testRunData - Test run data
 * @returns {Object} Created test run
 */
exports.createTestRun = async (testRunData) => {
  const {
    testRunId,
    testType,
    success,
    duration,
    testStats,
    failedTests,
    config,
    results,
    metrics
  } = testRunData;
  
  // Create test run
  const testRun = await TestRun.create({
    testRunId,
    testType,
    success,
    duration,
    testStats,
    failedTests,
    config
  });
  
  // Create test results if provided
  if (results && Array.isArray(results)) {
    await Promise.all(
      results.map(result => 
        TestResult.create({
          ...result,
          testRunId: testRun.id
        })
      )
    );
  }
  
  // Create test metrics if provided
  if (metrics && Array.isArray(metrics)) {
    await Promise.all(
      metrics.map(metric => 
        TestMetric.create({
          ...metric,
          testRunId: testRun.id
        })
      )
    );
  }
  
  return testRun;
};

/**
 * Get trend data
 * @param {number} days - Number of days
 * @param {string} testType - Test type
 * @returns {Object} Trend data
 */
exports.getTrendData = async (days = 7, testType) => {
  // Calculate start date
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  // Build filter conditions
  const where = {
    createdAt: {
      [Op.gte]: startDate
    }
  };
  
  if (testType) {
    where.testType = testType;
  }
  
  // Get test runs
  const testRuns = await TestRun.findAll({
    where,
    order: [['createdAt', 'ASC']]
  });
  
  // Group test runs by date
  const runsByDate = {};
  
  testRuns.forEach(run => {
    const date = run.createdAt.toISOString().split('T')[0];
    
    if (!runsByDate[date]) {
      runsByDate[date] = {
        total: 0,
        successful: 0,
        failed: 0,
        duration: 0,
        tests: {
          total: 0,
          passed: 0,
          failed: 0,
          skipped: 0
        }
      };
    }
    
    runsByDate[date].total += 1;
    runsByDate[date].successful += run.success ? 1 : 0;
    runsByDate[date].failed += run.success ? 0 : 1;
    runsByDate[date].duration += run.duration || 0;
    
    if (run.testStats) {
      runsByDate[date].tests.total += run.testStats.total || 0;
      runsByDate[date].tests.passed += run.testStats.passed || 0;
      runsByDate[date].tests.failed += run.testStats.failed || 0;
      runsByDate[date].tests.skipped += run.testStats.skipped || 0;
    }
  });
  
  // Fill in missing dates
  const dateArray = [];
  const currentDate = new Date(startDate);
  const endDate = new Date();
  
  while (currentDate <= endDate) {
    const dateString = currentDate.toISOString().split('T')[0];
    
    if (!runsByDate[dateString]) {
      runsByDate[dateString] = {
        total: 0,
        successful: 0,
        failed: 0,
        duration: 0,
        tests: {
          total: 0,
          passed: 0,
          failed: 0,
          skipped: 0
        }
      };
    }
    
    dateArray.push(dateString);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  // Calculate success rate and average duration
  Object.keys(runsByDate).forEach(date => {
    const data = runsByDate[date];
    data.successRate = data.total > 0 ? (data.successful / data.total) * 100 : 0;
    data.averageDuration = data.total > 0 ? data.duration / data.total : 0;
  });
  
  // Prepare trend data
  const trendData = {
    dates: dateArray,
    runs: dateArray.map(date => runsByDate[date].total),
    successRate: dateArray.map(date => runsByDate[date].successRate),
    averageDuration: dateArray.map(date => runsByDate[date].averageDuration),
    tests: {
      total: dateArray.map(date => runsByDate[date].tests.total),
      passed: dateArray.map(date => runsByDate[date].tests.passed),
      failed: dateArray.map(date => runsByDate[date].tests.failed),
      skipped: dateArray.map(date => runsByDate[date].tests.skipped)
    }
  };
  
  return trendData;
};
