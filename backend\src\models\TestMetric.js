const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     TestMetric:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 测试指标ID
 *           example: 1
 *         testRunId:
 *           type: integer
 *           description: 测试运行ID
 *           example: 1
 *         metricType:
 *           type: string
 *           description: 指标类型
 *           example: "cpu"
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: 时间戳
 *           example: "2025-05-01T10:00:00Z"
 *         value:
 *           type: number
 *           description: 指标值
 *           example: 45.2
 *         unit:
 *           type: string
 *           description: 单位
 *           example: "%"
 *         metadata:
 *           type: object
 *           description: 元数据
 *           example: { "browser": "chrome", "page": "login" }
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2025-05-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2025-05-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class TestMetric extends Model {
    static associate(models) {
      // 测试运行
      TestMetric.belongsTo(models.TestRun, {
        foreignKey: 'testRunId',
        as: 'testRun'
      });
    }
  }

  TestMetric.init({
    testRunId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'test_runs',
        key: 'id'
      }
    },
    metricType: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    timestamp: {
      type: DataTypes.DATE,
      allowNull: false
    },
    value: {
      type: DataTypes.FLOAT,
      allowNull: false
    },
    unit: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'TestMetric',
    tableName: 'test_metrics',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return TestMetric;
};
