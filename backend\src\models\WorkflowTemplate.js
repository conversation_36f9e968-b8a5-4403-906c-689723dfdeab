const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     WorkflowTemplate:
 *       type: object
 *       required:
 *         - title
 *         - form_template_id
 *         - creator_id
 *       properties:
 *         id:
 *           type: integer
 *           description: 工作流模板ID
 *           example: 1
 *         title:
 *           type: string
 *           description: 工作流模板名称
 *           example: "请假审批流程"
 *         description:
 *           type: string
 *           description: 工作流模板描述
 *           example: "员工请假审批流程，包含部门经理和人事审批"
 *         form_template_id:
 *           type: integer
 *           description: 关联的表单模板ID
 *           example: 1
 *         creator_id:
 *           type: integer
 *           description: 创建者ID
 *           example: 1
 *         version:
 *           type: integer
 *           description: 当前版本号
 *           example: 1
 *         status:
 *           type: string
 *           description: 工作流状态
 *           enum: [draft, published]
 *           example: "published"
 *         schema:
 *           type: object
 *           description: 工作流配置，包含节点和连线信息
 *           example: {
 *             "nodes": [
 *               {"id": "start", "type": "start", "name": "开始", "position": {"x": 100, "y": 100}},
 *               {"id": "approve1", "type": "approve", "name": "部门经理审批", "position": {"x": 300, "y": 100}},
 *               {"id": "approve2", "type": "approve", "name": "人事审批", "position": {"x": 500, "y": 100}},
 *               {"id": "end", "type": "end", "name": "结束", "position": {"x": 700, "y": 100}}
 *             ],
 *             "edges": [
 *               {"source": "start", "target": "approve1"},
 *               {"source": "approve1", "target": "approve2"},
 *               {"source": "approve2", "target": "end"}
 *             ]
 *           }
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 *       example:
 *         id: 1
 *         title: "请假审批流程"
 *         description: "员工请假审批流程，包含部门经理和人事审批"
 *         form_template_id: 1
 *         creator_id: 1
 *         version: 1
 *         status: "published"
 *         schema: {
 *           "nodes": [
 *             {"id": "start", "type": "start", "name": "开始", "position": {"x": 100, "y": 100}},
 *             {"id": "approve1", "type": "approve", "name": "部门经理审批", "position": {"x": 300, "y": 100}},
 *             {"id": "approve2", "type": "approve", "name": "人事审批", "position": {"x": 500, "y": 100}},
 *             {"id": "end", "type": "end", "name": "结束", "position": {"x": 700, "y": 100}}
 *           ],
 *           "edges": [
 *             {"source": "start", "target": "approve1"},
 *             {"source": "approve1", "target": "approve2"},
 *             {"source": "approve2", "target": "end"}
 *           ]
 *         }
 *         created_at: "2023-06-01T10:00:00Z"
 *         updated_at: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class WorkflowTemplate extends Model {
    static associate(models) {
      // 表单模板
      WorkflowTemplate.belongsTo(models.FormTemplate, {
        foreignKey: 'form_template_id',
        as: 'formTemplate'
      });

      // 创建者
      WorkflowTemplate.belongsTo(models.User, {
        foreignKey: 'creator_id',
        as: 'creator'
      });

      // 工作流节点
      if (models.WorkflowNode) {
        WorkflowTemplate.hasMany(models.WorkflowNode, {
          foreignKey: 'workflow_template_id',
          as: 'nodes'
        });
      }

      // 工作流转换
      if (models.WorkflowTransition) {
        WorkflowTemplate.hasMany(models.WorkflowTransition, {
          foreignKey: 'workflow_template_id',
          as: 'transitions'
        });
      }
    }
  }

  WorkflowTemplate.init({
    title: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    form_template_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'form_templates',
        key: 'id'
      }
    },
    creator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    version: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'draft'
    },
    schema: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {}
    }
  }, {
    sequelize,
    modelName: 'WorkflowTemplate',
    tableName: 'workflow_templates',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return WorkflowTemplate;
};
