/**
 * Global teardown for Playwright tests
 * This script runs after all tests to clean up the test environment
 */

const TestDataManager = require('./utils/test-data-manager');
const fs = require('fs');
const path = require('path');

// Configuration
const API_BASE_URL = 'http://localhost:5273/api';

/**
 * Teardown function that runs after all tests
 */
async function globalTeardown(config) {
  console.log('Starting global teardown...');
  
  // Get admin auth token from saved state
  const adminAuthPath = path.join(__dirname, 'auth', 'admin.json');
  let authToken = '';
  
  if (fs.existsSync(adminAuthPath)) {
    try {
      const authState = JSON.parse(fs.readFileSync(adminAuthPath, 'utf8'));
      const localStorage = authState.origins.find(o => 
        o.origin === 'http://localhost:5273'
      )?.localStorage;
      
      if (localStorage) {
        const tokenItem = localStorage.find(item => item.name === 'token');
        if (tokenItem) {
          authToken = tokenItem.value;
        }
      }
    } catch (error) {
      console.error('Error reading auth state:', error.message);
    }
  }
  
  // Create test data manager
  const testDataManager = new TestDataManager({
    baseURL: API_BASE_URL,
    authToken
  });
  
  // Clean up test data
  try {
    await testDataManager.cleanup();
    console.log('Test data cleanup completed');
  } catch (error) {
    console.error('Error cleaning up test data:', error.message);
  }
  
  console.log('Global teardown completed');
}

module.exports = globalTeardown;
