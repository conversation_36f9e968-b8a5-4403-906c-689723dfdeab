# 测试分析系统

## 概述

测试分析系统是一个用于收集、分析和可视化测试结果的工具。它提供了一个全面的仪表板，用于监控测试执行情况、识别问题模式并提供有关测试质量的见解。

## 功能特点

- **测试结果收集**：自动收集和存储测试运行结果
- **测试分析仪表板**：可视化测试结果和趋势
- **性能指标**：跟踪和分析测试执行的性能指标
- **失败分析**：识别和分析最常失败的测试
- **趋势分析**：随时间跟踪测试结果趋势
- **测试类型分布**：分析不同类型测试的分布情况

## 系统架构

测试分析系统由以下组件组成：

1. **后端 API**：提供数据存储和分析功能
2. **前端仪表板**：提供用户界面和可视化
3. **测试集成**：将测试结果发送到分析系统的工具

### 后端组件

- **TestRun 模型**：存储测试运行的基本信息
- **TestResult 模型**：存储单个测试的结果
- **TestMetric 模型**：存储测试运行期间收集的性能指标
- **测试分析服务**：提供数据分析和处理功能
- **测试分析 API**：提供 RESTful API 接口

### 前端组件

- **测试分析仪表板**：显示测试结果和趋势的主页面
- **测试运行详情**：显示单个测试运行的详细信息
- **图表和可视化**：使用 ECharts 提供数据可视化

### 集成组件

- **TestAnalyticsClient**：将测试结果发送到分析 API 的客户端
- **TestReporter 集成**：与现有测试报告工具的集成

## 安装和配置

### 数据库迁移

运行以下命令创建测试分析表：

```bash
node scripts/migrate-test-analytics.js
```

### 配置测试运行器

测试分析系统已集成到现有的测试运行器中。默认情况下，所有测试运行都会将结果发送到分析系统。

可以通过以下配置选项控制测试分析：

```javascript
const testReporter = new TestReporter({
  // ...其他配置
  analyticsEnabled: true, // 启用或禁用分析
  testType: 'smoke',      // 测试类型
  testConfig: {           // 测试配置
    // ...测试配置
  }
});
```

## 使用指南

### 访问测试分析仪表板

测试分析仪表板可以通过以下 URL 访问：

```
http://localhost:5273/test-analytics
```

### 查看测试运行详情

点击测试运行列表中的"详情"按钮，可以查看单个测试运行的详细信息。

### 分析测试趋势

测试分析仪表板提供了测试趋势图表，可以查看测试成功率、执行时间等指标的变化趋势。

### 识别问题模式

"最常失败的测试"部分列出了失败次数最多的测试，帮助识别需要重点关注的问题。

## API 参考

### 获取测试分析数据

```
GET /api/test-analytics
```

### 获取测试分析摘要

```
GET /api/test-analytics/summary
```

### 获取测试运行列表

```
GET /api/test-analytics/runs
```

### 获取测试运行详情

```
GET /api/test-analytics/runs/:id
```

### 提交测试运行结果

```
POST /api/test-analytics/runs
```

### 获取测试趋势数据

```
GET /api/test-analytics/trends
```

## 数据模型

### TestRun

| 字段 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| testRunId | STRING | 测试运行唯一标识符 |
| testType | STRING | 测试类型 |
| success | BOOLEAN | 测试是否成功 |
| duration | FLOAT | 测试持续时间（秒） |
| testStats | JSONB | 测试统计数据 |
| failedTests | JSONB | 失败的测试列表 |
| config | JSONB | 测试配置 |
| created_at | DATE | 创建时间 |
| updated_at | DATE | 更新时间 |

### TestResult

| 字段 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| testRunId | INTEGER | 测试运行 ID |
| file | STRING | 测试文件 |
| title | TEXT | 测试标题 |
| status | STRING | 测试状态 |
| duration | INTEGER | 测试持续时间（毫秒） |
| error | JSONB | 错误信息 |
| created_at | DATE | 创建时间 |
| updated_at | DATE | 更新时间 |

### TestMetric

| 字段 | 类型 | 描述 |
|------|------|------|
| id | INTEGER | 主键 |
| testRunId | INTEGER | 测试运行 ID |
| metricType | STRING | 指标类型 |
| timestamp | DATE | 时间戳 |
| value | FLOAT | 指标值 |
| unit | STRING | 单位 |
| metadata | JSONB | 元数据 |
| created_at | DATE | 创建时间 |
| updated_at | DATE | 更新时间 |

## 故障排除

### 测试结果未显示在分析仪表板上

1. 检查测试运行器中的 `analyticsEnabled` 设置是否为 `true`
2. 检查网络连接，确保测试运行器可以连接到分析 API
3. 检查后端日志中是否有错误信息

### 数据库迁移失败

1. 确保数据库服务正在运行
2. 检查数据库连接配置
3. 检查数据库用户是否有创建表的权限

## 未来计划

- 添加更多的数据可视化
- 实现测试结果比较功能
- 添加自动化报告生成
- 集成 CI/CD 系统通知
- 添加更多的性能指标分析
