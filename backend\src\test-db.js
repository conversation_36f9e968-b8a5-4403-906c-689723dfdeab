const { sequelize, Department, User } = require('./models');

async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('Connection has been established successfully.');
    
    console.log('Department model:', Department);
    
    if (Department && Department.findAll) {
      console.log('Trying to find all departments...');
      const departments = await Department.findAll();
      console.log('Departments:', departments);
    } else {
      console.log('Department model or findAll method is not defined');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await sequelize.close();
  }
}

testConnection();
