/**
 * Global setup test
 * This test runs before other tests to verify the test environment is properly set up
 */

const { test, expect } = require('@playwright/test');
const TestDataManager = require('../utils/test-data-manager');

test('verify test environment is properly set up', async ({ page }) => {
  console.log('Verifying test environment...');
  
  // Navigate to home page
  await page.goto('/');
  
  // Verify we're logged in as admin
  const userInfo = await page.locator('.user-info, .user-profile').textContent();
  expect(userInfo).toContain('管理员');
  
  // Verify E2E test data exists
  
  // 1. Check if E2E Test Department exists
  await page.goto('/department');
  await expect(page.locator('.el-table__row').filter({ hasText: 'E2E Test Department' })).toBeVisible();
  
  // 2. Check if E2E Test Form exists
  await page.goto('/form-design');
  await expect(page.locator('.el-table__row').filter({ hasText: 'E2E Test Form' })).toBeVisible();
  
  // 3. Check if E2E Test Workflow exists
  await page.goto('/workflow-design');
  await expect(page.locator('.el-table__row').filter({ hasText: 'E2E Test Workflow' })).toBeVisible();
  
  console.log('Test environment verification completed successfully');
});
