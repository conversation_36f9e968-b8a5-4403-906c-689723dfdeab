/**
 * 请求和响应日志中间件
 * 直接打印请求和响应信息到控制台
 */
const requestResponseLogger = (req, res, next) => {
  // 获取请求开始时间
  const startTime = Date.now();
  
  // 创建响应数据缓冲区
  let responseBody = '';
  
  // 保存原始的方法
  const originalWrite = res.write;
  const originalEnd = res.end;
  
  // 重写write方法以捕获响应数据
  res.write = function(chunk) {
    responseBody += chunk.toString('utf8');
    return originalWrite.apply(res, arguments);
  };
  
  // 重写end方法以捕获响应数据并打印日志
  res.end = function(chunk) {
    if (chunk) {
      responseBody += chunk.toString('utf8');
    }
    
    // 计算响应时间
    const responseTime = Date.now() - startTime;
    
    // 打印请求信息
    console.log('\n==============================');
    console.log('请求信息:');
    console.log('------------------------------');
    console.log(`请求方法: ${req.method}`);
    console.log(`请求地址: ${req.originalUrl || req.url}`);
    console.log(`客户端IP: ${req.ip || req.connection.remoteAddress}`);
    
    // 打印请求体
    if (req.method !== 'GET') {
      console.log('请求数据:');
      console.log(JSON.stringify(req.body, null, 2));
    }
    
    // 打印查询参数
    if (Object.keys(req.query).length > 0) {
      console.log('查询参数:');
      console.log(JSON.stringify(req.query, null, 2));
    }
    
    // 打印路由参数
    if (Object.keys(req.params).length > 0) {
      console.log('路由参数:');
      console.log(JSON.stringify(req.params, null, 2));
    }
    
    // 打印响应信息
    console.log('------------------------------');
    console.log('响应信息:');
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应时间: ${responseTime}ms`);
    
    // 尝试解析响应体为JSON
    try {
      const jsonResponse = JSON.parse(responseBody);
      console.log('响应数据:');
      console.log(JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      // 如果不是JSON，直接打印
      if (responseBody) {
        console.log('响应数据: (非JSON格式)');
        console.log(responseBody.substring(0, 1000) + (responseBody.length > 1000 ? '...(已截断)' : ''));
      }
    }
    
    console.log('==============================\n');
    
    return originalEnd.apply(res, arguments);
  };
  
  next();
};

module.exports = requestResponseLogger;
