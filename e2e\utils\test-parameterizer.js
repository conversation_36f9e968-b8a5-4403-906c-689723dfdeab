/**
 * Test Parameterizer
 * Runs tests with different parameters
 */

const fs = require('fs');
const path = require('path');
const { spawnSync } = require('child_process');
const TestDataGenerator = require('./test-data-generator');

class TestParameterizer {
  constructor(config = {}) {
    this.configDir = config.configDir || path.join(__dirname, '../config');
    this.parameterSetsFile = config.parameterSetsFile || path.join(this.configDir, 'parameter-sets.json');
    this.outputDir = config.outputDir || path.join(__dirname, '../parameter-test-results');
    this.testDataGenerator = new TestDataGenerator();
    
    // Create directories if they don't exist
    if (!fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true });
    }
    
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
    
    // Load parameter sets
    this.parameterSets = this.loadParameterSets();
  }

  /**
   * Load parameter sets from file
   * @returns {object} - Parameter sets
   */
  loadParameterSets() {
    try {
      if (fs.existsSync(this.parameterSetsFile)) {
        return JSON.parse(fs.readFileSync(this.parameterSetsFile, 'utf8'));
      }
    } catch (error) {
      console.error('Failed to load parameter sets:', error.message);
    }
    
    // Return default parameter sets if file doesn't exist or is invalid
    return {
      default: {
        description: 'Default parameter set',
        parameters: {
          type: 'smoke',
          parallel: 2,
          retries: 1,
          timeout: 60000
        }
      },
      performance: {
        description: 'Performance testing parameter set',
        parameters: {
          type: 'load',
          parallel: 4,
          retries: 0,
          timeout: 120000
        }
      }
    };
  }

  /**
   * Save parameter sets to file
   */
  saveParameterSets() {
    try {
      fs.writeFileSync(this.parameterSetsFile, JSON.stringify(this.parameterSets, null, 2));
    } catch (error) {
      console.error('Failed to save parameter sets:', error.message);
      throw error;
    }
  }

  /**
   * Add a parameter set
   * @param {string} name - Parameter set name
   * @param {object} parameterSet - Parameter set
   */
  addParameterSet(name, parameterSet) {
    this.parameterSets[name] = parameterSet;
    this.saveParameterSets();
  }

  /**
   * Remove a parameter set
   * @param {string} name - Parameter set name
   */
  removeParameterSet(name) {
    if (this.parameterSets[name]) {
      delete this.parameterSets[name];
      this.saveParameterSets();
    }
  }

  /**
   * Get a parameter set
   * @param {string} name - Parameter set name
   * @returns {object} - Parameter set
   */
  getParameterSet(name) {
    return this.parameterSets[name] || null;
  }

  /**
   * List all parameter sets
   * @returns {object} - Parameter sets
   */
  listParameterSets() {
    return this.parameterSets;
  }

  /**
   * Generate a command for a parameter set
   * @param {string} parameterSetName - Parameter set name
   * @param {object} overrides - Parameter overrides
   * @returns {string} - Command
   */
  generateCommand(parameterSetName, overrides = {}) {
    const parameterSet = this.getParameterSet(parameterSetName);
    
    if (!parameterSet) {
      throw new Error(`Parameter set ${parameterSetName} not found`);
    }
    
    // Merge parameters with overrides
    const parameters = {
      ...parameterSet.parameters,
      ...overrides
    };
    
    // Build command
    let command = 'node advanced-test-runner.js';
    
    // Add parameters
    for (const [key, value] of Object.entries(parameters)) {
      if (value === true) {
        command += ` --${key}`;
      } else if (value !== false && value !== null && value !== undefined) {
        command += ` --${key}=${value}`;
      }
    }
    
    return command;
  }

  /**
   * Run tests with a parameter set
   * @param {string} parameterSetName - Parameter set name
   * @param {object} overrides - Parameter overrides
   * @returns {object} - Test results
   */
  runWithParameterSet(parameterSetName, overrides = {}) {
    const command = this.generateCommand(parameterSetName, overrides);
    console.log(`Running tests with parameter set ${parameterSetName}`);
    console.log(`Command: ${command}`);
    
    // Run command
    const startTime = Date.now();
    const result = spawnSync(command, { shell: true, stdio: 'inherit' });
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Create result object
    const testResult = {
      parameterSet: parameterSetName,
      command,
      exitCode: result.status,
      duration,
      timestamp: new Date().toISOString(),
      success: result.status === 0
    };
    
    // Save result
    this.saveTestResult(testResult, parameterSetName);
    
    return testResult;
  }

  /**
   * Save test result
   * @param {object} result - Test result
   * @param {string} parameterSetName - Parameter set name
   */
  saveTestResult(result, parameterSetName) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const resultFile = path.join(this.outputDir, `${parameterSetName}-${timestamp}.json`);
      fs.writeFileSync(resultFile, JSON.stringify(result, null, 2));
      console.log(`Test result saved to ${resultFile}`);
    } catch (error) {
      console.error('Failed to save test result:', error.message);
    }
  }

  /**
   * Run tests with multiple parameter sets
   * @param {Array<string>} parameterSetNames - Parameter set names
   * @returns {Array<object>} - Test results
   */
  runWithMultipleParameterSets(parameterSetNames) {
    const results = [];
    
    for (const parameterSetName of parameterSetNames) {
      try {
        const result = this.runWithParameterSet(parameterSetName);
        results.push(result);
      } catch (error) {
        console.error(`Failed to run tests with parameter set ${parameterSetName}:`, error.message);
        results.push({
          parameterSet: parameterSetName,
          error: error.message,
          success: false
        });
      }
    }
    
    return results;
  }

  /**
   * Run tests with parameter variations
   * @param {string} baseParameterSetName - Base parameter set name
   * @param {object} variations - Parameter variations
   * @returns {Array<object>} - Test results
   */
  runWithParameterVariations(baseParameterSetName, variations) {
    const results = [];
    const baseParameterSet = this.getParameterSet(baseParameterSetName);
    
    if (!baseParameterSet) {
      throw new Error(`Base parameter set ${baseParameterSetName} not found`);
    }
    
    // Generate all combinations of parameter variations
    const combinations = this.generateParameterCombinations(variations);
    
    // Run tests with each combination
    for (const combination of combinations) {
      const variationName = Object.entries(combination)
        .map(([key, value]) => `${key}-${value}`)
        .join('_');
      
      const parameterSetName = `${baseParameterSetName}_${variationName}`;
      
      try {
        const result = this.runWithParameterSet(baseParameterSetName, combination);
        result.variation = combination;
        results.push(result);
      } catch (error) {
        console.error(`Failed to run tests with parameter variation ${parameterSetName}:`, error.message);
        results.push({
          parameterSet: baseParameterSetName,
          variation: combination,
          error: error.message,
          success: false
        });
      }
    }
    
    return results;
  }

  /**
   * Generate all combinations of parameter variations
   * @param {object} variations - Parameter variations
   * @returns {Array<object>} - Parameter combinations
   */
  generateParameterCombinations(variations) {
    const keys = Object.keys(variations);
    const combinations = [{}];
    
    for (const key of keys) {
      const values = variations[key];
      const newCombinations = [];
      
      for (const combination of combinations) {
        for (const value of values) {
          newCombinations.push({
            ...combination,
            [key]: value
          });
        }
      }
      
      combinations.length = 0;
      combinations.push(...newCombinations);
    }
    
    return combinations;
  }

  /**
   * Run tests with data variations
   * @param {string} parameterSetName - Parameter set name
   * @param {object} dataVariations - Data variations
   * @returns {Array<object>} - Test results
   */
  runWithDataVariations(parameterSetName, dataVariations) {
    const results = [];
    
    for (const [variationName, variation] of Object.entries(dataVariations)) {
      // Generate test data
      const testData = {};
      
      for (const [dataType, count] of Object.entries(variation)) {
        testData[dataType] = this.testDataGenerator.generateBatch(dataType, count);
      }
      
      // Save test data
      const timestamp = Date.now();
      const testDataFile = `test-data-${variationName}-${timestamp}.json`;
      this.testDataGenerator.saveGeneratedData(testData, testDataFile);
      
      // Run tests with test data
      try {
        const result = this.runWithParameterSet(parameterSetName, {
          testData: testDataFile
        });
        
        result.variation = {
          name: variationName,
          data: variation
        };
        
        results.push(result);
      } catch (error) {
        console.error(`Failed to run tests with data variation ${variationName}:`, error.message);
        results.push({
          parameterSet: parameterSetName,
          variation: {
            name: variationName,
            data: variation
          },
          error: error.message,
          success: false
        });
      }
    }
    
    return results;
  }

  /**
   * Generate a test matrix
   * @param {object} dimensions - Matrix dimensions
   * @returns {Array<object>} - Test matrix
   */
  generateTestMatrix(dimensions) {
    const matrix = [];
    const dimensionKeys = Object.keys(dimensions);
    
    // Generate all combinations of dimension values
    const combinations = this.generateParameterCombinations(dimensions);
    
    // Create matrix entries
    for (const combination of combinations) {
      const entry = {
        parameters: combination,
        command: this.generateCommand('default', combination)
      };
      
      matrix.push(entry);
    }
    
    return matrix;
  }

  /**
   * Run a test matrix
   * @param {Array<object>} matrix - Test matrix
   * @returns {Array<object>} - Test results
   */
  runTestMatrix(matrix) {
    const results = [];
    
    for (const entry of matrix) {
      try {
        console.log(`Running test matrix entry: ${JSON.stringify(entry.parameters)}`);
        console.log(`Command: ${entry.command}`);
        
        // Run command
        const startTime = Date.now();
        const result = spawnSync(entry.command, { shell: true, stdio: 'inherit' });
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // Create result object
        const testResult = {
          parameters: entry.parameters,
          command: entry.command,
          exitCode: result.status,
          duration,
          timestamp: new Date().toISOString(),
          success: result.status === 0
        };
        
        results.push(testResult);
      } catch (error) {
        console.error(`Failed to run test matrix entry:`, error.message);
        results.push({
          parameters: entry.parameters,
          error: error.message,
          success: false
        });
      }
    }
    
    // Save matrix results
    this.saveMatrixResults(results);
    
    return results;
  }

  /**
   * Save matrix results
   * @param {Array<object>} results - Matrix results
   */
  saveMatrixResults(results) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const resultFile = path.join(this.outputDir, `matrix-${timestamp}.json`);
      fs.writeFileSync(resultFile, JSON.stringify(results, null, 2));
      console.log(`Matrix results saved to ${resultFile}`);
    } catch (error) {
      console.error('Failed to save matrix results:', error.message);
    }
  }
}

module.exports = TestParameterizer;
