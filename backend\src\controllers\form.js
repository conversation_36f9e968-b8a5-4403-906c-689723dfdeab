const { FormTemplate, FormTemplateVersion, Form<PERSON>ield, User, Sequelize } = require('../models');

// 获取所有表单模板
exports.getAllForms = async (req, res) => {
  try {
    const formTemplates = await FormTemplate.findAll({
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'full_name']
        },
        {
          model: FormField,
          as: 'fields',
          order: [['order_index', 'ASC']]
        }
      ],
      order: [['updated_at', 'DESC']]
    });

    res.json({
      success: true,
      data: formTemplates
    });
  } catch (error) {
    console.error('获取表单模板列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取单个表单模板
exports.getFormById = async (req, res) => {
  try {
    const { id } = req.params;

    const formTemplate = await FormTemplate.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'full_name']
        },
        {
          model: FormField,
          as: 'fields',
          order: [['order_index', 'ASC']]
        }
      ]
    });

    if (!formTemplate) {
      return res.status(404).json({
        success: false,
        message: '表单模板不存在'
      });
    }

    res.json({
      success: true,
      data: formTemplate
    });
  } catch (error) {
    console.error('获取表单模板详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 创建表单模板
exports.createForm = async (req, res) => {
  try {
    const { title, description, schema, fields } = req.body;

    // 创建表单模板
    const formTemplate = await FormTemplate.create({
      title,
      description,
      creator_id: req.user.id,
      version: 1,
      status: 'draft',
      schema
    });

    // 创建表单版本
    await FormTemplateVersion.create({
      form_template_id: formTemplate.id,
      version: 1,
      schema,
      creator_id: req.user.id
    });

    // 创建表单字段
    if (fields && fields.length > 0) {
      const formFields = fields.map((field, index) => ({
        form_template_id: formTemplate.id,
        field_key: field.field_key,
        field_type: field.field_type,
        label: field.label,
        placeholder: field.placeholder || null,
        default_value: field.default_value || null,
        options: field.options || null,
        validation_rules: field.validation_rules || null,
        is_required: field.is_required || false,
        order_index: index
      }));

      await FormField.bulkCreate(formFields);
    }

    // 获取创建后的完整表单模板
    const createdFormTemplate = await FormTemplate.findByPk(formTemplate.id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'full_name']
        },
        {
          model: FormField,
          as: 'fields',
          order: [['order_index', 'ASC']]
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: '表单模板创建成功',
      data: createdFormTemplate
    });
  } catch (error) {
    console.error('创建表单模板错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 更新表单模板
exports.updateForm = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, schema, fields, status } = req.body;

    const formTemplate = await FormTemplate.findByPk(id);
    if (!formTemplate) {
      return res.status(404).json({
        success: false,
        message: '表单模板不存在'
      });
    }

    // 检查权限
    if (formTemplate.creator_id !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    // 更新表单模板
    const newVersion = formTemplate.version + 1;
    await formTemplate.update({
      title: title || formTemplate.title,
      description: description !== undefined ? description : formTemplate.description,
      version: schema ? newVersion : formTemplate.version,
      status: status || formTemplate.status,
      schema: schema || formTemplate.schema
    });

    // 如果有新的schema，创建新版本
    if (schema) {
      await FormTemplateVersion.create({
        form_template_id: formTemplate.id,
        version: newVersion,
        schema,
        creator_id: req.user.id
      });
    }

    // 如果有新的字段，更新字段
    if (fields && fields.length > 0) {
      // 删除旧字段
      await FormField.destroy({
        where: { form_template_id: formTemplate.id }
      });

      // 创建新字段
      const formFields = fields.map((field, index) => ({
        form_template_id: formTemplate.id,
        field_key: field.field_key,
        field_type: field.field_type,
        label: field.label,
        placeholder: field.placeholder || null,
        default_value: field.default_value || null,
        options: field.options || null,
        validation_rules: field.validation_rules || null,
        is_required: field.is_required || false,
        order_index: index
      }));

      await FormField.bulkCreate(formFields);
    }

    // 获取更新后的完整表单模板
    const updatedFormTemplate = await FormTemplate.findByPk(formTemplate.id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'full_name']
        },
        {
          model: FormField,
          as: 'fields',
          order: [['order_index', 'ASC']]
        }
      ]
    });

    res.json({
      success: true,
      message: '表单模板更新成功',
      data: updatedFormTemplate
    });
  } catch (error) {
    console.error('更新表单模板错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 删除表单模板
exports.deleteForm = async (req, res) => {
  try {
    const { id } = req.params;

    const formTemplate = await FormTemplate.findByPk(id);
    if (!formTemplate) {
      return res.status(404).json({
        success: false,
        message: '表单模板不存在'
      });
    }

    // 检查权限
    if (formTemplate.creator_id !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    await formTemplate.destroy();

    res.json({
      success: true,
      message: '表单模板删除成功'
    });
  } catch (error) {
    console.error('删除表单模板错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取表单版本历史
exports.getFormVersions = async (req, res) => {
  try {
    const { id } = req.params;

    const formTemplate = await FormTemplate.findByPk(id);
    if (!formTemplate) {
      return res.status(404).json({
        success: false,
        message: '表单模板不存在'
      });
    }

    const versions = await FormTemplateVersion.findAll({
      where: { form_template_id: id },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'full_name']
        }
      ],
      order: [['version', 'DESC']]
    });

    res.json({
      success: true,
      data: versions
    });
  } catch (error) {
    console.error('获取表单版本历史错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 预览表单
exports.previewForm = async (req, res) => {
  try {
    const { id } = req.params;

    const formTemplate = await FormTemplate.findByPk(id, {
      include: [
        {
          model: FormField,
          as: 'fields',
          order: [['order_index', 'ASC']]
        }
      ]
    });

    if (!formTemplate) {
      return res.status(404).json({
        success: false,
        message: '表单模板不存在'
      });
    }

    res.json({
      success: true,
      data: {
        id: formTemplate.id,
        title: formTemplate.title,
        description: formTemplate.description,
        schema: formTemplate.schema,
        fields: formTemplate.fields
      }
    });
  } catch (error) {
    console.error('预览表单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};
