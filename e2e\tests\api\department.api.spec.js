/**
 * API Tests for Department Endpoints
 */

const { test, expect } = require('@playwright/test');
const ApiTestClient = require('../../utils/api-test-client');

test.describe('Department API Tests', () => {
  let apiClient;
  let authToken;
  let createdDepartmentId;

  test.beforeAll(async () => {
    // Login to get auth token
    apiClient = new ApiTestClient();
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };

    const loginResponse = await apiClient.post('/users/login', loginData);
    authToken = loginResponse.data.data.token;
    apiClient.setAuthToken(authToken);
  });

  test('should get all departments', async () => {
    const response = await apiClient.get('/departments');
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify departments data
    expect(Array.isArray(response.data.data)).toBe(true);
  });

  test('should create a new department', async () => {
    const departmentData = {
      name: `API Test Department ${Date.now()}`,
      code: `API_TEST_${Date.now()}`,
      description: 'Department created by API test',
      status: 'active'
    };

    const response = await apiClient.post('/departments', departmentData);
    
    // Verify response
    apiClient.verifyStatus(response, 201);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify department data
    expect(response.data.data.name).toBe(departmentData.name);
    expect(response.data.data.code).toBe(departmentData.code);
    
    // Save department ID for later tests
    createdDepartmentId = response.data.data.id;
  });

  test('should get department by ID', async () => {
    // Skip if no department was created
    test.skip(!createdDepartmentId, 'No department created');
    
    const response = await apiClient.get(`/departments/${createdDepartmentId}`);
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    apiClient.verifySchema(response.data, ['success', 'data']);
    
    // Verify department data
    expect(response.data.data.id).toBe(createdDepartmentId);
  });

  test('should update department', async () => {
    // Skip if no department was created
    test.skip(!createdDepartmentId, 'No department created');
    
    const updateData = {
      description: `Updated description ${Date.now()}`
    };

    const response = await apiClient.put(`/departments/${createdDepartmentId}`, updateData);
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    
    // Verify department data was updated
    expect(response.data.data.description).toBe(updateData.description);
  });

  test('should fail to create department with duplicate code', async () => {
    // Skip if no department was created
    test.skip(!createdDepartmentId, 'No department created');
    
    // Get the created department to get its code
    const getResponse = await apiClient.get(`/departments/${createdDepartmentId}`);
    const existingCode = getResponse.data.data.code;
    
    const departmentData = {
      name: `Duplicate Department ${Date.now()}`,
      code: existingCode,
      description: 'Department with duplicate code',
      status: 'active'
    };

    const response = await apiClient.post('/departments', departmentData);
    
    // Verify response
    apiClient.verifyStatus(response, 400);
    apiClient.verifyError(response);
  });

  test('should delete department', async () => {
    // Skip if no department was created
    test.skip(!createdDepartmentId, 'No department created');
    
    const response = await apiClient.delete(`/departments/${createdDepartmentId}`);
    
    // Verify response
    apiClient.verifyStatus(response, 200);
    apiClient.verifySuccess(response);
    
    // Verify department was deleted
    const getResponse = await apiClient.get(`/departments/${createdDepartmentId}`);
    apiClient.verifyStatus(getResponse, 404);
  });
});
