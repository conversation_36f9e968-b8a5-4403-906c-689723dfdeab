'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('workflow_transitions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_templates',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      source_node_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_nodes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      target_node_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_nodes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      condition: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 添加索引
    await queryInterface.addIndex('workflow_transitions', ['workflow_template_id']);
    await queryInterface.addIndex('workflow_transitions', ['source_node_id']);
    await queryInterface.addIndex('workflow_transitions', ['target_node_id']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('workflow_transitions');
  }
};
