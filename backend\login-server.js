/**
 * 独立登录服务器
 * 仅用于处理登录请求
 */
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { Sequelize } = require('sequelize');

const app = express();
const PORT = 3003;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 简单日志中间件
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`);
  console.log('请求体:', req.body);
  next();
});

// 创建数据库连接
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USERNAME,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false
  }
);

// 定义用户模型
const User = sequelize.define('User', {
  username: {
    type: Sequelize.STRING,
    allowNull: false,
    unique: true
  },
  password: {
    type: Sequelize.STRING,
    allowNull: false
  },
  email: {
    type: Sequelize.STRING,
    allowNull: false,
    unique: true
  },
  full_name: {
    type: Sequelize.STRING,
    allowNull: false
  },
  status: {
    type: Sequelize.STRING,
    defaultValue: 'active'
  },
  last_login: {
    type: Sequelize.DATE
  }
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 定义角色模型
const Role = sequelize.define('Role', {
  name: {
    type: Sequelize.STRING,
    allowNull: false,
    unique: true
  },
  display_name: {
    type: Sequelize.STRING
  },
  description: {
    type: Sequelize.TEXT
  }
}, {
  tableName: 'roles',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 定义用户角色关联模型
const UserRole = sequelize.define('UserRole', {
}, {
  tableName: 'user_roles',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 设置关联关系
User.belongsToMany(Role, { through: UserRole, as: 'roles', foreignKey: 'user_id' });
Role.belongsToMany(User, { through: UserRole, as: 'users', foreignKey: 'role_id' });

// 登录路由
app.post('/api/users/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('处理登录请求:', username);

    // 查找用户
    const user = await User.findOne({
      where: { username },
      include: [{ model: Role, as: 'roles' }]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码不正确'
      });
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '账户已被禁用，请联系管理员'
      });
    }

    // 验证密码
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码不正确'
      });
    }

    // 生成JWT令牌
    const payload = {
      user: {
        id: user.id,
        username: user.username,
        roles: user.roles.map(role => role.name)
      }
    };

    const token = jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    // 更新最后登录时间
    await user.update({ last_login: new Date() });

    res.json({
      success: true,
      message: '登录成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        roles: user.roles.map(role => role.name)
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 测试路由
app.get('/api/test', (req, res) => {
  res.json({ message: '测试路由正常工作' });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`独立登录服务器运行在 http://localhost:${PORT}`);
});
