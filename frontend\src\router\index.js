import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import(/* webpackChunkName: "login" */ '../views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Home',
    component: () => import(/* webpackChunkName: "home" */ '../views/Home.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/department',
    name: 'Department',
    component: () => import(/* webpackChunkName: "department" */ '../views/department/Index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/members',
    name: 'Members',
    component: () => import(/* webpackChunkName: "members" */ '../views/department/Members.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/form-design',
    name: 'FormDesign',
    component: () => import(/* webpackChunkName: "form-design" */ '../views/form-design/Index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/workflow-design',
    name: 'WorkflowDesign',
    component: () => import(/* webpackChunkName: "workflow-design" */ '../views/workflow-design/Index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/workflow-form',
    name: 'WorkflowForm',
    component: () => import(/* webpackChunkName: "workflow-form" */ '../views/workflow-form/Index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/workflow-process',
    name: 'WorkflowProcess',
    component: () => import(/* webpackChunkName: "workflow-process" */ '../views/workflow-process/Index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/workflow-process/:id',
    name: 'WorkflowProcessDetail',
    component: () => import(/* webpackChunkName: "workflow-process-detail" */ '../views/workflow-process/Detail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/test-analytics',
    name: 'TestAnalytics',
    component: () => import(/* webpackChunkName: "test-analytics" */ '../views/test-analytics/Index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/test-analytics/runs/:id',
    name: 'TestRunDetails',
    component: () => import(/* webpackChunkName: "test-run-details" */ '../views/test-analytics/TestRunDetails.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import(/* webpackChunkName: "profile" */ '../views/Profile.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/change-password',
    name: 'ChangePassword',
    component: () => import(/* webpackChunkName: "change-password" */ '../views/ChangePassword.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/test-form-display',
    name: 'TestFormDisplay',
    component: () => import(/* webpackChunkName: "test-form-display" */ '../views/TestFormDisplay.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const token = localStorage.getItem('token')

  if (requiresAuth && !token) {
    next('/login')
  } else if (to.path === '/login' && token) {
    next('/')
  } else {
    next()
  }
})

export default router
