'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('workflow_task_histories', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      workflow_instance_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_instances',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      task_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_tasks',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      node_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'workflow_nodes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      operator_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      operation: {
        type: Sequelize.STRING(20),
        allowNull: false
      },
      comments: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      form_data: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 添加索引
    await queryInterface.addIndex('workflow_task_histories', ['workflow_instance_id']);
    await queryInterface.addIndex('workflow_task_histories', ['task_id']);
    await queryInterface.addIndex('workflow_task_histories', ['node_id']);
    await queryInterface.addIndex('workflow_task_histories', ['operator_id']);
    await queryInterface.addIndex('workflow_task_histories', ['operation']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('workflow_task_histories');
  }
};
