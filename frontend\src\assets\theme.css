:root {
  /* 主色调 - 更现代的蓝色 */
  --primary-color: #2563eb;
  --primary-color-light: #3b82f6;
  --primary-color-dark: #1d4ed8;
  --primary-color-bg: rgba(37, 99, 235, 0.1);

  /* 辅助色 - 更鲜明的色彩 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* 中性色 - 更柔和的灰度 */
  --text-color: #1f2937;
  --text-color-secondary: #4b5563;
  --text-color-disabled: #9ca3af;
  --border-color: #e5e7eb;
  --border-color-split: #f3f4f6;
  --background-color: #f9fafb;
  --background-color-light: #ffffff;
  --background-color-dark: #f3f4f6;

  /* 阴影 - 更精细的阴影效果 */
  --box-shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-card: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-dropdown: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 圆角 - 更现代的圆角 */
  --border-radius-base: 6px;
  --border-radius-sm: 4px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  /* 字体 */
  --font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  --font-size-base: 14px;
  --font-size-sm: 12px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 动画 */
  --animation-duration-base: 0.2s;
  --animation-duration-slow: 0.3s;
  --animation-duration-slower: 0.5s;
  --animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  --spacing-3xl: 48px;
}

/* 暗黑模式 */
.dark-mode {
  /* 主色调 - 更亮的蓝色以在暗色背景上更加突出 */
  --primary-color: #3b82f6;
  --primary-color-light: #60a5fa;
  --primary-color-dark: #2563eb;
  --primary-color-bg: rgba(59, 130, 246, 0.15);

  /* 辅助色 - 调整为暗色模式下更适合的亮度 */
  --success-color: #34d399;
  --warning-color: #fbbf24;
  --error-color: #f87171;
  --info-color: #60a5fa;

  /* 中性色 - 更舒适的暗色调 */
  --text-color: #f9fafb;
  --text-color-secondary: #e5e7eb;
  --text-color-disabled: #9ca3af;
  --border-color: #374151;
  --border-color-split: #1f2937;
  --background-color: #111827;
  --background-color-light: #1f2937;
  --background-color-dark: #0f172a;

  /* 阴影 - 暗色模式下更强的阴影 */
  --box-shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --box-shadow-card: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --box-shadow-dropdown: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* 全局样式 */
body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  color: var(--text-color);
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-color);
  border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.dark-mode ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.dark-mode ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 卡片样式 */
.el-card {
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color-split);
  box-shadow: var(--box-shadow-card) !important;
  margin-bottom: var(--spacing-xl);
  transition: all 0.3s var(--animation-timing-function);
  overflow: hidden;
}

.el-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--box-shadow-dropdown) !important;
}

.el-card__header {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-weight: var(--font-weight-medium);
  border-bottom: 1px solid var(--border-color-split);
}

.el-card__body {
  padding: var(--spacing-xl);
}

/* 按钮样式 */
.el-button {
  border-radius: var(--border-radius-base);
  transition: all 0.3s;
  font-weight: var(--font-weight-medium);
}

.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-button--primary:hover,
.el-button--primary:focus {
  background-color: var(--primary-color-light);
  border-color: var(--primary-color-light);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.2);
}

.el-button--primary:active {
  background-color: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
  transform: translateY(0);
}

.el-button--success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.el-button--warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.el-button--danger {
  background-color: var(--error-color);
  border-color: var(--error-color);
}

/* 表格样式 */
.el-table {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--box-shadow-base);
}

.el-table th {
  background-color: var(--background-color-light) !important;
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
}

.el-table td {
  padding: var(--spacing-md) 0;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: var(--background-color-dark);
}

.el-table__row:hover > td {
  background-color: var(--primary-color-bg) !important;
}

/* 表单样式 */
.el-form-item__label {
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.el-form-item {
  margin-bottom: var(--spacing-xl);
}

/* 输入框样式 */
.el-input__inner {
  border-radius: var(--border-radius-base);
  padding: var(--spacing-sm) var(--spacing-md);
  border-color: var(--border-color);
  transition: all 0.3s;
}

.el-input__inner:hover {
  border-color: var(--primary-color-light);
}

.el-input__inner:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color-bg);
}

/* 标签页样式 */
.el-tabs__item {
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
  padding: 0 var(--spacing-xl);
  height: 48px;
  line-height: 48px;
  transition: all 0.3s;
}

.el-tabs__item:hover {
  color: var(--primary-color-light);
}

.el-tabs__item.is-active {
  color: var(--primary-color);
}

.el-tabs__active-bar {
  background-color: var(--primary-color);
  height: 3px;
  border-radius: var(--border-radius-full);
}

.el-tabs__nav-wrap::after {
  height: 1px;
  background-color: var(--border-color-split);
}

/* 分页样式 */
.el-pagination {
  margin-top: var(--spacing-xl);
  justify-content: center;
}

.el-pagination.is-background .el-pager li {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-base);
  margin: 0 var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s;
}

.el-pagination.is-background .el-pager li:not(.disabled):hover {
  background-color: var(--primary-color-bg);
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

/* 下拉菜单 */
.el-dropdown-menu {
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-dropdown);
  padding: var(--spacing-sm) 0;
}

.el-dropdown-menu__item {
  padding: var(--spacing-sm) var(--spacing-xl);
  font-size: var(--font-size-base);
  line-height: 1.5;
}

.el-dropdown-menu__item:hover {
  background-color: var(--primary-color-bg);
  color: var(--primary-color);
}

/* 弹窗样式 */
.el-dialog {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-dropdown);
  overflow: hidden;
}

.el-dialog__header {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-color-split);
}

.el-dialog__title {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  color: var(--text-color);
}

.el-dialog__body {
  padding: var(--spacing-xl);
}

.el-dialog__footer {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--border-color-split);
}

/* 通知和消息 */
.el-message {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-dropdown);
  padding: var(--spacing-md) var(--spacing-xl);
}

.el-notification {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-dropdown);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .el-form-item {
    margin-bottom: var(--spacing-lg);
  }

  .el-form-item__label {
    padding: 0 0 var(--spacing-sm);
    line-height: 1.5;
  }

  .el-form--label-top .el-form-item__label {
    padding: 0 0 var(--spacing-sm);
  }

  .el-card__body {
    padding: var(--spacing-lg);
  }

  .el-dialog__body {
    padding: var(--spacing-lg);
  }
}
