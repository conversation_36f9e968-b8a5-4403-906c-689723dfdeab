const { TestRun, TestResult, TestMetric } = require('../models');
const { Op } = require('sequelize');
const testAnalyticsService = require('../services/test-analytics.service');

/**
 * Get test analytics data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTestAnalytics = async (req, res) => {
  try {
    const { startDate, endDate, testType } = req.query;
    
    // Build filter conditions
    const where = {};
    
    if (startDate && endDate) {
      where.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      where.createdAt = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      where.createdAt = {
        [Op.lte]: new Date(endDate)
      };
    }
    
    if (testType) {
      where.testType = testType;
    }
    
    // Get analytics data
    const analyticsData = await testAnalyticsService.getAnalyticsData(where);
    
    res.json({
      success: true,
      data: analyticsData
    });
  } catch (error) {
    console.error('Error getting test analytics:', error);
    res.status(500).json({
      success: false,
      message: '获取测试分析数据失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get test summary
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTestSummary = async (req, res) => {
  try {
    // Get summary data
    const summaryData = await testAnalyticsService.getSummaryData();
    
    res.json({
      success: true,
      data: summaryData
    });
  } catch (error) {
    console.error('Error getting test summary:', error);
    res.status(500).json({
      success: false,
      message: '获取测试分析摘要失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get test runs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTestRuns = async (req, res) => {
  try {
    const { page = 1, limit = 10, testType } = req.query;
    
    // Build filter conditions
    const where = {};
    
    if (testType) {
      where.testType = testType;
    }
    
    // Get paginated test runs
    const testRuns = await testAnalyticsService.getPaginatedTestRuns(
      where,
      parseInt(page),
      parseInt(limit)
    );
    
    res.json({
      success: true,
      data: testRuns
    });
  } catch (error) {
    console.error('Error getting test runs:', error);
    res.status(500).json({
      success: false,
      message: '获取测试运行列表失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get test run by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTestRunById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get test run by ID
    const testRun = await testAnalyticsService.getTestRunById(id);
    
    if (!testRun) {
      return res.status(404).json({
        success: false,
        message: '测试运行不存在'
      });
    }
    
    res.json({
      success: true,
      data: testRun
    });
  } catch (error) {
    console.error('Error getting test run:', error);
    res.status(500).json({
      success: false,
      message: '获取测试运行详情失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Create test run
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createTestRun = async (req, res) => {
  try {
    const testRunData = req.body;
    
    // Create test run
    const testRun = await testAnalyticsService.createTestRun(testRunData);
    
    res.status(201).json({
      success: true,
      data: testRun
    });
  } catch (error) {
    console.error('Error creating test run:', error);
    res.status(500).json({
      success: false,
      message: '提交测试运行结果失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get test trends
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTestTrends = async (req, res) => {
  try {
    const { days = 7, testType } = req.query;
    
    // Get trend data
    const trendData = await testAnalyticsService.getTrendData(
      parseInt(days),
      testType
    );
    
    res.json({
      success: true,
      data: trendData
    });
  } catch (error) {
    console.error('Error getting test trends:', error);
    res.status(500).json({
      success: false,
      message: '获取测试趋势数据失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
