const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     FormField:
 *       type: object
 *       required:
 *         - form_template_id
 *         - field_key
 *         - field_type
 *         - label
 *       properties:
 *         id:
 *           type: integer
 *           description: 字段ID
 *           example: 1
 *         form_template_id:
 *           type: integer
 *           description: 所属表单模板ID
 *           example: 1
 *         field_key:
 *           type: string
 *           description: 字段唯一标识
 *           example: "leave_type"
 *         field_type:
 *           type: string
 *           description: 字段类型
 *           example: "select"
 *         label:
 *           type: string
 *           description: 字段标签
 *           example: "请假类型"
 *         placeholder:
 *           type: string
 *           description: 占位文本
 *           example: "请选择请假类型"
 *         default_value:
 *           type: string
 *           description: 默认值
 *           example: "事假"
 *         options:
 *           type: object
 *           description: 选项配置（用于下拉框、单选框等）
 *           example: [{"label": "事假", "value": "personal"}, {"label": "病假", "value": "sick"}]
 *         validation_rules:
 *           type: object
 *           description: 验证规则
 *           example: {"required": true, "min": 1, "max": 100}
 *         is_required:
 *           type: boolean
 *           description: 是否必填
 *           example: true
 *         order_index:
 *           type: integer
 *           description: 排序索引
 *           example: 0
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 *       example:
 *         id: 1
 *         form_template_id: 1
 *         field_key: "leave_type"
 *         field_type: "select"
 *         label: "请假类型"
 *         placeholder: "请选择请假类型"
 *         default_value: "事假"
 *         options: [{"label": "事假", "value": "personal"}, {"label": "病假", "value": "sick"}]
 *         validation_rules: {"required": true}
 *         is_required: true
 *         order_index: 0
 *         created_at: "2023-06-01T10:00:00Z"
 *         updated_at: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class FormField extends Model {
    static associate(models) {
      // 表单模板
      FormField.belongsTo(models.FormTemplate, {
        foreignKey: 'form_template_id',
        as: 'formTemplate'
      });
    }
  }

  FormField.init({
    form_template_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'form_templates',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    field_key: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    field_type: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    label: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    placeholder: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    default_value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    options: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    validation_rules: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    is_required: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    order_index: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    }
  }, {
    sequelize,
    modelName: 'FormField',
    tableName: 'form_fields',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return FormField;
};
