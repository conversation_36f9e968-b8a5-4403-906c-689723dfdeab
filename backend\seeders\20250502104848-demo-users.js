'use strict';
const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const hashedPassword = await bcrypt.hash('password123', 10);

    await queryInterface.bulkInsert('users', [
      {
        username: 'admin',
        password: hashedPassword,
        email: '<EMAIL>',
        full_name: '系统管理员',
        avatar: null,
        phone: '13800000000',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        username: 'manager',
        password: hashedPassword,
        email: '<EMAIL>',
        full_name: '部门经理',
        avatar: null,
        phone: '13800000001',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        username: 'employee',
        password: hashedPassword,
        email: '<EMAIL>',
        full_name: '普通员工',
        avatar: null,
        phone: '13800000002',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.bulkDelete('users', null, {});
  }
};
