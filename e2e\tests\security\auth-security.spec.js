/**
 * Security Tests for Authentication
 * Tests for common authentication vulnerabilities
 */

const { test, expect } = require('@playwright/test');
const ApiTestClient = require('../../utils/api-test-client');

test.describe('Authentication Security Tests', () => {
  let apiClient;

  test.beforeEach(() => {
    apiClient = new ApiTestClient();
  });

  test('should prevent SQL injection in login', async () => {
    const sqlInjectionAttempts = [
      { username: "' OR 1=1 --", password: "password" },
      { username: "admin' --", password: "anything" },
      { username: "admin'; DROP TABLE users; --", password: "password" },
      { username: "admin", password: "' OR 1=1 --" },
      { username: "admin", password: "password' OR '1'='1" }
    ];

    for (const attempt of sqlInjectionAttempts) {
      const response = await apiClient.post('/users/login', attempt);
      
      // Verify login failed
      expect(response.success).toBe(false);
      expect(response.status).not.toBe(200);
    }
  });

  test('should prevent brute force attacks', async () => {
    const maxAttempts = 10;
    let lockoutDetected = false;
    
    // Try multiple login attempts with wrong password
    for (let i = 0; i < maxAttempts; i++) {
      const response = await apiClient.post('/users/login', {
        username: 'admin',
        password: `wrong_password_${i}`
      });
      
      // If we get a different response (like 429 Too Many Requests),
      // it might indicate rate limiting or account lockout
      if (response.status === 429 || 
          (response.data && response.data.message && 
           response.data.message.includes('locked'))) {
        lockoutDetected = true;
        break;
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Note: This test is informational - we're not failing if lockout isn't detected
    // because some systems might use other protection mechanisms
    console.log(`Brute force protection: ${lockoutDetected ? 'Detected' : 'Not detected'}`);
  });

  test('should use secure cookies', async () => {
    // Login to get cookies
    const loginResponse = await apiClient.post('/users/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    // Skip if login failed
    test.skip(!loginResponse.success, 'Login failed');
    
    // Check for secure cookies in response headers
    const cookies = loginResponse.headers['set-cookie'];
    
    // If no cookies are set, this might be using localStorage instead
    if (!cookies) {
      console.log('No cookies set, might be using localStorage or other mechanism');
      return;
    }
    
    // Check cookie security attributes
    let hasSecureFlag = false;
    let hasHttpOnlyFlag = false;
    let hasSameSiteFlag = false;
    
    for (const cookie of cookies) {
      hasSecureFlag = hasSecureFlag || cookie.includes('Secure');
      hasHttpOnlyFlag = hasHttpOnlyFlag || cookie.includes('HttpOnly');
      hasSameSiteFlag = hasSameSiteFlag || cookie.includes('SameSite');
    }
    
    console.log(`Cookie security: Secure=${hasSecureFlag}, HttpOnly=${hasHttpOnlyFlag}, SameSite=${hasSameSiteFlag}`);
  });

  test('should validate token expiration', async ({ page }) => {
    // Login via UI to get token
    await page.goto('/login');
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'admin123');
    await page.click('.login-button');
    await page.waitForURL(/.*\//);
    
    // Get token from localStorage
    const token = await page.evaluate(() => localStorage.getItem('token'));
    
    // Skip if no token found
    test.skip(!token, 'No token found');
    
    // Verify token structure (should be JWT)
    expect(token).toMatch(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/);
    
    // Try to decode token to check expiration
    const tokenParts = token.split('.');
    let payload;
    
    try {
      payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
    } catch (error) {
      console.log('Could not decode token payload');
      return;
    }
    
    // Check if token has expiration
    expect(payload).toHaveProperty('exp');
    
    // Verify expiration is in the future
    const expirationDate = new Date(payload.exp * 1000);
    const now = new Date();
    expect(expirationDate > now).toBe(true);
    
    // Verify token is not valid for too long (max 24 hours)
    const maxExpirationMs = 24 * 60 * 60 * 1000; // 24 hours
    expect(expirationDate - now).toBeLessThanOrEqual(maxExpirationMs);
  });

  test('should protect against CSRF', async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'admin123');
    await page.click('.login-button');
    await page.waitForURL(/.*\//);
    
    // Get token from localStorage
    const token = await page.evaluate(() => localStorage.getItem('token'));
    
    // Skip if no token found
    test.skip(!token, 'No token found');
    
    // Create API client with token
    apiClient.setAuthToken(token);
    
    // Make a request and check for CSRF protection
    const response = await apiClient.get('/users/me');
    
    // Check if API requires CSRF token
    const csrfHeader = response.headers['x-csrf-token'] || 
                      response.headers['csrf-token'] ||
                      response.headers['x-xsrf-token'];
    
    // If using JWT in Authorization header, CSRF might not be needed
    // This is informational only
    console.log(`CSRF protection: ${csrfHeader ? 'Detected' : 'Not detected (might be using JWT)'}`);
  });
});
