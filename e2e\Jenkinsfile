pipeline {
    agent {
        docker {
            image 'mcr.microsoft.com/playwright:v1.40.0-focal'
            args '-v /var/run/docker.sock:/var/run/docker.sock'
        }
    }
    
    environment {
        NODE_VERSION = '20'
        PLAYWRIGHT_VERSION = '1.40.0'
        CI = 'true'
    }
    
    parameters {
        choice(
            name: 'TEST_TYPE',
            choices: ['smoke', 'regression', 'api', 'a11y', 'security', 'visual', 'all'],
            description: 'Type of tests to run'
        )
        string(
            name: 'PARALLEL',
            defaultValue: '2',
            description: 'Number of parallel workers'
        )
        string(
            name: 'RETRIES',
            defaultValue: '1',
            description: 'Number of retries for failed tests'
        )
        booleanParam(
            name: 'CLEAN_WORKSPACE',
            defaultValue: false,
            description: 'Clean workspace before build'
        )
    }
    
    options {
        timeout(time: 1, unit: 'HOURS')
        ansiColor('xterm')
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Setup') {
            steps {
                sh 'node --version'
                sh 'npm --version'
                
                dir('frontend') {
                    sh 'npm ci'
                }
                
                dir('backend') {
                    sh 'npm ci'
                }
                
                dir('e2e') {
                    sh 'npm ci'
                }
            }
        }
        
        stage('Start Services') {
            steps {
                dir('e2e') {
                    sh 'docker-compose -f docker-compose.test.yml up -d postgres backend frontend'
                    sh 'sleep 10' // Wait for services to start
                }
            }
        }
        
        stage('Run Tests') {
            steps {
                dir('e2e') {
                    script {
                        def testCommand = "node ci-test.js --${params.TEST_TYPE} --parallel=${params.PARALLEL} --retries=${params.RETRIES} --reporter=list,junit,html"
                        
                        try {
                            sh "npx wait-on http://frontend:8080 -t 60000"
                            sh testCommand
                        } finally {
                            sh "node analyze-results.js --html --json --trend"
                        }
                    }
                }
            }
        }
        
        stage('Generate Reports') {
            steps {
                dir('e2e') {
                    junit 'test-results/**/*.xml'
                    publishHTML(target: [
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'playwright-report',
                        reportFiles: 'index.html',
                        reportName: 'Playwright Report'
                    ])
                    publishHTML(target: [
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'ci-reports',
                        reportFiles: '**/analysis-report.html',
                        reportName: 'Analysis Report'
                    ])
                }
            }
        }
    }
    
    post {
        always {
            dir('e2e') {
                sh 'docker-compose -f docker-compose.test.yml down -v'
            }
            
            archiveArtifacts artifacts: 'e2e/test-results/**/*', allowEmptyArchive: true
            archiveArtifacts artifacts: 'e2e/playwright-report/**/*', allowEmptyArchive: true
            archiveArtifacts artifacts: 'e2e/ci-reports/**/*', allowEmptyArchive: true
        }
        
        success {
            echo 'Tests completed successfully!'
        }
        
        failure {
            echo 'Tests failed!'
            
            mail to: '<EMAIL>',
                 subject: "Failed Pipeline: ${currentBuild.fullDisplayName}",
                 body: "Something is wrong with ${env.BUILD_URL}"
        }
        
        cleanup {
            cleanWs(cleanWhenNotBuilt: false,
                    deleteDirs: true,
                    disableDeferredWipeout: true,
                    notFailBuild: true,
                    patterns: [[pattern: 'e2e/node_modules', type: 'EXCLUDE']])
        }
    }
}
