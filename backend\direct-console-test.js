/**
 * Direct console test
 * Run with: node direct-console-test.js
 */

// 使用console.log
console.log('使用console.log打印消息');
console.log('这是第二行消息');
console.log('这是第三行消息');

// 使用process.stdout.write
process.stdout.write('使用process.stdout.write打印消息\n');
process.stdout.write('这是第二行消息\n');
process.stdout.write('这是第三行消息\n');

// 使用console.error
console.error('使用console.error打印错误消息');
console.error('这是第二行错误消息');
console.error('这是第三行错误消息');

// 使用process.stderr.write
process.stderr.write('使用process.stderr.write打印错误消息\n');
process.stderr.write('这是第二行错误消息\n');
process.stderr.write('这是第三行错误消息\n');
