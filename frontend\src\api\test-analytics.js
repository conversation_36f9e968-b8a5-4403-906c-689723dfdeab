import axios from 'axios';

const API_URL = '/api/test-analytics';

/**
 * 获取测试分析数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回测试分析数据
 */
export const getTestAnalytics = (params = {}) => {
  return axios.get(API_URL, { params });
};

/**
 * 获取测试分析摘要
 * @returns {Promise} - 返回测试分析摘要
 */
export const getTestSummary = () => {
  return axios.get(`${API_URL}/summary`);
};

/**
 * 获取测试运行列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回测试运行列表
 */
export const getTestRuns = (params = {}) => {
  return axios.get(`${API_URL}/runs`, { params });
};

/**
 * 获取测试运行详情
 * @param {string} id - 测试运行ID
 * @returns {Promise} - 返回测试运行详情
 */
export const getTestRunById = (id) => {
  return axios.get(`${API_URL}/runs/${id}`);
};

/**
 * 提交测试运行结果
 * @param {Object} data - 测试运行数据
 * @returns {Promise} - 返回创建的测试运行
 */
export const createTestRun = (data) => {
  return axios.post(`${API_URL}/runs`, data);
};

/**
 * 获取测试趋势数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回测试趋势数据
 */
export const getTestTrends = (params = {}) => {
  return axios.get(`${API_URL}/trends`, { params });
};
