# 工作流系统 E2E 测试

本目录包含工作流系统的端到端 (E2E) 测试，使用 Playwright 框架实现。

## 测试结构

测试按照功能模块和用户角色组织：

### 按功能模块

- **基础测试**
  - `login.spec.js` - 登录功能测试
  - `home.spec.js` - 首页功能测试

- **核心功能测试**
  - `department.spec.js` - 部门管理测试
  - `form-design.spec.js` - 表单设计器测试
  - `workflow-design.spec.js` - 工作流设计测试
  - `workflow-form.spec.js` - 工作流填写测试
  - `workflow-process.spec.js` - 工作流流转测试

- **高级功能测试**
  - `permissions.spec.js` - 用户权限测试
  - `form-validation.spec.js` - 表单验证测试
  - `workflow-lifecycle.spec.js` - 工作流生命周期测试

- **质量保障测试**
  - `error-handling.spec.js` - 错误处理测试
  - `performance.spec.js` - 性能测试
  - `responsive.spec.js` - 响应式设计测试
  - `visual-regression.spec.js` - 视觉回归测试

- **API 测试**
  - `api/auth.api.spec.js` - 认证 API 测试
  - `api/department.api.spec.js` - 部门 API 测试
  - `api/workflow.api.spec.js` - 工作流 API 测试

- **无障碍测试**
  - `accessibility/core-pages.a11y.spec.js` - 核心页面无障碍测试

- **安全测试**
  - `security/auth-security.spec.js` - 认证安全测试
  - `security/api-security.spec.js` - API 安全测试

- **负载测试**
  - `load/workflow-load.spec.js` - 工作流负载测试

### 按用户角色

- **管理员测试**
  - 所有不带特殊后缀的测试文件默认以管理员身份运行

- **普通用户测试**
  - `*.user.spec.js` - 以普通用户身份运行的测试
  - 例如：`workflow-form.user.spec.js`

- **移动设备测试**
  - `*.mobile.spec.js` - 在移动设备上运行的测试
  - 例如：`mobile-navigation.mobile.spec.js`

### 特殊测试

- `global.setup.js` - 全局设置验证测试，在其他测试之前运行

## 辅助工具和实用程序

- **测试辅助工具**
  - `utils/test-helpers.js` - 基本测试辅助函数
  - `utils/test-data-manager.js` - 测试数据管理工具

- **测试运行工具**
  - `run-tests.js` - 本地测试运行脚本
  - `ci-test.js` - CI/CD 环境测试运行脚本
  - `debug-workflow-api.js` - API 调试工具

- **全局设置和清理**
  - `global-setup.js` - 全局测试设置脚本
  - `global-teardown.js` - 全局测试清理脚本

## 运行测试

### 前提条件

1. 确保已安装所有依赖：
   ```bash
   npm install
   npx playwright install --with-deps
   ```

2. 确保前端和后端服务已启动：
   ```bash
   # 在另一个终端中启动前端服务
   cd ../frontend
   npm run dev

   # 在另一个终端中启动后端服务
   cd ../backend
   npm run dev
   ```

### 运行所有测试

```bash
npm test
```

### 运行特定测试组

```bash
node run-tests.js basic    # 运行基础测试
node run-tests.js core     # 运行核心功能测试
node run-tests.js advanced # 运行高级功能测试
node run-tests.js quality  # 运行质量保障测试
```

### 运行特定类型的测试

```bash
# 运行 API 测试
npm run test:api

# 运行无障碍测试
npm run test:a11y

# 运行安全测试
npm run test:security

# 运行负载测试
npm run test:load
```

### 运行高级测试

```bash
# 运行基本高级测试
npm run test:advanced

# 运行带性能监控和报告的高级测试
npm run test:advanced:monitor

# 运行完整的高级测试套件（所有测试、监控、报告和通知）
npm run test:advanced:full

# 自定义高级测试运行
node advanced-test-runner.js --type=smoke --parallel=4 --monitor --report
```

### 运行特定用户角色的测试

```bash
# 运行管理员测试
npx playwright test --project=admin

# 运行普通用户测试
npx playwright test --project=user

# 运行移动设备测试
npx playwright test --project=mobile
```

### 运行单个测试文件

```bash
node run-tests.js login.spec.js
```

### 其他运行选项

```bash
# 在有界面的浏览器中运行测试
npm run test:headed

# 在调试模式下运行测试
npm run test:debug

# 使用 Playwright UI 运行测试
npm run test:ui
```

### 在 CI/CD 环境中运行测试

```bash
# 运行冒烟测试
npm run test:smoke

# 运行回归测试
npm run test:regression

# 运行视觉回归测试
npm run test:visual

# 运行所有测试
node ci-test.js --all
```

## 高级功能

### 测试数据管理

测试使用 `TestDataAPI` 类来管理测试数据：

```javascript
const TestDataAPI = require('./utils/test-data-api');

// 创建测试数据管理器
const testDataApi = new TestDataAPI({
  baseURL: 'http://localhost:5273/api',
  authToken: 'your-auth-token'
});

// 创建测试数据
const department = await testDataApi.createDepartment();
const form = await testDataApi.createForm();
const workflow = await testDataApi.createWorkflow();
const instance = await testDataApi.startWorkflowInstance(workflow.id);

// 导出测试数据
testDataApi.exportData('./test-data-export.json');

// 导入测试数据
testDataApi.importData('./test-data-export.json');

// 清理测试数据
await testDataApi.cleanup();
```

### 性能监控

使用 `TestMonitor` 类监控测试执行和性能：

```javascript
const TestMonitor = require('./utils/test-monitor');

// 创建测试监控器
const testMonitor = new TestMonitor({
  enabled: true,
  logDir: './test-logs'
});

// 开始监控
testMonitor.startMonitoring();

// 记录响应时间
testMonitor.recordResponseTime('/api/users', 150);

// 记录事件
testMonitor.logEvent('test:start', { testName: 'Login Test' });

// 停止监控
testMonitor.stopMonitoring();

// 获取性能报告
const performanceReport = testMonitor.generatePerformanceReport();
```

### 测试报告

使用 `TestReporter` 类生成详细的测试报告：

```javascript
const TestReporter = require('./utils/test-reporter');

// 创建测试报告器
const testReporter = new TestReporter({
  reportDir: './test-reports'
});

// 设置测试结果
testReporter.setTestResults(testResults);

// 设置监控数据
testReporter.setMonitor(testMonitor);

// 生成报告
const reports = testReporter.generateReports();
console.log(`HTML 报告: ${reports.html.path}`);
console.log(`JSON 报告: ${reports.json.path}`);
```

### 集成通知

使用 `TestIntegrations` 类将测试结果发送到外部系统：

```javascript
const TestIntegrations = require('./utils/test-integrations');

// 创建测试集成
const testIntegrations = new TestIntegrations({
  jira: {
    url: 'https://your-jira-instance.atlassian.net',
    apiToken: 'YOUR_JIRA_API_TOKEN',
    username: '<EMAIL>',
    project: 'TEST'
  },
  slack: {
    webhookUrl: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK',
    channel: '#test-results'
  }
});

// 发送到 Slack
await testIntegrations.sendToSlack(testResults, {
  reportUrl: 'https://example.com/reports/latest.html'
});

// 发送到 Jira
await testIntegrations.sendToJira(testResults, {
  summary: '测试运行结果',
  reportPath: './test-reports/report.html'
});
```

## 查看测试报告

运行测试后，可以查看 HTML 格式的测试报告：

```bash
npm run report
```

## 测试账号

测试使用以下账号：

- 管理员账号：`admin` / `admin123`
- 普通用户账号：`user` / `user123`
- E2E 测试用户：`e2e_test_user` / `Test123!`（由全局设置脚本创建）

## 故障排除

### 测试失败

如果测试失败，可以查看以下内容：

1. 检查测试报告中的错误信息
2. 查看失败测试的截图（位于 `playwright-report` 目录）
3. 查看失败测试的视频（如果启用了视频录制）
4. 查看失败测试的跟踪信息（位于 `test-results` 目录）
5. 使用调试模式重新运行测试：`npm run test:debug`

### 全局设置失败

如果全局设置失败，可能会导致所有测试失败。检查以下内容：

1. 确保前端和后端服务正在运行
2. 检查 `global-setup.js` 中的错误信息
3. 手动运行全局设置测试：`npx playwright test tests/global.setup.js`
4. 检查 `auth` 目录中是否存在认证状态文件

### 后端 API 错误

如果测试失败是由于后端 API 错误，可以使用 `debug-workflow-api.js` 脚本进行调试：

1. 编辑脚本，设置正确的 API 端点和测试数据
2. 运行脚本：`node debug-workflow-api.js`
3. 检查输出的错误信息

### 视觉回归测试失败

如果视觉回归测试失败，可能是由于界面变化导致的。检查以下内容：

1. 查看失败测试的截图对比（位于 `test-results` 目录）
2. 如果变化是预期的，更新基准截图：`npx playwright test visual-regression.spec.js --update-snapshots`

### API 测试失败

如果 API 测试失败，可能是由于 API 变化或环境问题导致的。检查以下内容：

1. 确保后端服务正在运行
2. 检查 API 端点是否正确
3. 检查认证信息是否有效
4. 使用 Postman 或类似工具手动测试 API

### 无障碍测试失败

如果无障碍测试失败，可能是由于界面不符合无障碍标准导致的。检查以下内容：

1. 查看 `accessibility-reports` 目录中的报告
2. 检查失败的规则和元素
3. 如果某些规则不适用，可以在测试中排除这些规则

### 安全测试失败

如果安全测试失败，可能是由于安全漏洞导致的。检查以下内容：

1. 查看测试报告中的详细错误信息
2. 检查失败的安全规则
3. 与开发团队讨论安全问题

### 负载测试失败

如果负载测试失败，可能是由于性能问题导致的。检查以下内容：

1. 查看 `load-test-results` 目录中的报告
2. 检查响应时间和成功率
3. 调整测试参数（如并发用户数）
4. 检查服务器资源使用情况

## 最佳实践

1. **测试独立性**：保持测试独立性，每个测试应该能够单独运行
2. **测试数据管理**：使用 `TestDataManager` 创建和清理测试数据
3. **错误处理**：测试应该处理可能的错误情况，包括后端错误
4. **断言消息**：使用有意义的断言消息，便于理解测试失败原因
5. **定期运行**：定期运行所有测试以确保系统稳定性
6. **CI/CD 集成**：将测试集成到 CI/CD 流程中，确保每次代码变更都经过测试
7. **测试覆盖率**：确保测试覆盖所有关键功能和用户角色
8. **性能监控**：使用性能测试监控系统性能变化

## CI/CD 集成

本项目提供了多种 CI/CD 集成选项：

### GitHub Actions

GitHub Actions 工作流配置，可以自动运行测试：

- 位置：`.github/workflows/e2e-tests.yml`
- 触发条件：
  - 推送到主分支、开发分支
  - 拉取请求
  - 每日定时运行
  - 手动触发
- 功能：
  - 支持多种测试类型（冒烟、回归、API、无障碍、安全等）
  - 支持并行测试和分片
  - 自动生成测试报告和分析
  - 失败通知和问题创建
- 输出：测试报告、分析报告和截图作为构建产物

### Jenkins

Jenkins 流水线配置，可以在 Jenkins 中运行测试：

- 位置：`Jenkinsfile`
- 功能：
  - 使用 Docker 容器运行测试
  - 支持参数化构建
  - 生成 HTML 报告和 JUnit 报告
  - 邮件通知
- 输出：测试报告、分析报告和截图作为构建产物

### Docker

Docker Compose 配置，可以在容器化环境中运行测试：

- 位置：`docker-compose.test.yml` 和 `Dockerfile.test`
- 功能：
  - 完整的测试环境（数据库、后端、前端）
  - 隔离的测试运行
  - 支持多种测试类型
- 使用方法：
  ```bash
  # 运行冒烟测试
  ./run-docker-tests.sh --smoke

  # 运行回归测试
  ./run-docker-tests.sh --regression

  # 运行所有测试
  ./run-docker-tests.sh --all
  ```
