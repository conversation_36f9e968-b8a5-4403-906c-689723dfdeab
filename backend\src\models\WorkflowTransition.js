const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     WorkflowTransition:
 *       type: object
 *       required:
 *         - workflow_template_id
 *         - source_node_id
 *         - target_node_id
 *       properties:
 *         id:
 *           type: integer
 *           description: 转换ID
 *           example: 1
 *         workflow_template_id:
 *           type: integer
 *           description: 所属工作流模板ID
 *           example: 1
 *         source_node_id:
 *           type: integer
 *           description: 源节点ID
 *           example: 1
 *         target_node_id:
 *           type: integer
 *           description: 目标节点ID
 *           example: 2
 *         condition:
 *           type: object
 *           description: 转换条件
 *           example: {
 *             "type": "approval",
 *             "value": "approved"
 *           }
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class WorkflowTransition extends Model {
    static associate(models) {
      // 工作流模板
      WorkflowTransition.belongsTo(models.WorkflowTemplate, {
        foreignKey: 'workflow_template_id',
        as: 'workflowTemplate'
      });

      // 只有当 WorkflowNode 模型存在时才建立关联
      if (models.WorkflowNode) {
        // 源节点
        WorkflowTransition.belongsTo(models.WorkflowNode, {
          foreignKey: 'source_node_id',
          as: 'sourceNode'
        });

        // 目标节点
        WorkflowTransition.belongsTo(models.WorkflowNode, {
          foreignKey: 'target_node_id',
          as: 'targetNode'
        });
      }
    }
  }

  WorkflowTransition.init({
    workflow_template_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_templates',
        key: 'id'
      }
    },
    source_node_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_nodes',
        key: 'id'
      }
    },
    target_node_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'workflow_nodes',
        key: 'id'
      }
    },
    condition: {
      type: DataTypes.JSONB,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'WorkflowTransition',
    tableName: 'workflow_transitions',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return WorkflowTransition;
};
