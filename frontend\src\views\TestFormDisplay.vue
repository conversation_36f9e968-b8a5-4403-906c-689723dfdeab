<template>
  <div class="test-form-display">
    <h1>表单数据显示测试</h1>
    <p>此页面用于测试表单数据显示组件的分页功能</p>

    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <h2>表单数据显示</h2>
        </div>
      </template>

      <div>
        <h3>测试数据</h3>
        <p>字段数量: {{ Object.keys(testFormData.values).length }}</p>
        <el-button @click="showFormData">显示表单数据</el-button>
      </div>

      <!-- 简单的分页测试 -->
      <div class="test-pagination">
        <h3>分页测试</h3>
        <div v-for="item in paginatedItems" :key="item.key" class="test-item">
          <div class="test-label">{{ item.label }}</div>
          <div class="test-value">{{ item.value }}</div>
        </div>

        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="allItems.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(10)

// 处理页码变化
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  console.log('页码变化:', newPage)
}

// 处理每页条数变化
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  console.log('每页条数变化:', newSize)
  // 当每页条数变化时，如果当前页码超出范围，重置为第一页
  const totalPages = Math.ceil(allItems.value.length / pageSize.value)
  if (totalPages > 0 && currentPage.value > totalPages) {
    currentPage.value = 1
  }
}

// 显示表单数据
const showFormData = () => {
  ElMessage.info(`表单数据包含 ${Object.keys(testFormData.value.values).length} 个字段`)
  console.log('表单数据:', testFormData.value)
}

// 创建所有项目数据
const allItems = computed(() => {
  return Object.entries(testFormData.value.values).map(([key, value], index) => {
    return {
      key,
      label: `测试字段${index + 1}`,
      value
    }
  })
})

// 计算当前页的数据
const paginatedItems = computed(() => {
  if (!allItems.value || allItems.value.length === 0) {
    return []
  }
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return allItems.value.slice(start, end)
})

// 创建测试数据，包含大量字段以测试分页功能
const testFormData = ref({
  values: {
    field_1: '测试字段1的值',
    field_2: '测试字段2的值',
    field_3: '测试字段3的值',
    field_4: '测试字段4的值',
    field_5: '测试字段5的值',
    field_6: '测试字段6的值',
    field_7: '测试字段7的值',
    field_8: '测试字段8的值',
    field_9: '测试字段9的值',
    field_10: '测试字段10的值',
    field_11: '测试字段11的值',
    field_12: '测试字段12的值',
    field_13: '测试字段13的值',
    field_14: '测试字段14的值',
    field_15: '测试字段15的值',
    field_16: '测试字段16的值',
    field_17: '测试字段17的值',
    field_18: '测试字段18的值',
    field_19: '测试字段19的值',
    field_20: '测试字段20的值',
    field_21: '测试字段21的值',
    field_22: '测试字段22的值',
    field_23: '测试字段23的值',
    field_24: '测试字段24的值',
    field_25: '测试字段25的值',
    field_26: '测试字段26的值',
    field_27: '测试字段27的值',
    field_28: '测试字段28的值',
    field_29: '测试字段29的值',
    field_30: '测试字段30的值'
  },
  schema: {
    fields: [
      { field_key: 'field_1', label: '测试字段1', field_type: 'input' },
      { field_key: 'field_2', label: '测试字段2', field_type: 'input' },
      { field_key: 'field_3', label: '测试字段3', field_type: 'input' },
      { field_key: 'field_4', label: '测试字段4', field_type: 'input' },
      { field_key: 'field_5', label: '测试字段5', field_type: 'input' },
      { field_key: 'field_6', label: '测试字段6', field_type: 'input' },
      { field_key: 'field_7', label: '测试字段7', field_type: 'input' },
      { field_key: 'field_8', label: '测试字段8', field_type: 'input' },
      { field_key: 'field_9', label: '测试字段9', field_type: 'input' },
      { field_key: 'field_10', label: '测试字段10', field_type: 'input' },
      { field_key: 'field_11', label: '测试字段11', field_type: 'input' },
      { field_key: 'field_12', label: '测试字段12', field_type: 'input' },
      { field_key: 'field_13', label: '测试字段13', field_type: 'input' },
      { field_key: 'field_14', label: '测试字段14', field_type: 'input' },
      { field_key: 'field_15', label: '测试字段15', field_type: 'input' },
      { field_key: 'field_16', label: '测试字段16', field_type: 'input' },
      { field_key: 'field_17', label: '测试字段17', field_type: 'input' },
      { field_key: 'field_18', label: '测试字段18', field_type: 'input' },
      { field_key: 'field_19', label: '测试字段19', field_type: 'input' },
      { field_key: 'field_20', label: '测试字段20', field_type: 'input' },
      { field_key: 'field_21', label: '测试字段21', field_type: 'input' },
      { field_key: 'field_22', label: '测试字段22', field_type: 'input' },
      { field_key: 'field_23', label: '测试字段23', field_type: 'input' },
      { field_key: 'field_24', label: '测试字段24', field_type: 'input' },
      { field_key: 'field_25', label: '测试字段25', field_type: 'input' },
      { field_key: 'field_26', label: '测试字段26', field_type: 'input' },
      { field_key: 'field_27', label: '测试字段27', field_type: 'input' },
      { field_key: 'field_28', label: '测试字段28', field_type: 'input' },
      { field_key: 'field_29', label: '测试字段29', field_type: 'input' },
      { field_key: 'field_30', label: '测试字段30', field_type: 'input' }
    ],
    formProps: {
      labelWidth: '120px',
      labelPosition: 'right'
    }
  }
})
</script>

<style scoped>
.test-form-display {
  padding: 20px;
}

.form-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

h1 {
  margin-bottom: 10px;
}

p {
  color: #606266;
  margin-bottom: 20px;
}

.test-pagination {
  margin-top: 30px;
}

.test-item {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  padding: 12px 0;
  line-height: 24px;
}

.test-label {
  width: 180px;
  color: #606266;
  font-weight: 500;
  text-align: right;
  padding-right: 12px;
  box-sizing: border-box;
}

.test-value {
  flex: 1;
  color: #303133;
}

.el-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
