const { Model } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     FormTemplate:
 *       type: object
 *       required:
 *         - title
 *         - creator_id
 *       properties:
 *         id:
 *           type: integer
 *           description: 表单模板ID
 *           example: 1
 *         title:
 *           type: string
 *           description: 表单模板标题
 *           example: "请假申请表"
 *         description:
 *           type: string
 *           description: 表单模板描述
 *           example: "用于员工请假申请"
 *         creator_id:
 *           type: integer
 *           description: 创建者ID
 *           example: 1
 *         version:
 *           type: integer
 *           description: 当前版本号
 *           example: 1
 *         status:
 *           type: string
 *           description: 表单状态
 *           enum: [draft, published]
 *           example: "published"
 *         schema:
 *           type: object
 *           description: 表单布局和配置
 *           example: { "layout": "vertical" }
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2023-06-01T10:00:00Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2023-06-01T10:00:00Z"
 *       example:
 *         id: 1
 *         title: "请假申请表"
 *         description: "用于员工请假申请"
 *         creator_id: 1
 *         version: 1
 *         status: "published"
 *         schema: { "layout": "vertical" }
 *         created_at: "2023-06-01T10:00:00Z"
 *         updated_at: "2023-06-01T10:00:00Z"
 */

module.exports = (sequelize, DataTypes) => {
  class FormTemplate extends Model {
    static associate(models) {
      // 创建者
      FormTemplate.belongsTo(models.User, {
        foreignKey: 'creator_id',
        as: 'creator'
      });

      // 表单字段
      FormTemplate.hasMany(models.FormField, {
        foreignKey: 'form_template_id',
        as: 'fields',
        onDelete: 'CASCADE'
      });

      // 表单版本
      FormTemplate.hasMany(models.FormTemplateVersion, {
        foreignKey: 'form_template_id',
        as: 'versions',
        onDelete: 'CASCADE'
      });

      // 工作流模板
      FormTemplate.hasMany(models.WorkflowTemplate, {
        foreignKey: 'form_template_id',
        as: 'workflows'
      });
    }
  }

  FormTemplate.init({
    title: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    creator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    version: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'draft'
    },
    schema: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {}
    }
  }, {
    sequelize,
    modelName: 'FormTemplate',
    tableName: 'form_templates',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return FormTemplate;
};
