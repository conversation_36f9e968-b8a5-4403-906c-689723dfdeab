<template>
  <div>
    <!-- 登录页不显示菜单和头部 -->
    <router-view v-if="isLoginPage" v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>

    <!-- 其他页面显示完整布局 -->
    <el-container v-else class="app-container">
      <el-aside width="220px" class="sidebar">
        <div class="logo-container">
          <img src="./assets/logo.png" alt="Logo" class="logo" />
          <h2 class="logo-text">工作流系统</h2>
        </div>
        <el-scrollbar>
          <el-menu
            router
            :default-active="activeRoute"
            class="el-menu-vertical"
            background-color="#001529"
            text-color="#fff"
            active-text-color="#409EFF"
          >
            <el-menu-item index="/">
              <el-icon><i-ep-home /></el-icon>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/department">
              <el-icon><i-ep-office-building /></el-icon>
              <span>部门配置</span>
            </el-menu-item>
            <el-menu-item index="/members">
              <el-icon><i-ep-user-filled /></el-icon>
              <span>成员管理</span>
            </el-menu-item>
            <el-menu-item index="/form-design">
              <el-icon><i-ep-document /></el-icon>
              <span>表单设计器</span>
            </el-menu-item>
            <el-menu-item index="/workflow-design">
              <el-icon><i-ep-connection /></el-icon>
              <span>工作流设计</span>
            </el-menu-item>
            <el-menu-item index="/workflow-form">
              <el-icon><i-ep-edit /></el-icon>
              <span>工作流填写</span>
            </el-menu-item>
            <el-menu-item index="/workflow-process">
              <el-icon><i-ep-share /></el-icon>
              <span>工作流流转</span>
            </el-menu-item>

          </el-menu>
        </el-scrollbar>
      </el-aside>
      <el-container>
        <el-header class="header">
          <div class="header-left">
            <el-icon class="toggle-sidebar" @click="toggleSidebar">
              <i-ep-fold />
            </el-icon>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="currentRouteName">{{ currentRouteName }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
            <MessageCenter />
            <el-tooltip content="切换主题" placement="bottom">
              <el-button circle @click="toggleTheme">
                <el-icon><i-ep-moon v-if="isDarkMode" /><i-ep-sunny v-else /></el-icon>
              </el-button>
            </el-tooltip>
            <el-dropdown trigger="click">
              <div class="user-info">
                <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
                <span class="username">{{ username }}</span>
                <el-icon><i-ep-arrow-down /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="router.push('/profile')">个人信息</el-dropdown-item>
                  <el-dropdown-item @click="router.push('/change-password')">修改密码</el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        <el-main class="main-content">
          <router-view v-slot="{ Component, route }">
            <transition name="fade" mode="out-in">
              <suspense>
                <template #default>
                  <component :is="Component" />
                </template>
                <template #fallback>
                  <div class="loading-container">
                    <el-skeleton :rows="10" animated />
                  </div>
                </template>
              </suspense>
            </transition>
          </router-view>
          <div v-if="routeError" class="route-error">
            <el-alert
              title="页面加载错误"
              type="error"
              description="加载页面时发生错误，请刷新页面或联系管理员"
              show-icon
              :closable="true"
              @close="routeError = null"
            />
          </div>
        </el-main>
        <el-footer class="footer">
          © 2025 工作流系统 | 版权所有
        </el-footer>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MessageCenter from './components/MessageCenter.vue'

const route = useRoute()
const router = useRouter()
const sidebarCollapsed = ref(false)
const isDarkMode = ref(false)
const username = ref('管理员')
const routeError = ref(null)

// 判断是否是登录页
const isLoginPage = computed(() => route.path === '/login')

// 计算当前路由
const activeRoute = computed(() => route.path)

// 计算当前路由名称
const currentRouteName = computed(() => {
  const routeMap = {
    '/': '首页',
    '/department': '部门配置',
    '/members': '成员管理',
    '/form-design': '表单设计器',
    '/workflow-design': '工作流设计',
    '/workflow-form': '工作流填写',
    '/workflow-process': '工作流流转',
    '/test-analytics': '测试分析',
    '/profile': '个人信息',
    '/change-password': '修改密码'
  }

  // 处理动态路由
  if (route.path.startsWith('/test-analytics/runs/')) {
    return '测试运行详情'
  }

  return routeMap[route.path] || ''
})

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 切换主题
const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value
  document.documentElement.classList.toggle('dark-mode', isDarkMode.value)
}

// 退出登录
const handleLogout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('user')
  router.push('/login')
}

// 获取用户信息
onMounted(() => {
  const user = localStorage.getItem('user')
  if (user) {
    try {
      const userData = JSON.parse(user)
      username.value = userData.full_name || userData.username || '用户'
    } catch (e) {
      console.error('Failed to parse user data', e)
    }
  }

  // 添加路由错误处理
  router.onError((error) => {
    console.error('路由错误:', error)
    routeError.value = error

    // 如果是动态导入模块失败，尝试刷新页面
    if (error.message.includes('Failed to fetch dynamically imported module')) {
      // 记录错误，但不立即刷新，让用户决定
      console.warn('动态导入模块失败，可能需要刷新页面')
    }
  })
})
</script>

<style scoped>
.app-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.sidebar {
  background-color: #0f172a;
  transition: all 0.3s var(--animation-timing-function);
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  z-index: 10;
  position: relative;
}

.logo-container {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-lg);
  color: white;
  background-color: #0f172a;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  height: 36px;
  margin-right: var(--spacing-md);
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.05));
  transition: all 0.3s;
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  white-space: nowrap;
  margin: 0;
  background: linear-gradient(45deg, #60a5fa, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.el-menu-vertical {
  border-right: none;
  background-color: transparent;
}

.el-menu-vertical .el-menu-item {
  height: 56px;
  line-height: 56px;
  margin: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-base);
  color: rgba(255, 255, 255, 0.7) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-menu-vertical .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.12) !important;
  color: white !important;
  transform: translateX(4px);
}

.el-menu-vertical .el-menu-item.is-active {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light)) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.5);
  transform: translateX(4px);
}

.el-menu-vertical .el-menu-item i {
  color: inherit;
  margin-right: var(--spacing-md);
  font-size: 18px;
  transition: all 0.3s;
}

.el-menu-vertical .el-menu-item:hover i {
  transform: scale(1.1);
}

.header {
  background-color: var(--background-color-light);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-xl);
  height: 70px;
  transition: all 0.3s;
  border-bottom: 1px solid var(--border-color-light);
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-sidebar {
  font-size: 20px;
  margin-right: var(--spacing-xl);
  cursor: pointer;
  color: var(--text-color-secondary);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-full);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  background: linear-gradient(145deg, var(--background-color-light), var(--background-color));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.toggle-sidebar:hover {
  background-color: var(--primary-color-bg);
  color: var(--primary-color);
  transform: rotate(180deg);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.el-button.circle {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-full);
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(145deg, var(--background-color-light), var(--background-color));
  border: none;
  color: var(--text-color-secondary);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.el-button.circle:hover {
  background: linear-gradient(145deg, var(--background-color), var(--background-color-light));
  color: var(--primary-color);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.el-button.circle:active {
  transform: translateY(0) scale(0.95);
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-full);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  background: linear-gradient(145deg, var(--background-color-light), var(--background-color));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-info:hover {
  background: linear-gradient(145deg, var(--background-color), var(--background-color-light));
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.user-info .el-avatar {
  transition: all 0.3s;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.user-info:hover .el-avatar {
  transform: scale(1.1);
  border-color: var(--primary-color);
}

.username {
  margin: 0 var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.main-content {
  background-color: var(--background-color);
  padding: var(--spacing-xl);
  overflow-y: auto;
  transition: all 0.3s;
  position: relative;
}

.loading-container {
  padding: 20px;
  width: 100%;
}

.route-error {
  position: fixed;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 600px;
  z-index: 9999;
}

.footer {
  background-color: var(--background-color);
  color: var(--text-color-secondary);
  text-align: center;
  padding: var(--spacing-lg);
  font-size: var(--font-size-sm);
  border-top: 1px solid var(--border-color-split);
  transition: all 0.3s;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 80px !important;
  }

  .logo-text {
    display: none;
  }

  .el-menu--collapse .el-menu-item span {
    display: none;
  }

  .main-content {
    padding: var(--spacing-lg);
  }

  .header {
    padding: 0 var(--spacing-lg);
  }
}

/* 暗黑模式 */
:global(.dark-mode) .header {
  background-color: rgba(30, 41, 59, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

:global(.dark-mode) .toggle-sidebar {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

:global(.dark-mode) .main-content {
  background-color: var(--background-color);
  color: var(--text-color);
}

:global(.dark-mode) .footer {
  background-color: var(--background-color);
  color: var(--text-color-secondary);
  border-top: 1px solid var(--border-color-split);
}

:global(.dark-mode) .el-button.circle {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  color: var(--text-color-secondary);
}

:global(.dark-mode) .el-button.circle:hover {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  color: var(--primary-color-light);
}

:global(.dark-mode) .user-info {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

:global(.dark-mode) .user-info:hover {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
}
</style>
