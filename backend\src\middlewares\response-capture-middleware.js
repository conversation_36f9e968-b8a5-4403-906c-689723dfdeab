/**
 * 响应捕获中间件
 * 捕获并打印响应数据
 */
function responseCaptureMiddleware(req, res, next) {
  // 保存原始的方法
  const originalSend = res.send;
  const originalJson = res.json;
  const originalEnd = res.end;
  
  // 记录请求开始时间
  const startTime = Date.now();
  
  // 打印请求信息
  console.log(`\n请求: ${req.method} ${req.url}`);
  console.log(`请求体: ${JSON.stringify(req.body)}`);
  
  // 重写send方法
  res.send = function(body) {
    // 计算响应时间
    const responseTime = Date.now() - startTime;
    
    // 打印响应信息
    console.log(`响应: 状态码 ${res.statusCode} (${responseTime}ms)`);
    console.log(`响应数据: ${body}`);
    
    // 调用原始方法
    return originalSend.apply(this, arguments);
  };
  
  // 重写json方法
  res.json = function(body) {
    // 计算响应时间
    const responseTime = Date.now() - startTime;
    
    // 打印响应信息
    console.log(`响应: 状态码 ${res.statusCode} (${responseTime}ms)`);
    console.log(`响应数据: ${JSON.stringify(body)}`);
    
    // 调用原始方法
    return originalJson.apply(this, arguments);
  };
  
  next();
}

module.exports = responseCaptureMiddleware;
