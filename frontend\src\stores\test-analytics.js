import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import * as testAnalyticsApi from '../api/test-analytics';

export const useTestAnalyticsStore = defineStore('testAnalytics', () => {
  // 状态
  const summary = ref(null);
  const analytics = ref(null);
  const testRuns = ref([]);
  const currentTestRun = ref(null);
  const trends = ref(null);
  const loading = ref(false);
  const error = ref(null);
  const pagination = ref({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  });

  // 计算属性
  const successRate = computed(() => {
    if (!summary.value) return 0;
    return summary.value.successRate.toFixed(2);
  });

  const testTypeDistribution = computed(() => {
    if (!summary.value || !summary.value.testTypeDistribution) return [];
    return summary.value.testTypeDistribution;
  });

  const recentRuns = computed(() => {
    if (!summary.value || !summary.value.recentRuns) return [];
    return summary.value.recentRuns;
  });

  const mostFailedTests = computed(() => {
    if (!analytics.value || !analytics.value.mostFailedTests) return [];
    return analytics.value.mostFailedTests;
  });

  // 方法
  const fetchSummary = async () => {
    try {
      loading.value = true;
      error.value = null;
      const response = await testAnalyticsApi.getTestSummary();
      summary.value = response.data.data;
    } catch (err) {
      console.error('Error fetching test summary:', err);
      error.value = err.response?.data?.message || '获取测试分析摘要失败';
    } finally {
      loading.value = false;
    }
  };

  const fetchAnalytics = async (params) => {
    try {
      loading.value = true;
      error.value = null;
      const response = await testAnalyticsApi.getTestAnalytics(params);
      analytics.value = response.data.data;
    } catch (err) {
      console.error('Error fetching test analytics:', err);
      error.value = err.response?.data?.message || '获取测试分析数据失败';
    } finally {
      loading.value = false;
    }
  };

  const fetchTestRuns = async (params) => {
    try {
      loading.value = true;
      error.value = null;
      const response = await testAnalyticsApi.getTestRuns(params);
      testRuns.value = response.data.data.testRuns;
      pagination.value = response.data.data.pagination;
    } catch (err) {
      console.error('Error fetching test runs:', err);
      error.value = err.response?.data?.message || '获取测试运行列表失败';
    } finally {
      loading.value = false;
    }
  };

  const fetchTestRunById = async (id) => {
    try {
      loading.value = true;
      error.value = null;
      const response = await testAnalyticsApi.getTestRunById(id);
      currentTestRun.value = response.data.data;
    } catch (err) {
      console.error('Error fetching test run:', err);
      error.value = err.response?.data?.message || '获取测试运行详情失败';
    } finally {
      loading.value = false;
    }
  };

  const fetchTrends = async (params) => {
    try {
      loading.value = true;
      error.value = null;
      const response = await testAnalyticsApi.getTestTrends(params);
      trends.value = response.data.data;
    } catch (err) {
      console.error('Error fetching test trends:', err);
      error.value = err.response?.data?.message || '获取测试趋势数据失败';
    } finally {
      loading.value = false;
    }
  };

  const createTestRun = async (data) => {
    try {
      loading.value = true;
      error.value = null;
      const response = await testAnalyticsApi.createTestRun(data);
      return response.data.data;
    } catch (err) {
      console.error('Error creating test run:', err);
      error.value = err.response?.data?.message || '提交测试运行结果失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    // 状态
    summary,
    analytics,
    testRuns,
    currentTestRun,
    trends,
    loading,
    error,
    pagination,

    // 计算属性
    successRate,
    testTypeDistribution,
    recentRuns,
    mostFailedTests,

    // 方法
    fetchSummary,
    fetchAnalytics,
    fetchTestRuns,
    fetchTestRunById,
    fetchTrends,
    createTestRun
  };
});
