/**
 * Test Results Analyzer
 * This script analyzes test results and generates reports
 * 
 * Usage:
 *   node analyze-results.js [options]
 * 
 * Options:
 *   --dir=PATH      Path to results directory (default: latest in ci-reports)
 *   --html          Generate HTML report
 *   --json          Generate JSON report
 *   --trend         Include trend analysis
 *   --days=N        Include N days of history in trend (default: 7)
 */

const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);

// Get directory parameter
const getDirParam = () => {
  const param = args.find(arg => arg.startsWith('--dir='));
  if (param) {
    return param.split('=')[1];
  }
  return null;
};

// Get days parameter
const getDaysParam = () => {
  const param = args.find(arg => arg.startsWith('--days='));
  if (param) {
    const value = param.split('=')[1];
    return isNaN(parseInt(value)) ? 7 : parseInt(value);
  }
  return 7;
};

// Options
const dirPath = getDirParam();
const generateHtml = args.includes('--html');
const generateJson = args.includes('--json');
const includeTrend = args.includes('--trend');
const historyDays = getDaysParam();

// Find latest results directory if not specified
const getLatestResultsDir = () => {
  const reportsDir = path.join(__dirname, 'ci-reports');
  if (!fs.existsSync(reportsDir)) {
    console.error('No reports directory found');
    process.exit(1);
  }
  
  const dirs = fs.readdirSync(reportsDir)
    .filter(file => fs.statSync(path.join(reportsDir, file)).isDirectory())
    .sort((a, b) => b.localeCompare(a)); // Sort in descending order
  
  if (dirs.length === 0) {
    console.error('No result directories found');
    process.exit(1);
  }
  
  return path.join(reportsDir, dirs[0]);
};

// Get results directory
const resultsDir = dirPath || getLatestResultsDir();
console.log(`Analyzing results in: ${resultsDir}`);

// Read test results
const readTestResults = (dir) => {
  const resultsFile = path.join(dir, 'test-results.json');
  if (!fs.existsSync(resultsFile)) {
    console.error(`No test results found in ${dir}`);
    process.exit(1);
  }
  
  try {
    const data = fs.readFileSync(resultsFile, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading test results: ${error.message}`);
    process.exit(1);
  }
};

// Read Playwright test report
const readPlaywrightReport = (dir) => {
  const reportFile = path.join(dir, 'report.json');
  if (!fs.existsSync(reportFile)) {
    console.warn(`No Playwright report found in ${dir}`);
    return null;
  }
  
  try {
    const data = fs.readFileSync(reportFile, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.warn(`Error reading Playwright report: ${error.message}`);
    return null;
  }
};

// Get historical results for trend analysis
const getHistoricalResults = (days) => {
  const reportsDir = path.join(__dirname, 'ci-reports');
  if (!fs.existsSync(reportsDir)) {
    return [];
  }
  
  const now = new Date();
  const cutoffDate = new Date(now.setDate(now.getDate() - days));
  
  const dirs = fs.readdirSync(reportsDir)
    .filter(file => {
      const dirPath = path.join(reportsDir, file);
      return fs.statSync(dirPath).isDirectory();
    })
    .sort((a, b) => b.localeCompare(a)); // Sort in descending order
  
  const results = [];
  
  for (const dir of dirs) {
    const dirPath = path.join(reportsDir, dir);
    const resultsFile = path.join(dirPath, 'test-results.json');
    
    if (fs.existsSync(resultsFile)) {
      try {
        const data = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
        const resultDate = new Date(data.timestamp);
        
        if (resultDate >= cutoffDate) {
          results.push({
            date: resultDate,
            success: data.success,
            duration: data.duration,
            totalTests: data.tests.total,
            config: data.config
          });
        }
      } catch (error) {
        console.warn(`Error reading historical results from ${dir}: ${error.message}`);
      }
    }
  }
  
  return results;
};

// Analyze test results
const analyzeResults = (results, playwrightReport) => {
  const analysis = {
    timestamp: results.timestamp,
    duration: results.duration,
    success: results.success,
    exitCode: results.exitCode,
    totalTests: results.tests.total,
    config: results.config
  };
  
  // Add detailed test results if Playwright report is available
  if (playwrightReport) {
    const testStats = {
      passed: 0,
      failed: 0,
      skipped: 0,
      flaky: 0,
      total: 0
    };
    
    const testsByFile = {};
    const failedTests = [];
    
    // Process test results
    playwrightReport.suites.forEach(suite => {
      processSuite(suite, testStats, testsByFile, failedTests);
    });
    
    analysis.testStats = testStats;
    analysis.testsByFile = testsByFile;
    analysis.failedTests = failedTests;
  }
  
  return analysis;
};

// Process test suite recursively
const processSuite = (suite, testStats, testsByFile, failedTests, filePath = '') => {
  // Update file path
  const currentFilePath = suite.file ? suite.file : filePath;
  
  // Process specs (tests)
  if (suite.specs) {
    suite.specs.forEach(spec => {
      // Initialize file stats if not exists
      if (!testsByFile[currentFilePath]) {
        testsByFile[currentFilePath] = {
          passed: 0,
          failed: 0,
          skipped: 0,
          flaky: 0,
          total: 0
        };
      }
      
      // Update stats
      testStats.total++;
      testsByFile[currentFilePath].total++;
      
      if (spec.ok) {
        testStats.passed++;
        testsByFile[currentFilePath].passed++;
      } else if (spec.skipped) {
        testStats.skipped++;
        testsByFile[currentFilePath].skipped++;
      } else {
        testStats.failed++;
        testsByFile[currentFilePath].failed++;
        
        // Add to failed tests
        failedTests.push({
          file: currentFilePath,
          title: spec.title,
          error: spec.error ? spec.error.message : 'Unknown error'
        });
      }
      
      // Check for flaky tests
      if (spec.flaky) {
        testStats.flaky++;
        testsByFile[currentFilePath].flaky++;
      }
    });
  }
  
  // Process child suites
  if (suite.suites) {
    suite.suites.forEach(childSuite => {
      processSuite(childSuite, testStats, testsByFile, failedTests, currentFilePath);
    });
  }
};

// Generate HTML report
const generateHtmlReport = (analysis, historicalResults) => {
  const reportPath = path.join(resultsDir, 'analysis-report.html');
  
  // Create HTML content
  let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试结果分析报告</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }
    h1 { color: #2c3e50; border-bottom: 2px solid #eee; padding-bottom: 10px; }
    h2 { color: #3498db; margin-top: 30px; }
    h3 { color: #2980b9; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 30px; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
    th { background-color: #f2f2f2; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    .summary { background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    .success { color: #27ae60; }
    .failure { color: #e74c3c; }
    .warning { color: #f39c12; }
    .error-details { background-color: #ffeeee; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 10px; }
    .chart-container { width: 100%; height: 300px; margin-bottom: 30px; }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <h1>测试结果分析报告</h1>
  
  <div class="summary">
    <p>测试时间: ${new Date(analysis.timestamp).toLocaleString()}</p>
    <p>测试结果: <span class="${analysis.success ? 'success' : 'failure'}">${analysis.success ? '成功' : '失败'}</span></p>
    <p>测试时长: ${analysis.duration.toFixed(2)} 秒</p>
    <p>测试文件数: ${analysis.totalTests}</p>
  </div>
  
  <h2>测试配置</h2>
  <table>
    <tr>
      <th>配置项</th>
      <th>值</th>
    </tr>
    <tr>
      <td>并行工作进程</td>
      <td>${analysis.config.parallelWorkers}</td>
    </tr>
    <tr>
      <td>重试次数</td>
      <td>${analysis.config.retryCount}</td>
    </tr>
    <tr>
      <td>超时时间</td>
      <td>${analysis.config.testTimeout}ms</td>
    </tr>
    <tr>
      <td>分片</td>
      <td>${analysis.config.shard || '无'}</td>
    </tr>
    <tr>
      <td>项目</td>
      <td>${analysis.config.project || '所有'}</td>
    </tr>
    <tr>
      <td>报告器</td>
      <td>${analysis.config.reporter}</td>
    </tr>
  </table>
  `;
  
  // Add test statistics if available
  if (analysis.testStats) {
    html += `
  <h2>测试统计</h2>
  <table>
    <tr>
      <th>指标</th>
      <th>数量</th>
      <th>百分比</th>
    </tr>
    <tr>
      <td>通过</td>
      <td>${analysis.testStats.passed}</td>
      <td>${(analysis.testStats.passed / analysis.testStats.total * 100).toFixed(2)}%</td>
    </tr>
    <tr>
      <td>失败</td>
      <td>${analysis.testStats.failed}</td>
      <td>${(analysis.testStats.failed / analysis.testStats.total * 100).toFixed(2)}%</td>
    </tr>
    <tr>
      <td>跳过</td>
      <td>${analysis.testStats.skipped}</td>
      <td>${(analysis.testStats.skipped / analysis.testStats.total * 100).toFixed(2)}%</td>
    </tr>
    <tr>
      <td>不稳定</td>
      <td>${analysis.testStats.flaky}</td>
      <td>${(analysis.testStats.flaky / analysis.testStats.total * 100).toFixed(2)}%</td>
    </tr>
    <tr>
      <td>总计</td>
      <td>${analysis.testStats.total}</td>
      <td>100%</td>
    </tr>
  </table>
  
  <div class="chart-container">
    <canvas id="testResultsChart"></canvas>
  </div>
  
  <h2>测试文件结果</h2>
  <table>
    <tr>
      <th>文件</th>
      <th>通过</th>
      <th>失败</th>
      <th>跳过</th>
      <th>不稳定</th>
      <th>总计</th>
      <th>通过率</th>
    </tr>
    `;
    
    // Add rows for each file
    Object.entries(analysis.testsByFile).forEach(([file, stats]) => {
      const passRate = (stats.passed / stats.total * 100).toFixed(2);
      const className = passRate >= 90 ? 'success' : (passRate >= 70 ? 'warning' : 'failure');
      
      html += `
    <tr>
      <td>${file}</td>
      <td>${stats.passed}</td>
      <td>${stats.failed}</td>
      <td>${stats.skipped}</td>
      <td>${stats.flaky}</td>
      <td>${stats.total}</td>
      <td class="${className}">${passRate}%</td>
    </tr>
      `;
    });
    
    html += `
  </table>
    `;
    
    // Add failed tests if any
    if (analysis.failedTests && analysis.failedTests.length > 0) {
      html += `
  <h2>失败的测试</h2>
      `;
      
      analysis.failedTests.forEach(test => {
        html += `
  <div class="error-details">
    <h3>${test.file}</h3>
    <p><strong>测试:</strong> ${test.title}</p>
    <p><strong>错误:</strong> ${test.error}</p>
  </div>
        `;
      });
    }
  }
  
  // Add trend analysis if requested
  if (includeTrend && historicalResults && historicalResults.length > 0) {
    html += `
  <h2>趋势分析 (${historicalResults.length} 次运行)</h2>
  
  <div class="chart-container">
    <canvas id="trendChart"></canvas>
  </div>
  
  <table>
    <tr>
      <th>日期</th>
      <th>结果</th>
      <th>时长 (秒)</th>
      <th>测试文件数</th>
    </tr>
    `;
    
    // Add rows for each historical result
    historicalResults.forEach(result => {
      html += `
    <tr>
      <td>${result.date.toLocaleString()}</td>
      <td class="${result.success ? 'success' : 'failure'}">${result.success ? '成功' : '失败'}</td>
      <td>${result.duration.toFixed(2)}</td>
      <td>${result.totalTests}</td>
    </tr>
      `;
    });
    
    html += `
  </table>
    `;
  }
  
  // Add JavaScript for charts
  html += `
  <script>
    // Test results chart
    const testResultsCtx = document.getElementById('testResultsChart').getContext('2d');
    new Chart(testResultsCtx, {
      type: 'pie',
      data: {
        labels: ['通过', '失败', '跳过', '不稳定'],
        datasets: [{
          data: [${analysis.testStats ? [
            analysis.testStats.passed,
            analysis.testStats.failed,
            analysis.testStats.skipped,
            analysis.testStats.flaky
          ].join(', ') : ''}],
          backgroundColor: [
            '#27ae60',
            '#e74c3c',
            '#f39c12',
            '#3498db'
          ]
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'right',
          },
          title: {
            display: true,
            text: '测试结果分布'
          }
        }
      }
    });
    
    ${includeTrend && historicalResults && historicalResults.length > 0 ? `
    // Trend chart
    const trendCtx = document.getElementById('trendChart').getContext('2d');
    new Chart(trendCtx, {
      type: 'line',
      data: {
        labels: [${historicalResults.map(r => `'${r.date.toLocaleDateString()}'`).join(', ')}],
        datasets: [{
          label: '测试时长 (秒)',
          data: [${historicalResults.map(r => r.duration.toFixed(2)).join(', ')}],
          borderColor: '#3498db',
          backgroundColor: 'rgba(52, 152, 219, 0.1)',
          yAxisID: 'y'
        }, {
          label: '测试文件数',
          data: [${historicalResults.map(r => r.totalTests).join(', ')}],
          borderColor: '#f39c12',
          backgroundColor: 'rgba(243, 156, 18, 0.1)',
          yAxisID: 'y1'
        }]
      },
      options: {
        responsive: true,
        stacked: false,
        plugins: {
          title: {
            display: true,
            text: '测试趋势'
          }
        },
        scales: {
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            title: {
              display: true,
              text: '测试时长 (秒)'
            }
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
              display: true,
              text: '测试文件数'
            },
            grid: {
              drawOnChartArea: false
            }
          }
        }
      }
    });
    ` : ''}
  </script>
</body>
</html>
  `;
  
  // Write HTML report
  fs.writeFileSync(reportPath, html);
  console.log(`HTML report generated: ${reportPath}`);
};

// Generate JSON report
const generateJsonReport = (analysis, historicalResults) => {
  const reportPath = path.join(resultsDir, 'analysis-report.json');
  
  // Create JSON content
  const jsonReport = {
    analysis,
    historicalResults: includeTrend ? historicalResults : undefined
  };
  
  // Write JSON report
  fs.writeFileSync(reportPath, JSON.stringify(jsonReport, null, 2));
  console.log(`JSON report generated: ${reportPath}`);
};

// Main function
const main = () => {
  // Read test results
  const results = readTestResults(resultsDir);
  
  // Read Playwright report
  const playwrightReport = readPlaywrightReport(resultsDir);
  
  // Analyze results
  const analysis = analyzeResults(results, playwrightReport);
  
  // Get historical results for trend analysis
  const historicalResults = includeTrend ? getHistoricalResults(historyDays) : null;
  
  // Generate reports
  if (generateHtml) {
    generateHtmlReport(analysis, historicalResults);
  }
  
  if (generateJson) {
    generateJsonReport(analysis, historicalResults);
  }
  
  // Print summary
  console.log('\n=== Analysis Summary ===');
  console.log(`Test run: ${new Date(analysis.timestamp).toLocaleString()}`);
  console.log(`Result: ${analysis.success ? 'Success' : 'Failure'}`);
  console.log(`Duration: ${analysis.duration.toFixed(2)} seconds`);
  console.log(`Test files: ${analysis.totalTests}`);
  
  if (analysis.testStats) {
    console.log(`\nTest Statistics:`);
    console.log(`- Passed: ${analysis.testStats.passed} (${(analysis.testStats.passed / analysis.testStats.total * 100).toFixed(2)}%)`);
    console.log(`- Failed: ${analysis.testStats.failed} (${(analysis.testStats.failed / analysis.testStats.total * 100).toFixed(2)}%)`);
    console.log(`- Skipped: ${analysis.testStats.skipped} (${(analysis.testStats.skipped / analysis.testStats.total * 100).toFixed(2)}%)`);
    console.log(`- Flaky: ${analysis.testStats.flaky} (${(analysis.testStats.flaky / analysis.testStats.total * 100).toFixed(2)}%)`);
    console.log(`- Total: ${analysis.testStats.total}`);
  }
  
  console.log('=======================');
};

// Run main function
main();
