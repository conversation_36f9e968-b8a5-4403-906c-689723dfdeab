const { test, expect } = require('@playwright/test');
const { login, navigateToModule } = require('../utils/test-helpers');

test.describe('Workflow Design Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await login(page, 'admin');
    // Navigate to workflow design page
    await navigateToModule(page, '工作流设计');
  });

  test('should display workflow list', async ({ page }) => {
    // Verify workflow list tab is active
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('工作流列表');
    
    // Verify workflow table is visible
    await expect(page.locator('.el-table')).toBeVisible();
  });

  test('should open workflow designer', async ({ page }) => {
    // Click on create workflow button
    await page.click('button:has-text("创建工作流")');
    
    // Verify workflow designer tab is active
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('工作流设计');
    
    // Verify node types panel is visible
    await expect(page.locator('.node-types')).toBeVisible();
    
    // Verify design canvas is visible
    await expect(page.locator('.workflow-canvas')).toBeVisible();
    
    // Verify property panel is visible
    await expect(page.locator('.property-panel')).toBeVisible();
  });

  test('should add nodes to workflow', async ({ page }) => {
    // Click on create workflow button
    await page.click('button:has-text("创建工作流")');
    
    // Wait for workflow designer to load
    await page.waitForSelector('.node-types');
    
    // Add start node (if not already present)
    if (await page.locator('.workflow-canvas .workflow-node').count() === 0) {
      await page.locator('.node-types .node-type-item').filter({ hasText: '开始节点' }).click();
      
      // Click on canvas to place the node
      await page.locator('.workflow-canvas').click();
    }
    
    // Add approval node
    await page.locator('.node-types .node-type-item').filter({ hasText: '审批节点' }).click();
    
    // Click on canvas to place the node
    await page.locator('.workflow-canvas').click({ position: { x: 300, y: 200 } });
    
    // Add end node
    await page.locator('.node-types .node-type-item').filter({ hasText: '结束节点' }).click();
    
    // Click on canvas to place the node
    await page.locator('.workflow-canvas').click({ position: { x: 500, y: 300 } });
    
    // Verify nodes are added
    await expect(page.locator('.workflow-canvas .workflow-node')).toHaveCount.above(2);
  });

  test('should edit node properties', async ({ page }) => {
    // Click on create workflow button
    await page.click('button:has-text("创建工作流")');
    
    // Wait for workflow designer to load
    await page.waitForSelector('.node-types');
    
    // Add approval node if not already present
    if (await page.locator('.workflow-canvas .workflow-node[data-type="approval"]').count() === 0) {
      await page.locator('.node-types .node-type-item').filter({ hasText: '审批节点' }).click();
      await page.locator('.workflow-canvas').click({ position: { x: 300, y: 200 } });
    }
    
    // Click on approval node to select it
    await page.locator('.workflow-canvas .workflow-node[data-type="approval"]').first().click();
    
    // Edit node name
    const nodeName = '测试审批节点' + Date.now();
    await page.fill('.property-panel input[placeholder="请输入节点名称"]', nodeName);
    
    // Verify node name is updated
    await expect(page.locator('.workflow-canvas .workflow-node[data-type="approval"] .node-name')).toContainText(nodeName);
  });

  test('should save workflow template', async ({ page }) => {
    // Click on create workflow button
    await page.click('button:has-text("创建工作流")');
    
    // Wait for workflow designer to load
    await page.waitForSelector('.node-types');
    
    // Fill workflow info
    const workflowName = '测试工作流' + Date.now();
    await page.fill('input[placeholder="请输入工作流名称"]', workflowName);
    await page.fill('textarea[placeholder="请输入工作流描述"]', '这是一个测试工作流');
    
    // Select a form template
    await page.click('.el-select').filter({ hasText: '请选择关联表单' });
    await page.click('.el-select-dropdown__item:first-child');
    
    // Save workflow
    await page.click('button:has-text("保存")');
    
    // Verify success message
    await expect(page.locator('.el-message--success')).toBeVisible();
    
    // Switch to workflow list tab
    await page.locator('.el-tabs__item').filter({ hasText: '工作流列表' }).click();
    
    // Verify new workflow appears in the list
    await expect(page.locator('.el-table__row').filter({ hasText: workflowName })).toBeVisible();
  });
});
