/**
 * Test Analytics Client
 * 
 * This module provides a client for sending test results to the test analytics API.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const os = require('os');

class TestAnalyticsClient {
  /**
   * Create a new TestAnalyticsClient
   * @param {Object} config - Configuration options
   * @param {string} config.apiUrl - API URL
   * @param {string} config.testRunId - Test run ID
   * @param {string} config.testType - Test type
   * @param {Object} config.config - Test configuration
   */
  constructor(config = {}) {
    this.apiUrl = config.apiUrl || 'http://localhost:3001/api/test-analytics';
    this.testRunId = config.testRunId || this.generateTestRunId();
    this.testType = config.testType || 'unknown';
    this.config = config.config || {};
    this.metrics = [];
    this.startTime = Date.now();
  }

  /**
   * Generate a unique test run ID
   * @returns {string} - Unique test run ID
   */
  generateTestRunId() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const random = Math.random().toString(36).substring(2, 8);
    return `${timestamp}-${random}`;
  }

  /**
   * Record a metric
   * @param {string} metricType - Metric type
   * @param {number} value - Metric value
   * @param {string} unit - Metric unit
   * @param {Object} metadata - Additional metadata
   */
  recordMetric(metricType, value, unit = '', metadata = {}) {
    this.metrics.push({
      metricType,
      timestamp: new Date().toISOString(),
      value,
      unit,
      metadata
    });
  }

  /**
   * Process test results
   * @param {Object} results - Test results
   * @returns {Object} - Processed test results
   */
  processResults(results) {
    // Extract test statistics
    const testStats = {
      total: results.tests?.total || 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    // Extract test results
    const testResults = [];
    const failedTests = [];

    // Process test results if available
    if (results.specs && Array.isArray(results.specs)) {
      results.specs.forEach(spec => {
        // Update test statistics
        if (spec.status === 'passed') {
          testStats.passed++;
        } else if (spec.status === 'failed') {
          testStats.failed++;
          
          // Add to failed tests
          failedTests.push({
            file: spec.file,
            title: spec.title,
            error: spec.error
          });
        } else if (spec.status === 'skipped') {
          testStats.skipped++;
        }
        
        // Add to test results
        testResults.push({
          file: spec.file,
          title: spec.title,
          status: spec.status,
          duration: spec.duration,
          error: spec.error
        });
      });
    }

    // Calculate success
    const success = testStats.failed === 0;
    
    // Calculate duration
    const endTime = Date.now();
    const duration = (endTime - this.startTime) / 1000;
    
    // Prepare test run data
    const testRunData = {
      testRunId: this.testRunId,
      testType: this.testType,
      success,
      duration,
      testStats,
      failedTests,
      config: {
        ...this.config,
        system: {
          platform: os.platform(),
          release: os.release(),
          arch: os.arch(),
          cpus: os.cpus().length,
          memory: Math.round(os.totalmem() / (1024 * 1024 * 1024)) + 'GB'
        }
      },
      results: testResults,
      metrics: this.metrics
    };
    
    return testRunData;
  }

  /**
   * Send test results to the API
   * @param {Object} results - Test results
   * @returns {Promise} - Promise that resolves when results are sent
   */
  async sendResults(results) {
    try {
      const testRunData = this.processResults(results);
      
      // Send to API
      const response = await axios.post(`${this.apiUrl}/runs`, testRunData);
      
      console.log(`Test results sent to analytics API. Test run ID: ${this.testRunId}`);
      
      return response.data;
    } catch (error) {
      console.error('Error sending test results to analytics API:', error.message);
      
      // Save results locally as fallback
      this.saveResultsLocally(this.processResults(results));
      
      throw error;
    }
  }

  /**
   * Save results locally as fallback
   * @param {Object} testRunData - Test run data
   */
  saveResultsLocally(testRunData) {
    try {
      const fallbackDir = path.join(__dirname, '../test-analytics-fallback');
      
      // Create directory if it doesn't exist
      if (!fs.existsSync(fallbackDir)) {
        fs.mkdirSync(fallbackDir, { recursive: true });
      }
      
      // Save results to file
      const filePath = path.join(fallbackDir, `${this.testRunId}.json`);
      fs.writeFileSync(filePath, JSON.stringify(testRunData, null, 2));
      
      console.log(`Test results saved locally: ${filePath}`);
    } catch (error) {
      console.error('Error saving test results locally:', error.message);
    }
  }
}

module.exports = TestAnalyticsClient;
