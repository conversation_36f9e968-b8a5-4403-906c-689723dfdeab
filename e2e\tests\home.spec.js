const { test, expect } = require('@playwright/test');
const { login } = require('../utils/test-helpers');

test.describe('Home Page Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await login(page, 'admin');
  });

  test('should display welcome message and statistics', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Verify welcome message is displayed
    await expect(page.locator('h1')).toContainText('欢迎使用工作流系统');
    
    // Verify statistics are displayed
    await expect(page.locator('.stats-row')).toBeVisible();
    await expect(page.locator('.stat-card')).toHaveCount(4);
  });

  test('should display module cards', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Verify module cards are displayed
    await expect(page.locator('.module-card')).toBeVisible();
    
    // Verify specific modules are present
    const moduleNames = ['部门配置', '表单设计器', '工作流设计', '工作流填写', '工作流流转'];
    for (const name of moduleNames) {
      await expect(page.locator('.module-card').filter({ hasText: name })).toBeVisible();
    }
  });

  test('should navigate to department module', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Click on department module
    await page.locator('.module-card').filter({ hasText: '部门配置' }).locator('button').click();
    
    // Verify navigation to department page
    await page.waitForURL(/.*department/);
    await expect(page.locator('.card-header')).toContainText('部门管理');
  });

  test('should navigate to form design module', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Click on form design module
    await page.locator('.module-card').filter({ hasText: '表单设计器' }).locator('button').click();
    
    // Verify navigation to form design page
    await page.waitForURL(/.*form-design/);
    await expect(page.locator('.card-header')).toContainText('表单设计');
  });

  test('should navigate to workflow design module', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Click on workflow design module
    await page.locator('.module-card').filter({ hasText: '工作流设计' }).locator('button').click();
    
    // Verify navigation to workflow design page
    await page.waitForURL(/.*workflow-design/);
    await expect(page.locator('.card-header')).toContainText('工作流设计');
  });

  test('should navigate to workflow form module', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Click on workflow form module
    await page.locator('.module-card').filter({ hasText: '工作流填写' }).locator('button').click();
    
    // Verify navigation to workflow form page
    await page.waitForURL(/.*workflow-form/);
    await expect(page.locator('.card-header')).toContainText('工作流填写');
  });

  test('should navigate to workflow process module', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Click on workflow process module
    await page.locator('.module-card').filter({ hasText: '工作流流转' }).locator('button').click();
    
    // Verify navigation to workflow process page
    await page.waitForURL(/.*workflow-process/);
    await expect(page.locator('.card-header')).toContainText('工作流处理');
  });
});
