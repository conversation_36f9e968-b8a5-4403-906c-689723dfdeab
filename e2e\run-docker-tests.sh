#!/bin/bash

# Run E2E tests in Docker
# Usage: ./run-docker-tests.sh [options]
# Options:
#   --smoke         Run smoke tests only (default)
#   --regression    Run regression tests
#   --api           Run API tests
#   --a11y          Run accessibility tests
#   --security      Run security tests
#   --visual        Run visual regression tests
#   --all           Run all tests
#   --parallel=N    Run N workers in parallel (default: 2)
#   --retries=N     Retry failed tests N times (default: 1)
#   --clean         Clean up containers after tests
#   --rebuild       Rebuild containers before running tests
#   --help          Show this help message

# Default values
TEST_TYPE="smoke"
PARALLEL=2
RETRIES=1
CLEAN=false
REBUILD=false

# Parse arguments
for arg in "$@"; do
  case $arg in
    --smoke)
      TEST_TYPE="smoke"
      shift
      ;;
    --regression)
      TEST_TYPE="regression"
      shift
      ;;
    --api)
      TEST_TYPE="api"
      shift
      ;;
    --a11y)
      TEST_TYPE="a11y"
      shift
      ;;
    --security)
      TEST_TYPE="security"
      shift
      ;;
    --visual)
      TEST_TYPE="visual"
      shift
      ;;
    --all)
      TEST_TYPE="all"
      shift
      ;;
    --parallel=*)
      PARALLEL="${arg#*=}"
      shift
      ;;
    --retries=*)
      RETRIES="${arg#*=}"
      shift
      ;;
    --clean)
      CLEAN=true
      shift
      ;;
    --rebuild)
      REBUILD=true
      shift
      ;;
    --help)
      echo "Run E2E tests in Docker"
      echo "Usage: ./run-docker-tests.sh [options]"
      echo "Options:"
      echo "  --smoke         Run smoke tests only (default)"
      echo "  --regression    Run regression tests"
      echo "  --api           Run API tests"
      echo "  --a11y          Run accessibility tests"
      echo "  --security      Run security tests"
      echo "  --visual        Run visual regression tests"
      echo "  --all           Run all tests"
      echo "  --parallel=N    Run N workers in parallel (default: 2)"
      echo "  --retries=N     Retry failed tests N times (default: 1)"
      echo "  --clean         Clean up containers after tests"
      echo "  --rebuild       Rebuild containers before running tests"
      echo "  --help          Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $arg"
      echo "Use --help to see available options"
      exit 1
      ;;
  esac
done

# Print configuration
echo "=== Test Configuration ==="
echo "Test type: $TEST_TYPE"
echo "Parallel workers: $PARALLEL"
echo "Retry count: $RETRIES"
echo "Clean up: $CLEAN"
echo "Rebuild: $REBUILD"
echo "=========================="

# Build and start containers
if [ "$REBUILD" = true ]; then
  echo "Rebuilding containers..."
  docker-compose -f docker-compose.test.yml build --no-cache
fi

# Modify the command in docker-compose file
COMMAND="node ci-test.js --$TEST_TYPE --parallel=$PARALLEL --retries=$RETRIES && node analyze-results.js --html --json"
export E2E_COMMAND="$COMMAND"

# Start containers
echo "Starting containers..."
docker-compose -f docker-compose.test.yml up -d postgres backend frontend

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 10

# Run tests
echo "Running tests..."
docker-compose -f docker-compose.test.yml run --rm e2e sh -c "npx wait-on http://frontend:8080 -t 60000 && $COMMAND"

# Get exit code
EXIT_CODE=$?

# Clean up if requested
if [ "$CLEAN" = true ]; then
  echo "Cleaning up containers..."
  docker-compose -f docker-compose.test.yml down -v
fi

# Print results location
echo "Test results available in:"
echo "- ./playwright-report/index.html (HTML report)"
echo "- ./ci-reports/ (Analysis reports)"

# Exit with the same code as the tests
exit $EXIT_CODE
