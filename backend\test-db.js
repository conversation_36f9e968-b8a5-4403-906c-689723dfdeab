/**
 * Test script for database models
 * Run with: node test-db.js
 */

const { sequelize, WorkflowTemplate, WorkflowNode, WorkflowTask, WorkflowTaskHistory, User } = require('./src/models');

async function testDatabase() {
  try {
    console.log('Testing database connection...');
    await sequelize.authenticate();
    console.log('Database connection has been established successfully.');

    // Test WorkflowTaskHistory model
    console.log('\nTesting WorkflowTaskHistory model...');
    const taskHistoryAttributes = Object.keys(WorkflowTaskHistory.rawAttributes);
    console.log('WorkflowTaskHistory attributes:', taskHistoryAttributes);
    
    // Check if task_id is required
    const taskIdAttribute = WorkflowTaskHistory.rawAttributes.task_id;
    console.log('task_id attribute:', {
      type: taskIdAttribute.type.key,
      allowNull: taskIdAttribute.allowNull
    });

    // Test creating a task history record with null task_id
    try {
      console.log('\nTesting creating a task history record with null task_id...');
      const testHistory = await WorkflowTaskHistory.create({
        workflow_instance_id: 1,
        task_id: null,
        node_id: 1,
        operator_id: 1,
        operation: 'test',
        comments: 'Test record'
      });
      console.log('Successfully created task history record with null task_id:', testHistory.id);
    } catch (error) {
      console.error('Failed to create task history record with null task_id:', error.message);
    }

    console.log('\nDatabase tests completed.');
  } catch (error) {
    console.error('Unable to connect to the database:', error);
  } finally {
    await sequelize.close();
  }
}

testDatabase();
