/**
 * Test Data Management API
 * A comprehensive API for managing test data across different test environments
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class TestDataAPI {
  constructor(config = {}) {
    this.baseURL = config.baseURL || 'http://localhost:5273/api';
    this.authToken = config.authToken || '';
    this.dataDir = config.dataDir || path.join(__dirname, '../test-data');
    this.dataFile = config.dataFile || path.join(this.dataDir, 'test-data.json');
    this.testRunId = config.testRunId || this.generateTestRunId();
    
    // Create data directory if it doesn't exist
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
    
    // Initialize test data storage
    this.testData = {
      testRunId: this.testRunId,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'test',
      data: {
        users: [],
        departments: [],
        forms: [],
        workflows: [],
        instances: [],
        tasks: []
      },
      metadata: {}
    };
    
    // Load existing data if available
    this.loadTestData();
  }

  /**
   * Generate a unique test run ID
   * @returns {string} - Unique test run ID
   */
  generateTestRunId() {
    return crypto.randomBytes(8).toString('hex');
  }

  /**
   * Set auth token for API requests
   * @param {string} token - JWT token
   */
  setAuthToken(token) {
    this.authToken = token;
  }

  /**
   * Get request headers including auth token if available
   * @returns {object} - Headers object
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
    
    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }
    
    return headers;
  }

  /**
   * Make an API request
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {object} data - Request data
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Response data
   */
  async request(method, endpoint, data = null, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      method,
      url,
      headers: this.getHeaders(),
      ...options
    };

    if (data) {
      config.data = data;
    }

    try {
      const response = await axios(config);
      return {
        status: response.status,
        data: response.data,
        headers: response.headers,
        success: true
      };
    } catch (error) {
      if (error.response) {
        return {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers,
          success: false,
          error: error.message
        };
      }
      throw error;
    }
  }

  /**
   * Load test data from file
   */
  loadTestData() {
    try {
      if (fs.existsSync(this.dataFile)) {
        const data = fs.readFileSync(this.dataFile, 'utf8');
        const parsedData = JSON.parse(data);
        
        // Merge with existing data
        this.testData = {
          ...this.testData,
          ...parsedData,
          data: {
            ...this.testData.data,
            ...parsedData.data
          },
          metadata: {
            ...this.testData.metadata,
            ...parsedData.metadata
          }
        };
      }
    } catch (error) {
      console.error('Failed to load test data:', error.message);
    }
  }

  /**
   * Save test data to file
   */
  saveTestData() {
    try {
      fs.writeFileSync(
        this.dataFile,
        JSON.stringify(this.testData, null, 2)
      );
    } catch (error) {
      console.error('Failed to save test data:', error.message);
    }
  }

  /**
   * Add data to test data storage
   * @param {string} category - Data category (users, departments, etc.)
   * @param {object} data - Data to add
   * @returns {object} - Added data
   */
  addData(category, data) {
    if (!this.testData.data[category]) {
      this.testData.data[category] = [];
    }
    
    this.testData.data[category].push(data);
    this.saveTestData();
    
    return data;
  }

  /**
   * Get data from test data storage
   * @param {string} category - Data category (users, departments, etc.)
   * @param {function} filter - Filter function
   * @returns {Array<object>} - Filtered data
   */
  getData(category, filter = null) {
    if (!this.testData.data[category]) {
      return [];
    }
    
    if (filter) {
      return this.testData.data[category].filter(filter);
    }
    
    return this.testData.data[category];
  }

  /**
   * Set metadata
   * @param {string} key - Metadata key
   * @param {any} value - Metadata value
   */
  setMetadata(key, value) {
    this.testData.metadata[key] = value;
    this.saveTestData();
  }

  /**
   * Get metadata
   * @param {string} key - Metadata key
   * @returns {any} - Metadata value
   */
  getMetadata(key) {
    return this.testData.metadata[key];
  }

  /**
   * Create a user for testing
   * @param {object} userData - User data
   * @returns {Promise<object>} - Created user
   */
  async createUser(userData = {}) {
    const defaultData = {
      username: `testuser_${Date.now()}`,
      password: 'Test123!',
      full_name: 'Test User',
      email: `testuser_${Date.now()}@example.com`,
      roles: ['user']
    };

    const data = { ...defaultData, ...userData };
    
    try {
      const response = await this.request('post', '/users/register', data);
      if (response.success && response.data.success) {
        const user = response.data.data;
        this.addData('users', user);
        return user;
      }
      throw new Error(`Failed to create user: ${response.data.message || 'Unknown error'}`);
    } catch (error) {
      console.error('Create user failed:', error.message);
      throw error;
    }
  }

  /**
   * Create a department for testing
   * @param {object} departmentData - Department data
   * @returns {Promise<object>} - Created department
   */
  async createDepartment(departmentData = {}) {
    const defaultData = {
      name: `Test Department ${Date.now()}`,
      code: `TEST${Date.now()}`,
      description: 'Test department created for E2E testing',
      status: 'active'
    };

    const data = { ...defaultData, ...departmentData };
    
    try {
      const response = await this.request('post', '/departments', data);
      if (response.success && response.data.success) {
        const department = response.data.data;
        this.addData('departments', department);
        return department;
      }
      throw new Error(`Failed to create department: ${response.data.message || 'Unknown error'}`);
    } catch (error) {
      console.error('Create department failed:', error.message);
      throw error;
    }
  }

  /**
   * Create a form template for testing
   * @param {object} formData - Form data
   * @returns {Promise<object>} - Created form
   */
  async createForm(formData = {}) {
    const defaultData = {
      title: `Test Form ${Date.now()}`,
      description: 'Test form created for E2E testing',
      fields: [
        {
          type: 'input',
          label: 'Test Text Field',
          name: 'test_text',
          required: true,
          placeholder: 'Enter text'
        },
        {
          type: 'textarea',
          label: 'Test Textarea',
          name: 'test_textarea',
          required: false,
          placeholder: 'Enter description'
        },
        {
          type: 'select',
          label: 'Test Select',
          name: 'test_select',
          required: true,
          options: [
            { label: 'Option 1', value: 'option1' },
            { label: 'Option 2', value: 'option2' }
          ]
        }
      ],
      status: 'published'
    };

    const data = { ...defaultData, ...formData };
    
    try {
      const response = await this.request('post', '/forms', data);
      if (response.success && response.data.success) {
        const form = response.data.data;
        this.addData('forms', form);
        return form;
      }
      throw new Error(`Failed to create form: ${response.data.message || 'Unknown error'}`);
    } catch (error) {
      console.error('Create form failed:', error.message);
      throw error;
    }
  }

  /**
   * Create a workflow template for testing
   * @param {object} workflowData - Workflow data
   * @returns {Promise<object>} - Created workflow
   */
  async createWorkflow(workflowData = {}) {
    // Create a form first if no form_template_id provided
    let formTemplateId = workflowData.form_template_id;
    if (!formTemplateId) {
      try {
        const form = await this.createForm();
        formTemplateId = form.id;
      } catch (error) {
        console.error('Failed to create form for workflow:', error.message);
        throw error;
      }
    }

    const defaultData = {
      title: `Test Workflow ${Date.now()}`,
      description: 'Test workflow created for E2E testing',
      form_template_id: formTemplateId,
      nodes: [
        {
          id: 'start_node',
          type: 'start',
          name: 'Start',
          position: { x: 100, y: 100 }
        },
        {
          id: 'approval_node',
          type: 'approval',
          name: 'Approval',
          position: { x: 300, y: 100 },
          config: {
            approver_type: 'specific',
            approver_id: 1 // Admin user ID
          }
        },
        {
          id: 'end_node',
          type: 'end',
          name: 'End',
          position: { x: 500, y: 100 }
        }
      ],
      transitions: [
        {
          source: 'start_node',
          target: 'approval_node'
        },
        {
          source: 'approval_node',
          target: 'end_node'
        }
      ],
      status: 'published'
    };

    const data = { ...defaultData, ...workflowData };
    
    try {
      const response = await this.request('post', '/workflows', data);
      if (response.success && response.data.success) {
        const workflow = response.data.data;
        this.addData('workflows', workflow);
        return workflow;
      }
      throw new Error(`Failed to create workflow: ${response.data.message || 'Unknown error'}`);
    } catch (error) {
      console.error('Create workflow failed:', error.message);
      throw error;
    }
  }

  /**
   * Start a workflow instance for testing
   * @param {number} workflowId - Workflow template ID
   * @param {object} instanceData - Instance data
   * @returns {Promise<object>} - Created instance
   */
  async startWorkflowInstance(workflowId, instanceData = {}) {
    // Create a workflow first if no workflowId provided
    if (!workflowId) {
      try {
        const workflow = await this.createWorkflow();
        workflowId = workflow.id;
      } catch (error) {
        console.error('Failed to create workflow for instance:', error.message);
        throw error;
      }
    }

    const defaultData = {
      title: `Test Instance ${Date.now()}`,
      form_data: {
        test_text: 'Test value',
        test_textarea: 'Test description',
        test_select: 'option1'
      }
    };

    const data = { ...defaultData, ...instanceData };
    
    try {
      const response = await this.request('post', `/workflows/${workflowId}/instances`, data);
      if (response.success && response.data.success) {
        const instance = response.data.data;
        this.addData('instances', instance);
        return instance;
      }
      throw new Error(`Failed to start workflow instance: ${response.data.message || 'Unknown error'}`);
    } catch (error) {
      console.error('Start workflow instance failed:', error.message);
      throw error;
    }
  }

  /**
   * Process a workflow task
   * @param {number} taskId - Task ID
   * @param {object} taskData - Task data
   * @returns {Promise<object>} - Processed task
   */
  async processTask(taskId, taskData = {}) {
    const defaultData = {
      action: 'approve',
      comment: 'Test comment',
      data: {}
    };

    const data = { ...defaultData, ...taskData };
    
    try {
      const response = await this.request('post', `/workflows/tasks/${taskId}/process`, data);
      if (response.success && response.data.success) {
        const task = response.data.data;
        this.addData('tasks', task);
        return task;
      }
      throw new Error(`Failed to process task: ${response.data.message || 'Unknown error'}`);
    } catch (error) {
      console.error('Process task failed:', error.message);
      throw error;
    }
  }

  /**
   * Get todo tasks
   * @returns {Promise<Array<object>>} - Todo tasks
   */
  async getTodoTasks() {
    try {
      const response = await this.request('get', '/workflows/tasks/todo');
      if (response.success && response.data.success) {
        return response.data.data.tasks;
      }
      throw new Error(`Failed to get todo tasks: ${response.data.message || 'Unknown error'}`);
    } catch (error) {
      console.error('Get todo tasks failed:', error.message);
      throw error;
    }
  }

  /**
   * Get done tasks
   * @returns {Promise<Array<object>>} - Done tasks
   */
  async getDoneTasks() {
    try {
      const response = await this.request('get', '/workflows/tasks/done');
      if (response.success && response.data.success) {
        return response.data.data.taskHistories;
      }
      throw new Error(`Failed to get done tasks: ${response.data.message || 'Unknown error'}`);
    } catch (error) {
      console.error('Get done tasks failed:', error.message);
      throw error;
    }
  }

  /**
   * Clean up test data
   * @returns {Promise<void>}
   */
  async cleanup() {
    console.log('Cleaning up test data...');

    // Clean up instances
    for (const instance of this.testData.data.instances) {
      try {
        await this.request('delete', `/workflows/instances/${instance.id}`);
        console.log(`Deleted instance: ${instance.id}`);
      } catch (error) {
        console.error(`Failed to delete instance ${instance.id}:`, error.message);
      }
    }

    // Clean up workflows
    for (const workflow of this.testData.data.workflows) {
      try {
        await this.request('delete', `/workflows/${workflow.id}`);
        console.log(`Deleted workflow: ${workflow.id}`);
      } catch (error) {
        console.error(`Failed to delete workflow ${workflow.id}:`, error.message);
      }
    }

    // Clean up forms
    for (const form of this.testData.data.forms) {
      try {
        await this.request('delete', `/forms/${form.id}`);
        console.log(`Deleted form: ${form.id}`);
      } catch (error) {
        console.error(`Failed to delete form ${form.id}:`, error.message);
      }
    }

    // Clean up departments
    for (const department of this.testData.data.departments) {
      try {
        await this.request('delete', `/departments/${department.id}`);
        console.log(`Deleted department: ${department.id}`);
      } catch (error) {
        console.error(`Failed to delete department ${department.id}:`, error.message);
      }
    }

    // Clean up users (except admin and default user)
    for (const user of this.testData.data.users) {
      if (user.username !== 'admin' && user.username !== 'user') {
        try {
          await this.request('delete', `/users/${user.id}`);
          console.log(`Deleted user: ${user.id}`);
        } catch (error) {
          console.error(`Failed to delete user ${user.id}:`, error.message);
        }
      }
    }

    // Reset test data
    this.testData.data = {
      users: [],
      departments: [],
      forms: [],
      workflows: [],
      instances: [],
      tasks: []
    };
    
    this.saveTestData();
  }

  /**
   * Export test data to file
   * @param {string} filePath - File path
   */
  exportData(filePath) {
    try {
      fs.writeFileSync(
        filePath,
        JSON.stringify(this.testData, null, 2)
      );
      console.log(`Test data exported to ${filePath}`);
    } catch (error) {
      console.error('Failed to export test data:', error.message);
      throw error;
    }
  }

  /**
   * Import test data from file
   * @param {string} filePath - File path
   */
  importData(filePath) {
    try {
      const data = fs.readFileSync(filePath, 'utf8');
      this.testData = JSON.parse(data);
      this.saveTestData();
      console.log(`Test data imported from ${filePath}`);
    } catch (error) {
      console.error('Failed to import test data:', error.message);
      throw error;
    }
  }
}

module.exports = TestDataAPI;
