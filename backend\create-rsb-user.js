/**
 * Create rsb user script
 */
const bcrypt = require('bcryptjs');
const { sequelize, User, Role } = require('./src/models');

async function createRsbUser() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Check if user role exists
    let userRole = await Role.findOne({ where: { name: 'user' } });
    
    // Create user role if it doesn't exist
    if (!userRole) {
      console.log('Creating user role...');
      userRole = await Role.create({
        name: 'user',
        display_name: '普通用户',
        description: '普通用户，拥有基本权限'
      });
      console.log('User role created successfully.');
    } else {
      console.log('User role already exists.');
    }

    // Check if rsb user exists
    let rsbUser = await User.findOne({ where: { username: 'rsb' } });
    
    // Create rsb user if it doesn't exist
    if (!rsbUser) {
      console.log('Creating rsb user...');
      
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('password123', salt);
      
      // Create the user
      rsbUser = await User.create({
        username: 'rsb',
        password: hashedPassword,
        email: '<EMAIL>',
        full_name: 'RSB User',
        status: 'active'
      });
      
      // Assign user role to user
      await rsbUser.addRole(userRole);
      
      console.log('RSB user created successfully.');
    } else {
      console.log('RSB user already exists.');
      
      // Check if rsb user has user role
      const userRoles = await rsbUser.getRoles();
      const hasUserRole = userRoles.some(role => role.name === 'user');
      
      if (!hasUserRole) {
        console.log('Assigning user role to rsb user...');
        await rsbUser.addRole(userRole);
        console.log('User role assigned to rsb user.');
      }

      // Ensure user is active
      if (rsbUser.status !== 'active') {
        console.log('Activating rsb user...');
        rsbUser.status = 'active';
        await rsbUser.save();
        console.log('RSB user activated.');
      }
    }
    
    console.log('RSB user setup completed successfully.');
  } catch (error) {
    console.error('Error creating rsb user:', error);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

createRsbUser();
