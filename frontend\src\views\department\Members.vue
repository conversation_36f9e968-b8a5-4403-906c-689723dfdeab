<template>
  <div class="members-page">
    <h1>成员管理</h1>
    
    <!-- 部门选择 -->
    <div class="department-selector">
      <div class="search-bar">
        <el-select
          v-model="selectedDepartment"
          style="width: 300px; margin-right: 20px;"
          placeholder="请选择部门"
          @change="handleDepartmentChange"
          clearable
        >
          <el-option v-for="dept in departments" :key="dept.id" :value="dept.id" :label="dept.name"></el-option>
        </el-select>
        
        <el-button type="primary" @click="loadAllMembers">
          <el-icon><i-ep-search /></el-icon>
          查询所有成员
        </el-button>
        
        <el-button type="success" @click="showAddUserModal">
          <el-icon><i-ep-plus /></el-icon>
          添加新用户
        </el-button>
      </div>
    </div>

    <!-- 成员列表 -->
    <div class="members-list" v-if="isLoaded">
      <div class="table-actions" v-if="selectedDepartment">
        <el-button type="primary" @click="showAddMemberModal">
          <el-icon><i-ep-plus /></el-icon>
          添加成员到当前部门
        </el-button>
      </div>

      <el-table
        :data="members"
        :loading="loading"
        style="width: 100%"
        row-key="id"
      >
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :src="row.avatar || '/default-avatar.png'" />
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" sortable></el-table-column>
        <el-table-column prop="full_name" label="姓名" sortable></el-table-column>
        <el-table-column prop="email" label="邮箱"></el-table-column>
        <el-table-column label="所属角色">
          <template #default="{ row }">
            <div v-if="row.roles && row.roles.length > 0">
              <el-tag v-for="role in row.roles" :key="role.id" size="small" style="margin-right: 5px;">
                {{ role.name }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="所属部门">
          <template #default="{ row }">
            <div v-if="row.departments && row.departments.length > 0">
              <el-tag v-for="dept in row.departments" :key="dept.id" size="small" :type="dept.department_users?.is_primary ? 'success' : 'info'" style="margin-right: 5px;">
                {{ dept.name }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column v-if="selectedDepartment" label="职位">
          <template #default="{ row }">
            {{ row.department_users?.position || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column v-if="selectedDepartment" label="类型">
          <template #default="{ row }">
            <el-tag v-if="row.department_users?.is_primary" type="success">主部门</el-tag>
            <el-tag v-else type="info">辅部门</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template #default="{ row }">
            <el-button type="primary" text @click="showEditUserModal(row)">
              <el-icon><i-ep-edit /></el-icon>
              编辑用户
            </el-button>
            <el-button type="success" text @click="showEditRolesModal(row)">
              <el-icon><i-ep-user /></el-icon>
              分配角色
            </el-button>
            <el-button type="warning" text @click="showResetPasswordModal(row)">
              <el-icon><i-ep-key /></el-icon>
              重置密码
            </el-button>
            <template v-if="selectedDepartment">
              <el-button type="primary" text @click="showEditMemberModal(row)">
                编辑
              </el-button>
              <el-popconfirm
                title="确认移除该成员?"
                @confirm="removeMember(row)"
              >
                <template #reference>
                  <el-button type="danger" text>移除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加成员弹窗 -->
    <el-dialog
      v-model="addMemberModalVisible"
      title="添加部门成员"
      width="500px"
    >
      <el-form :model="memberForm" :rules="rules" ref="memberFormRef" label-width="80px">
        <el-form-item label="选择用户" prop="user_id">
          <el-select
            v-model="memberForm.user_id"
            placeholder="请选择用户"
            filterable
            style="width: 100%"
          >
            <el-option 
              v-for="user in availableUsers" 
              :key="user.id" 
              :value="user.id" 
              :label="`${user.full_name} (${user.username})`"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="职位" prop="position">
          <el-input v-model="memberForm.position" placeholder="请输入职位"></el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="memberForm.is_primary">设为主部门</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addMemberModalVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAddMember" :loading="modalLoading">确认</el-button>
      </template>
    </el-dialog>

    <!-- 编辑成员弹窗 -->
    <el-dialog
      v-model="editMemberModalVisible"
      title="编辑部门成员"
      width="500px"
    >
      <el-form :model="memberForm" :rules="rules" ref="editMemberFormRef" label-width="80px">
        <el-form-item label="用户信息">
          <div>{{ currentEditMember?.full_name }} ({{ currentEditMember?.username }})</div>
        </el-form-item>
        <el-form-item label="职位" prop="position">
          <el-input v-model="memberForm.position" placeholder="请输入职位"></el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="memberForm.is_primary">设为主部门</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editMemberModalVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateMember" :loading="modalLoading">确认</el-button>
      </template>
    </el-dialog>
    
    <!-- 添加新用户弹窗 -->
    <el-dialog
      v-model="addUserModalVisible"
      title="添加新用户"
      width="500px"
    >
      <el-form :model="userForm" :rules="userRules" ref="userFormRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入登录用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="userForm.confirmPassword" type="password" placeholder="请再次输入密码" show-password></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="full_name">
          <el-input v-model="userForm.full_name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入电话号码"></el-input>
        </el-form-item>
        <el-form-item label="所属部门" prop="primary_department">
          <el-select
            v-model="userForm.primary_department"
            placeholder="请选择部门"
            style="width: 100%"
            :disabled="false"
          >
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="角色" prop="roles">
          <el-select
            v-model="userForm.roles"
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="role in roles"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addUserModalVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAddUser" :loading="modalLoading">确认</el-button>
      </template>
    </el-dialog>
    
    <!-- 编辑用户弹窗 -->
    <el-dialog
      v-model="editUserModalVisible"
      title="编辑用户信息"
      width="500px"
    >
      <el-form :model="userForm" :rules="userEditRules" ref="editUserFormRef" label-width="100px">
        <el-form-item label="用户名">
          <div>{{ currentEditUser?.username }}</div>
        </el-form-item>
        <el-form-item label="姓名" prop="full_name">
          <el-input v-model="userForm.full_name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入电话号码"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editUserModalVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateUser" :loading="modalLoading">确认</el-button>
      </template>
    </el-dialog>
    
    <!-- 编辑角色弹窗 -->
    <el-dialog
      v-model="editRolesModalVisible"
      title="分配用户角色"
      width="500px"
    >
      <el-form :model="roleForm" ref="roleFormRef" label-width="100px">
        <el-form-item label="用户">
          <div>{{ currentEditUser?.full_name }} ({{ currentEditUser?.username }})</div>
        </el-form-item>
        <el-form-item label="角色" prop="role_ids">
          <el-select
            v-model="roleForm.role_ids"
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="role in roles"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editRolesModalVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAssignRoles" :loading="modalLoading">确认</el-button>
      </template>
    </el-dialog>
    
    <!-- 重置密码弹窗 -->
    <el-dialog
      v-model="resetPasswordModalVisible"
      title="重置用户密码"
      width="500px"
    >
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px">
        <el-form-item label="用户">
          <div>{{ currentEditUser?.full_name }} ({{ currentEditUser?.username }})</div>
        </el-form-item>
        <el-form-item label="新密码" prop="new_password">
          <el-input v-model="passwordForm.new_password" type="password" placeholder="请输入新密码" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirm_password">
          <el-input v-model="passwordForm.confirm_password" type="password" placeholder="请再次输入新密码" show-password></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="resetPasswordModalVisible = false">取消</el-button>
        <el-button type="primary" @click="handleResetPassword" :loading="modalLoading">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { departmentApi, userApi, roleApi } from '@/api'
import { ElMessage } from 'element-plus'

// 状态
const departments = ref([])
const members = ref([])
const allUsers = ref([])
const roles = ref([])
const loading = ref(false)
const modalLoading = ref(false)
const selectedDepartment = ref(null)
const isLoaded = ref(false)

// 弹窗控制
const addMemberModalVisible = ref(false)
const editMemberModalVisible = ref(false)
const addUserModalVisible = ref(false)
const editUserModalVisible = ref(false)
const editRolesModalVisible = ref(false)
const resetPasswordModalVisible = ref(false)

// 当前编辑对象
const currentEditMember = ref(null)
const currentEditUser = ref(null)

// 表单引用
const memberFormRef = ref(null)
const editMemberFormRef = ref(null)
const userFormRef = ref(null)
const editUserFormRef = ref(null)
const roleFormRef = ref(null)
const passwordFormRef = ref(null)

// 成员表单
const memberForm = reactive({
  user_id: null,
  position: '',
  is_primary: false
})

// 用户表单
const userForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  full_name: '',
  email: '',
  phone: '',
  roles: null,
  primary_department: null
})

// 角色表单
const roleForm = reactive({
  role_ids: null
})

// 密码表单
const passwordForm = reactive({
  new_password: '',
  confirm_password: ''
})

// 表单验证规则
const rules = {
  user_id: [{ required: true, message: '请选择用户', trigger: 'change' }]
}

// 用户表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在3到20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== userForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  full_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  primary_department: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  roles: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 用户编辑表单验证规则（不包含密码）
const userEditRules = {
  full_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 密码表单验证规则
const passwordRules = {
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 计算属性：可选用户（排除已在部门的用户）
const availableUsers = computed(() => {
  // 获取当前部门成员的ID列表
  const memberIds = members.value.map(member => member.id)
  // 返回不在当前部门的用户
  return allUsers.value.filter(user => !memberIds.includes(user.id))
})

// 获取部门名称
const getDepartmentName = (deptId) => {
  const dept = departments.value.find(d => d.id === deptId)
  return dept ? dept.name : '未知部门'
}

// 加载部门列表
const loadDepartments = async () => {
  try {
    loading.value = true
    const { success, data } = await departmentApi.getAllDepartments()
    if (success) {
      departments.value = data
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  } finally {
    loading.value = false
  }
}

// 加载所有用户
const loadAllUsers = async () => {
  try {
    loading.value = true
    const { success, data } = await userApi.getAllUsers()
    if (success) {
      members.value = data
      allUsers.value = data
      isLoaded.value = true
      selectedDepartment.value = null // 清除选中部门
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 加载角色列表
const loadRoles = async () => {
  try {
    // 先尝试使用正确的API加载角色
    const response = await roleApi.getAllRoles()
    if (response && response.success) {
      roles.value = response.data
      return
    }
    
    // 如果API调用失败，使用模拟数据
    console.warn('加载角色API失败，使用默认角色数据')
    roles.value = [
      { id: 1, name: 'admin', display_name: '管理员' },
      { id: 2, name: 'user', display_name: '普通用户' }
    ]
  } catch (error) {
    console.error('获取角色列表失败:', error)
    // 使用模拟数据作为备选
    roles.value = [
      { id: 1, name: 'admin', display_name: '管理员' },
      { id: 2, name: 'user', display_name: '普通用户' }
    ]
  }
}

// 加载部门成员
const loadDepartmentMembers = async (departmentId) => {
  if (!departmentId) return
  
  try {
    loading.value = true
    const { success, data } = await departmentApi.getDepartmentMembers(departmentId)
    if (success) {
      members.value = data
      isLoaded.value = true
    }
  } catch (error) {
    console.error(`获取部门(ID: ${departmentId})成员失败:`, error)
    ElMessage.error('获取部门成员失败')
  } finally {
    loading.value = false
  }
}

// 部门变更处理
const handleDepartmentChange = (value) => {
  if (value) {
    loadDepartmentMembers(value)
  } else {
    // 当清除部门选择时，显示所有用户
    loadAllMembers()
  }
}

// 显示添加成员弹窗
const showAddMemberModal = () => {
  // 重置表单
  Object.assign(memberForm, {
    user_id: null,
    position: '',
    is_primary: false
  })
  addMemberModalVisible.value = true
}

// 显示编辑成员弹窗
const showEditMemberModal = (record) => {
  currentEditMember.value = record
  Object.assign(memberForm, {
    position: record.department_users?.position || '',
    is_primary: !!record.department_users?.is_primary
  })
  editMemberModalVisible.value = true
}

// 显示添加用户弹窗
const showAddUserModal = () => {
  // 重置表单
  Object.assign(userForm, {
    username: '',
    password: '',
    confirmPassword: '',
    full_name: '',
    email: '',
    phone: '',
    roles: null,
    primary_department: null
  })
  addUserModalVisible.value = true
}

// 显示编辑用户弹窗
const showEditUserModal = (record) => {
  currentEditUser.value = record
  Object.assign(userForm, {
    full_name: record.full_name || '',
    email: record.email || '',
    phone: record.phone || ''
  })
  editUserModalVisible.value = true
}

// 显示编辑角色弹窗
const showEditRolesModal = (record) => {
  currentEditUser.value = record
  roleForm.role_ids = record.roles && record.roles.length > 0 ? record.roles[0].id : null
  editRolesModalVisible.value = true
}

// 显示重置密码弹窗
const showResetPasswordModal = (record) => {
  currentEditUser.value = record
  Object.assign(passwordForm, {
    new_password: '',
    confirm_password: ''
  })
  resetPasswordModalVisible.value = true
}

// 添加成员
const handleAddMember = async () => {
  try {
    await memberFormRef.value.validate()
    
    modalLoading.value = true
    const { success, message: msg } = await departmentApi.addDepartmentMember(selectedDepartment.value, {
      user_id: memberForm.user_id,
      position: memberForm.position,
      is_primary: memberForm.is_primary
    })
    
    if (success) {
      ElMessage.success('添加成员成功')
      addMemberModalVisible.value = false
      await loadDepartmentMembers(selectedDepartment.value)
    } else {
      ElMessage.error(msg || '添加成员失败')
    }
  } catch (error) {
    console.error('添加成员错误:', error)
    ElMessage.error('添加成员失败')
  } finally {
    modalLoading.value = false
  }
}

// 更新成员
const handleUpdateMember = async () => {
  try {
    if (editMemberFormRef.value) {
      await editMemberFormRef.value.validate()
    }
    
    modalLoading.value = true
    const { success, message: msg } = await departmentApi.updateDepartmentMember(
      selectedDepartment.value,
      currentEditMember.value.id,
      {
        position: memberForm.position,
        is_primary: memberForm.is_primary
      }
    )
    
    if (success) {
      ElMessage.success('更新成员成功')
      editMemberModalVisible.value = false
      await loadDepartmentMembers(selectedDepartment.value)
    } else {
      ElMessage.error(msg || '更新成员失败')
    }
  } catch (error) {
    console.error('更新成员错误:', error)
    ElMessage.error('更新成员失败')
  } finally {
    modalLoading.value = false
  }
}

// 移除成员
const removeMember = async (record) => {
  try {
    loading.value = true
    const { success, message: msg } = await departmentApi.removeDepartmentMember(
      selectedDepartment.value,
      record.id
    )
    
    if (success) {
      ElMessage.success('移除成员成功')
      await loadDepartmentMembers(selectedDepartment.value)
    } else {
      ElMessage.error(msg || '移除成员失败')
    }
  } catch (error) {
    console.error('移除成员错误:', error)
    ElMessage.error('移除成员失败')
  } finally {
    loading.value = false
  }
}

// 添加新用户
const handleAddUser = async () => {
  try {
    await userFormRef.value.validate()
    
    modalLoading.value = true
    // 注册用户
    const { success, message: msg, data } = await userApi.register({
      username: userForm.username,
      password: userForm.password,
      email: userForm.email,
      full_name: userForm.full_name,
      phone: userForm.phone
    })
    
    if (success && data) {
      // 如果选择了角色，给用户分配角色
      if (userForm.roles) {
        await userApi.assignRoles(data.id, {
          role_ids: [userForm.roles]
        })
      }
      
      // 只添加用户到主部门
      if (userForm.primary_department) {
        await departmentApi.addDepartmentMember(userForm.primary_department, {
          user_id: data.id,
          is_primary: true
        })
      }
      
      ElMessage.success('添加用户成功')
      addUserModalVisible.value = false
      
      // 刷新用户列表
      if (selectedDepartment.value) {
        await loadDepartmentMembers(selectedDepartment.value)
      } else {
        await loadAllUsers()
      }
    } else {
      ElMessage.error(msg || '添加用户失败')
    }
  } catch (error) {
    console.error('添加用户错误:', error)
    ElMessage.error('添加用户失败')
  } finally {
    modalLoading.value = false
  }
}

// 更新用户信息
const handleUpdateUser = async () => {
  try {
    await editUserFormRef.value.validate()
    
    modalLoading.value = true
    const { success, message: msg } = await userApi.updateUser(currentEditUser.value.id, {
      full_name: userForm.full_name,
      email: userForm.email,
      phone: userForm.phone
    })
    
    if (success) {
      ElMessage.success('更新用户信息成功')
      editUserModalVisible.value = false
      
      // 刷新用户列表
      if (selectedDepartment.value) {
        await loadDepartmentMembers(selectedDepartment.value)
      } else {
        await loadAllUsers()
      }
    } else {
      ElMessage.error(msg || '更新用户信息失败')
    }
  } catch (error) {
    console.error('更新用户信息错误:', error)
    ElMessage.error('更新用户信息失败')
  } finally {
    modalLoading.value = false
  }
}

// 分配角色
const handleAssignRoles = async () => {
  try {
    modalLoading.value = true
    const { success, message: msg } = await userApi.assignRoles(currentEditUser.value.id, {
      role_ids: roleForm.role_ids ? [roleForm.role_ids] : []
    })
    
    if (success) {
      ElMessage.success('分配角色成功')
      editRolesModalVisible.value = false
      
      // 刷新用户列表
      if (selectedDepartment.value) {
        await loadDepartmentMembers(selectedDepartment.value)
      } else {
        await loadAllUsers()
      }
    } else {
      ElMessage.error(msg || '分配角色失败')
    }
  } catch (error) {
    console.error('分配角色错误:', error)
    ElMessage.error('分配角色失败')
  } finally {
    modalLoading.value = false
  }
}

// 重置密码
const handleResetPassword = async () => {
  try {
    await passwordFormRef.value.validate()
    
    modalLoading.value = true
    const { success, message: msg } = await userApi.resetPassword(currentEditUser.value.id, {
      new_password: passwordForm.new_password
    })
    
    if (success) {
      ElMessage.success('密码重置成功')
      resetPasswordModalVisible.value = false
    } else {
      ElMessage.error(msg || '密码重置失败')
    }
  } catch (error) {
    console.error('重置密码错误:', error)
    ElMessage.error('重置密码失败')
  } finally {
    modalLoading.value = false
  }
}

// 初始化
onMounted(() => {
  loadDepartments()
  loadRoles()
  loadAllUsers() // 默认加载所有用户
})
</script>

<style scoped>
.members-page {
  padding: 20px;
}

.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.table-actions {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 