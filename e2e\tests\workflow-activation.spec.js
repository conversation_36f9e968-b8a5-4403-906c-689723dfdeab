const { test, expect } = require('@playwright/test');

// Test user credentials
const TEST_USER = {
  username: 'admin',
  password: 'admin123'
};

test.describe('Workflow Activation Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');

    // Login
    await page.fill('input[placeholder="用户名"]', TEST_USER.username);
    await page.fill('input[placeholder="密码"]', TEST_USER.password);
    await page.click('.login-button');

    // Verify login was successful - wait for navigation
    await page.waitForURL(/.*\//);
  });

  test('Create and publish a workflow', async ({ page }) => {
    // Navigate to home page and then to workflow design through the UI
    await page.goto('/');
    
    // Find and click on the workflow design module button
    await page.locator('button:has-text("进入模块")').filter({ hasText: /工作流设计/ }).click();
    
    // Wait for the page to load
    await page.waitForURL(/.*workflow-design/);
    
    // Click on create workflow button
    await page.click('button:has-text("创建工作流")');

    // Fill workflow information
    await page.fill('input[placeholder="请输入工作流名称"]', 'Test Workflow ' + Date.now());
    await page.fill('textarea[placeholder="请输入工作流描述"]', 'Test workflow description');

    // Select a form template
    const formSelect = page.locator('div.el-select').filter({ hasText: '请选择关联表单' });
    await formSelect.click();
    await page.click('.el-select-dropdown__item:first-child');

    // Set status to published
    const statusSelect = page.locator('div.el-select').filter({ hasText: '草稿' });
    await statusSelect.click();
    await page.click('.el-select-dropdown__item:has-text("已发布")');

    // Add nodes (assuming the workflow designer already has a start node)
    // Add end node
    const endNodeItem = page.locator('.component-item').filter({ hasText: '结束节点' });
    const canvas = page.locator('.workflow-canvas');

    // Drag and drop end node to canvas
    const canvasBoundingBox = await canvas.boundingBox();
    await endNodeItem.dragTo(canvas, {
      targetPosition: {
        x: canvasBoundingBox.x + canvasBoundingBox.width / 2 + 200,
        y: canvasBoundingBox.y + canvasBoundingBox.height / 2
      }
    });

    // Create a connection between start and end nodes
    const startNode = page.locator('.workflow-node.start-node');
    const endNode = page.locator('.workflow-node.end-node');

    // Click on start node port and drag to end node
    const startNodeBoundingBox = await startNode.boundingBox();
    const endNodeBoundingBox = await endNode.boundingBox();

    await page.mouse.move(
      startNodeBoundingBox.x + startNodeBoundingBox.width,
      startNodeBoundingBox.y + startNodeBoundingBox.height / 2
    );
    await page.mouse.down();
    await page.mouse.move(
      endNodeBoundingBox.x,
      endNodeBoundingBox.y + endNodeBoundingBox.height / 2
    );
    await page.mouse.up();

    // Save workflow
    await page.click('button:has-text("保存")');

    // Verify success message
  test('Start a workflow instance from a published workflow', async ({ page }) => {
    // Navigate to home page and then to workflow form through the UI
    await page.goto('/');
    
    // Find and click on the workflow form module button
    await page.locator('button:has-text("进入模块")').filter({ hasText: /工作流填写/ }).click();
    
    // Wait for the page to load
    await page.waitForURL(/.*workflow-form/);
    
    // Increase timeout for table rows to appear and add retry logic
    await page.waitForSelector('.el-table__row', { timeout: 10000 }).catch(async () => {
      // If no workflows available, we need to create one first
      console.log('No workflows found, skipping this test');
      test.skip();
    });
  test('Start a workflow instance from a published workflow', async ({ page }) => {
    // Navigate to workflow form page
    await page.goto('/workflow-form');

    // Verify there's at least one available workflow
    await expect(page.locator('.el-table__row')).toBeVisible();

    // Click on the first workflow's "发起" button
    await page.click('.el-table__row >> button:has-text("发起")');

    // Fill the form
    await page.fill('input[placeholder="请输入工作流标题"]', 'Test Instance ' + Date.now());

    // Fill form fields (this will depend on the actual form structure)
    // For text inputs:
    const textInputs = await page.locator('input[type="text"]:not([placeholder="请输入工作流标题"])').all();
    for (const input of textInputs) {
      await input.fill('Test value');
    }

    // For textareas:
    const textareas = await page.locator('textarea').all();
    for (const textarea of textareas) {
      await textarea.fill('Test description');
    }

    // Submit the form
    await page.click('button:has-text("提交")');

    // Verify success message
    await expect(page.locator('.el-message')).toContainText('工作流发起成功');

    // Verify we're redirected to the "我发起的工作流" tab
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('我发起的工作流');

    // Verify the new instance appears in the list
    await expect(page.locator('.el-table__row')).toBeVisible();
  });
});
