/**
 * Test Data Generator
 * Generates realistic test data for various scenarios
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class TestDataGenerator {
  constructor(config = {}) {
    this.locale = config.locale || 'zh-CN';
    this.seed = config.seed || this.generateSeed();
    this.dataDir = config.dataDir || path.join(__dirname, '../test-data');
    this.templatesDir = config.templatesDir || path.join(this.dataDir, 'templates');
    
    // Create directories if they don't exist
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
    
    if (!fs.existsSync(this.templatesDir)) {
      fs.mkdirSync(this.templatesDir, { recursive: true });
    }
    
    // Initialize random number generator with seed
    this.random = this.createRandomGenerator(this.seed);
    
    // Load templates if available
    this.templates = this.loadTemplates();
  }

  /**
   * Generate a random seed
   * @returns {string} - Random seed
   */
  generateSeed() {
    return crypto.randomBytes(8).toString('hex');
  }

  /**
   * Create a seeded random number generator
   * @param {string} seed - Seed for random number generator
   * @returns {function} - Random number generator
   */
  createRandomGenerator(seed) {
    const seedNum = parseInt(seed, 16) % 2147483647;
    let state = seedNum;
    
    // Simple LCG random number generator
    return () => {
      state = (state * 48271) % 2147483647;
      return state / 2147483647;
    };
  }

  /**
   * Load templates from templates directory
   * @returns {object} - Templates
   */
  loadTemplates() {
    const templates = {};
    
    try {
      // Check if templates directory exists
      if (fs.existsSync(this.templatesDir)) {
        // Read all JSON files in templates directory
        const files = fs.readdirSync(this.templatesDir)
          .filter(file => file.endsWith('.json'));
        
        // Load each template
        for (const file of files) {
          const templateName = path.basename(file, '.json');
          const templatePath = path.join(this.templatesDir, file);
          
          try {
            const templateData = JSON.parse(fs.readFileSync(templatePath, 'utf8'));
            templates[templateName] = templateData;
          } catch (error) {
            console.error(`Failed to load template ${templateName}:`, error.message);
          }
        }
      }
    } catch (error) {
      console.error('Failed to load templates:', error.message);
    }
    
    return templates;
  }

  /**
   * Save a template
   * @param {string} name - Template name
   * @param {object} data - Template data
   */
  saveTemplate(name, data) {
    try {
      const templatePath = path.join(this.templatesDir, `${name}.json`);
      fs.writeFileSync(templatePath, JSON.stringify(data, null, 2));
      this.templates[name] = data;
    } catch (error) {
      console.error(`Failed to save template ${name}:`, error.message);
      throw error;
    }
  }

  /**
   * Get a random integer between min and max (inclusive)
   * @param {number} min - Minimum value
   * @param {number} max - Maximum value
   * @returns {number} - Random integer
   */
  getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(this.random() * (max - min + 1)) + min;
  }

  /**
   * Get a random element from an array
   * @param {Array} array - Array to pick from
   * @returns {any} - Random element
   */
  getRandomElement(array) {
    return array[this.getRandomInt(0, array.length - 1)];
  }

  /**
   * Get a random boolean
   * @param {number} probability - Probability of true (0-1)
   * @returns {boolean} - Random boolean
   */
  getRandomBoolean(probability = 0.5) {
    return this.random() < probability;
  }

  /**
   * Generate a random string
   * @param {number} length - String length
   * @param {string} charset - Character set to use
   * @returns {string} - Random string
   */
  generateRandomString(length = 10, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(this.random() * charset.length));
    }
    return result;
  }

  /**
   * Generate a random Chinese name
   * @returns {string} - Random Chinese name
   */
  generateChineseName() {
    const familyNames = [
      '李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
      '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
      '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧'
    ];
    
    const givenNames = [
      '伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军',
      '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞',
      '平', '刚', '桂英', '玲', '桂兰', '志强', '春梅', '建国', '建华', '建军',
      '建平', '建设', '国强', '国庆', '国栋', '国华', '国辉', '国梁', '国良', '国亮'
    ];
    
    return this.getRandomElement(familyNames) + this.getRandomElement(givenNames);
  }

  /**
   * Generate a random email
   * @param {string} name - Name to use in email
   * @returns {string} - Random email
   */
  generateEmail(name = null) {
    const domains = ['example.com', 'test.com', 'company.cn', 'mail.cn', 'qq.com', '163.com', '126.com', 'gmail.com'];
    const username = name ? name.replace(/\s+/g, '.').toLowerCase() : this.generateRandomString(8, 'abcdefghijklmnopqrstuvwxyz');
    return `${username}@${this.getRandomElement(domains)}`;
  }

  /**
   * Generate a random phone number
   * @returns {string} - Random phone number
   */
  generatePhoneNumber() {
    const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139', '150', '151', '152', '153', '155', '156', '157', '158', '159', '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'];
    return this.getRandomElement(prefixes) + this.generateRandomString(8, '0123456789');
  }

  /**
   * Generate a random date
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Date} - Random date
   */
  generateDate(startDate = new Date(2000, 0, 1), endDate = new Date()) {
    const startTime = startDate.getTime();
    const endTime = endDate.getTime();
    const randomTime = startTime + (this.random() * (endTime - startTime));
    return new Date(randomTime);
  }

  /**
   * Generate a random address
   * @returns {object} - Random address
   */
  generateAddress() {
    const provinces = ['北京市', '上海市', '天津市', '重庆市', '河北省', '山西省', '辽宁省', '吉林省', '黑龙江省', '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省', '河南省', '湖北省', '湖南省', '广东省', '海南省', '四川省', '贵州省', '云南省', '陕西省', '甘肃省', '青海省', '台湾省', '内蒙古自治区', '广西壮族自治区', '西藏自治区', '宁夏回族自治区', '新疆维吾尔自治区', '香港特别行政区', '澳门特别行政区'];
    
    const cities = {
      '北京市': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '顺义区', '通州区', '大兴区', '房山区', '门头沟区', '昌平区', '平谷区', '密云区', '怀柔区', '延庆区'],
      '上海市': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'],
      '广东省': ['广州市', '深圳市', '珠海市', '汕头市', '韶关市', '佛山市', '江门市', '湛江市', '茂名市', '肇庆市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市']
    };
    
    const streets = ['人民路', '中山路', '解放路', '建设路', '和平路', '民族路', '友谊路', '胜利路', '新华路', '长江路', '黄河路', '珠江路', '南京路', '北京路', '上海路', '广州路'];
    
    const province = this.getRandomElement(provinces);
    const city = cities[province] ? this.getRandomElement(cities[province]) : `${province}城市`;
    const street = this.getRandomElement(streets);
    const number = this.getRandomInt(1, 999);
    
    return {
      province,
      city,
      street,
      number,
      full: `${province}${city}${street}${number}号`
    };
  }

  /**
   * Generate a random department
   * @returns {object} - Random department
   */
  generateDepartment() {
    const departments = [
      { name: '人力资源部', code: 'HR' },
      { name: '财务部', code: 'FIN' },
      { name: '市场部', code: 'MKT' },
      { name: '销售部', code: 'SALES' },
      { name: '技术部', code: 'TECH' },
      { name: '研发部', code: 'RD' },
      { name: '产品部', code: 'PROD' },
      { name: '客服部', code: 'CS' },
      { name: '行政部', code: 'ADMIN' },
      { name: '法务部', code: 'LEGAL' }
    ];
    
    const department = this.getRandomElement(departments);
    const timestamp = Date.now();
    
    return {
      name: `${department.name}_${timestamp}`,
      code: `${department.code}_${timestamp}`,
      description: `${department.name}测试数据`,
      status: this.getRandomBoolean(0.9) ? 'active' : 'inactive'
    };
  }

  /**
   * Generate a random user
   * @returns {object} - Random user
   */
  generateUser() {
    const name = this.generateChineseName();
    const timestamp = Date.now();
    
    return {
      username: `user_${timestamp}`,
      password: 'Test123!',
      full_name: name,
      email: this.generateEmail(name),
      phone: this.generatePhoneNumber(),
      roles: this.getRandomBoolean(0.2) ? ['admin'] : ['user']
    };
  }

  /**
   * Generate a random form field
   * @param {string} type - Field type
   * @returns {object} - Random form field
   */
  generateFormField(type = null) {
    const fieldTypes = ['input', 'textarea', 'number', 'select', 'radio', 'checkbox', 'date', 'time', 'datetime'];
    const fieldType = type || this.getRandomElement(fieldTypes);
    const timestamp = Date.now();
    
    const baseField = {
      type: fieldType,
      label: `测试${fieldType}字段_${timestamp}`,
      name: `test_${fieldType}_${timestamp}`,
      required: this.getRandomBoolean(0.7),
      placeholder: `请输入${fieldType}`,
      description: `这是一个测试${fieldType}字段`
    };
    
    switch (fieldType) {
      case 'select':
      case 'radio':
      case 'checkbox':
        return {
          ...baseField,
          options: [
            { label: '选项1', value: 'option1' },
            { label: '选项2', value: 'option2' },
            { label: '选项3', value: 'option3' }
          ]
        };
      case 'number':
        return {
          ...baseField,
          min: 0,
          max: 100,
          step: 1
        };
      case 'date':
      case 'time':
      case 'datetime':
        return {
          ...baseField,
          format: fieldType === 'date' ? 'YYYY-MM-DD' : (fieldType === 'time' ? 'HH:mm:ss' : 'YYYY-MM-DD HH:mm:ss')
        };
      default:
        return baseField;
    }
  }

  /**
   * Generate a random form
   * @param {number} fieldCount - Number of fields
   * @returns {object} - Random form
   */
  generateForm(fieldCount = 5) {
    const timestamp = Date.now();
    const fields = [];
    
    // Generate random fields
    for (let i = 0; i < fieldCount; i++) {
      fields.push(this.generateFormField());
    }
    
    return {
      title: `测试表单_${timestamp}`,
      description: `这是一个测试表单，包含${fieldCount}个字段`,
      fields,
      status: 'published'
    };
  }

  /**
   * Generate a random workflow node
   * @param {string} type - Node type
   * @param {object} position - Node position
   * @returns {object} - Random workflow node
   */
  generateWorkflowNode(type = null, position = null) {
    const nodeTypes = ['start', 'approval', 'condition', 'task', 'end'];
    const nodeType = type || this.getRandomElement(nodeTypes);
    const timestamp = Date.now();
    const nodeId = `${nodeType}_node_${timestamp}`;
    
    const baseNode = {
      id: nodeId,
      type: nodeType,
      name: `${nodeType}节点_${timestamp}`,
      position: position || { x: this.getRandomInt(100, 800), y: this.getRandomInt(100, 600) }
    };
    
    switch (nodeType) {
      case 'approval':
        return {
          ...baseNode,
          config: {
            approver_type: this.getRandomElement(['specific', 'role', 'department']),
            approver_id: this.getRandomInt(1, 10)
          }
        };
      case 'condition':
        return {
          ...baseNode,
          config: {
            conditions: [
              {
                field: 'test_field',
                operator: this.getRandomElement(['==', '!=', '>', '<', '>=', '<=']),
                value: this.getRandomInt(1, 100)
              }
            ]
          }
        };
      case 'task':
        return {
          ...baseNode,
          config: {
            assignee_type: this.getRandomElement(['specific', 'role', 'department']),
            assignee_id: this.getRandomInt(1, 10),
            due_days: this.getRandomInt(1, 7)
          }
        };
      default:
        return baseNode;
    }
  }

  /**
   * Generate a random workflow
   * @param {number} formId - Form ID
   * @returns {object} - Random workflow
   */
  generateWorkflow(formId = null) {
    const timestamp = Date.now();
    
    // Generate nodes
    const startNode = this.generateWorkflowNode('start', { x: 100, y: 300 });
    const approvalNode = this.generateWorkflowNode('approval', { x: 300, y: 300 });
    const endNode = this.generateWorkflowNode('end', { x: 500, y: 300 });
    
    // Generate transitions
    const transitions = [
      {
        source: startNode.id,
        target: approvalNode.id
      },
      {
        source: approvalNode.id,
        target: endNode.id
      }
    ];
    
    return {
      title: `测试工作流_${timestamp}`,
      description: `这是一个测试工作流`,
      form_template_id: formId || this.getRandomInt(1, 10),
      nodes: [startNode, approvalNode, endNode],
      transitions,
      status: 'published'
    };
  }

  /**
   * Generate a random workflow instance
   * @param {number} workflowId - Workflow ID
   * @param {object} formData - Form data
   * @returns {object} - Random workflow instance
   */
  generateWorkflowInstance(workflowId = null, formData = null) {
    const timestamp = Date.now();
    
    return {
      title: `测试工作流实例_${timestamp}`,
      workflow_template_id: workflowId || this.getRandomInt(1, 10),
      form_data: formData || {
        test_input: `测试输入_${timestamp}`,
        test_textarea: `测试多行文本_${timestamp}`,
        test_select: 'option1'
      }
    };
  }

  /**
   * Generate data from template
   * @param {string} templateName - Template name
   * @param {object} overrides - Override values
   * @returns {object} - Generated data
   */
  generateFromTemplate(templateName, overrides = {}) {
    if (!this.templates[templateName]) {
      throw new Error(`Template ${templateName} not found`);
    }
    
    const template = this.templates[templateName];
    return this.processTemplate(template, overrides);
  }

  /**
   * Process template recursively
   * @param {object|Array|string} template - Template to process
   * @param {object} overrides - Override values
   * @returns {object|Array|string} - Processed template
   */
  processTemplate(template, overrides = {}) {
    if (typeof template === 'string') {
      // Check if it's a generator function
      if (template.startsWith('{{') && template.endsWith('}}')) {
        const generator = template.slice(2, -2).trim();
        return this.executeGenerator(generator);
      }
      return template;
    } else if (Array.isArray(template)) {
      return template.map(item => this.processTemplate(item, overrides));
    } else if (typeof template === 'object' && template !== null) {
      const result = {};
      for (const key in template) {
        if (overrides.hasOwnProperty(key)) {
          result[key] = overrides[key];
        } else {
          result[key] = this.processTemplate(template[key], overrides);
        }
      }
      return result;
    }
    return template;
  }

  /**
   * Execute generator function
   * @param {string} generator - Generator function
   * @returns {any} - Generated value
   */
  executeGenerator(generator) {
    const [func, ...args] = generator.split('|');
    
    switch (func.trim()) {
      case 'randomString':
        return this.generateRandomString(args[0] ? parseInt(args[0]) : 10);
      case 'chineseName':
        return this.generateChineseName();
      case 'email':
        return this.generateEmail(args[0]);
      case 'phone':
        return this.generatePhoneNumber();
      case 'date':
        return this.generateDate().toISOString();
      case 'int':
        return this.getRandomInt(
          args[0] ? parseInt(args[0]) : 1,
          args[1] ? parseInt(args[1]) : 100
        );
      case 'boolean':
        return this.getRandomBoolean(args[0] ? parseFloat(args[0]) : 0.5);
      case 'address':
        return this.generateAddress().full;
      case 'timestamp':
        return Date.now();
      default:
        return generator;
    }
  }

  /**
   * Generate a batch of data
   * @param {string} type - Data type
   * @param {number} count - Number of items to generate
   * @param {object} options - Generation options
   * @returns {Array<object>} - Generated data
   */
  generateBatch(type, count = 10, options = {}) {
    const results = [];
    
    for (let i = 0; i < count; i++) {
      let data;
      
      switch (type) {
        case 'user':
          data = this.generateUser();
          break;
        case 'department':
          data = this.generateDepartment();
          break;
        case 'form':
          data = this.generateForm(options.fieldCount || 5);
          break;
        case 'workflow':
          data = this.generateWorkflow(options.formId);
          break;
        case 'instance':
          data = this.generateWorkflowInstance(options.workflowId, options.formData);
          break;
        default:
          if (this.templates[type]) {
            data = this.generateFromTemplate(type, options);
          } else {
            throw new Error(`Unknown data type: ${type}`);
          }
      }
      
      results.push(data);
    }
    
    return results;
  }

  /**
   * Save generated data to file
   * @param {Array<object>|object} data - Data to save
   * @param {string} filename - Filename
   */
  saveGeneratedData(data, filename) {
    try {
      const filePath = path.join(this.dataDir, filename);
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      console.log(`Generated data saved to ${filePath}`);
    } catch (error) {
      console.error(`Failed to save generated data:`, error.message);
      throw error;
    }
  }
}

module.exports = TestDataGenerator;
