const { Department, User, Sequelize } = require('../models');

// 获取所有部门
exports.getAllDepartments = async (req, res) => {
  try {
    console.log('Department model:', Department);
    console.log('User model:', User);

    // 简化查询，先不使用关联
    const departments = await Department.findAll();

    res.json({
      success: true,
      data: departments
    });
  } catch (error) {
    console.error('获取部门列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取单个部门
exports.getDepartmentById = async (req, res) => {
  try {
    const { id } = req.params;

    const department = await Department.findByPk(id, {
      include: [
        {
          model: Department,
          as: 'children',
          include: [
            {
              model: User,
              as: 'manager',
              attributes: ['id', 'username', 'full_name']
            }
          ]
        },
        {
          model: Department,
          as: 'parent'
        },
        {
          model: User,
          as: 'manager',
          attributes: ['id', 'username', 'full_name']
        },
        {
          model: User,
          as: 'members',
          attributes: ['id', 'username', 'full_name', 'email'],
          through: {
            attributes: ['is_primary', 'position']
          }
        }
      ]
    });

    if (!department) {
      return res.status(404).json({
        success: false,
        message: '部门不存在'
      });
    }

    res.json({
      success: true,
      data: department
    });
  } catch (error) {
    console.error('获取部门详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 创建部门
exports.createDepartment = async (req, res) => {
  try {
    const { name, code, parent_id, manager_id, description } = req.body;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    // 检查部门代码是否已存在
    if (code) {
      const existingDepartment = await Department.findOne({ where: { code } });
      if (existingDepartment) {
        return res.status(400).json({
          success: false,
          message: '部门代码已存在'
        });
      }
    }

    // 创建部门
    const department = await Department.create({
      name,
      code,
      parent_id: parent_id || null,
      manager_id: manager_id || null,
      description,
      status: 'active'
    });

    res.status(201).json({
      success: true,
      message: '部门创建成功',
      data: department
    });
  } catch (error) {
    console.error('创建部门错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 更新部门
exports.updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, parent_id, manager_id, description, status } = req.body;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const department = await Department.findByPk(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: '部门不存在'
      });
    }

    // 检查部门代码是否已存在
    if (code && code !== department.code) {
      const existingDepartment = await Department.findOne({ where: { code } });
      if (existingDepartment) {
        return res.status(400).json({
          success: false,
          message: '部门代码已存在'
        });
      }
    }

    // 更新部门
    await department.update({
      name: name || department.name,
      code: code || department.code,
      parent_id: parent_id !== undefined ? parent_id : department.parent_id,
      manager_id: manager_id !== undefined ? manager_id : department.manager_id,
      description: description || department.description,
      status: status || department.status
    });

    res.json({
      success: true,
      message: '部门更新成功',
      data: department
    });
  } catch (error) {
    console.error('更新部门错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 删除部门
exports.deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const department = await Department.findByPk(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: '部门不存在'
      });
    }

    // 检查是否有子部门
    const childDepartments = await Department.findAll({ where: { parent_id: id } });
    if (childDepartments.length > 0) {
      return res.status(400).json({
        success: false,
        message: '无法删除有子部门的部门，请先删除子部门'
      });
    }

    await department.destroy();

    res.json({
      success: true,
      message: '部门删除成功'
    });
  } catch (error) {
    console.error('删除部门错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取部门成员
exports.getDepartmentMembers = async (req, res) => {
  try {
    const { id } = req.params;

    const department = await Department.findByPk(id, {
      include: [
        {
          model: User,
          as: 'members',
          attributes: ['id', 'username', 'full_name', 'email', 'avatar', 'phone'],
          through: {
            attributes: ['is_primary', 'position']
          }
        }
      ]
    });

    if (!department) {
      return res.status(404).json({
        success: false,
        message: '部门不存在'
      });
    }

    res.json({
      success: true,
      data: department.members
    });
  } catch (error) {
    console.error('获取部门成员错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 添加部门成员
exports.addDepartmentMember = async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id, is_primary, position } = req.body;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const department = await Department.findByPk(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: '部门不存在'
      });
    }

    const user = await User.findByPk(user_id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查用户是否已经是部门成员
    const existingMember = await department.hasMembers(user);
    if (existingMember) {
      return res.status(400).json({
        success: false,
        message: '该用户已经是部门成员'
      });
    }

    // 如果设置为主部门，需要将用户的其他部门设置为非主部门
    if (is_primary) {
      const { DepartmentUser } = require('../models');
      await DepartmentUser.update(
        { is_primary: false },
        { where: { user_id } }
      );
    }

    // 添加成员
    await department.addMember(user, {
      through: {
        is_primary: is_primary || false,
        position: position || null
      }
    });

    res.json({
      success: true,
      message: '成员添加成功'
    });
  } catch (error) {
    console.error('添加部门成员错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 更新部门成员
exports.updateDepartmentMember = async (req, res) => {
  try {
    const { id, userId } = req.params;
    const { is_primary, position } = req.body;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const department = await Department.findByPk(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: '部门不存在'
      });
    }

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查用户是否是部门成员
    const isMember = await department.hasMembers(user);
    if (!isMember) {
      return res.status(404).json({
        success: false,
        message: '该用户不是部门成员'
      });
    }

    // 如果设置为主部门，需要将用户的其他部门设置为非主部门
    if (is_primary) {
      const { DepartmentUser } = require('../models');
      await DepartmentUser.update(
        { is_primary: false },
        { where: { user_id: userId } }
      );
    }

    // 更新成员信息
    const { DepartmentUser } = require('../models');
    await DepartmentUser.update(
      {
        is_primary: is_primary !== undefined ? is_primary : false,
        position: position !== undefined ? position : null
      },
      {
        where: {
          department_id: id,
          user_id: userId
        }
      }
    );

    res.json({
      success: true,
      message: '成员更新成功'
    });
  } catch (error) {
    console.error('更新部门成员错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 移除部门成员
exports.removeDepartmentMember = async (req, res) => {
  try {
    const { id, userId } = req.params;

    // 检查权限
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限执行此操作'
      });
    }

    const department = await Department.findByPk(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: '部门不存在'
      });
    }

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 移除成员
    await department.removeMember(user);

    res.json({
      success: true,
      message: '成员移除成功'
    });
  } catch (error) {
    console.error('移除部门成员错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};
