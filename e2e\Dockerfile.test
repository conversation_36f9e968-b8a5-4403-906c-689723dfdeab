FROM mcr.microsoft.com/playwright:v1.40.0-focal

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy test files
COPY . .

# Create directories for test results
RUN mkdir -p test-results playwright-report ci-reports

# Set environment variables
ENV CI=true
ENV NODE_ENV=test

# Command to run tests (will be overridden by docker-compose)
CMD ["node", "ci-test.js", "--smoke"]
