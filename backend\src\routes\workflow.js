const express = require('express');
const router = express.Router();
const workflowController = require('../controllers/workflow');
const { authenticate } = require('../middlewares/auth');

/**
 * @swagger
 * tags:
 *   name: Workflows
 *   description: 工作流管理API
 */

/**
 * @swagger
 * /workflows:
 *   get:
 *     summary: 获取所有工作流模板
 *     tags: [Workflows]
 *     responses:
 *       200:
 *         description: 成功获取工作流模板列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       title:
 *                         type: string
 *                         example: "请假审批流程"
 *                       description:
 *                         type: string
 *                         example: "员工请假审批流程"
 *                       status:
 *                         type: string
 *                         example: "published"
 *                       version:
 *                         type: integer
 *                         example: 1
 *                       form_template_id:
 *                         type: integer
 *                         example: 1
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-06-01T10:00:00Z"
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-06-01T10:00:00Z"
 *       500:
 *         description: 服务器错误
 */
router.get('/', workflowController.getAllWorkflows);

/**
 * @swagger
 * /workflows/{id}:
 *   get:
 *     summary: 获取单个工作流模板
 *     description: 获取指定ID的工作流模板详细信息，包括节点和连线配置
 *     tags: [Workflows]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工作流模板ID
 *     responses:
 *       200:
 *         description: 成功获取工作流模板
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/WorkflowTemplate'
 *       404:
 *         description: 工作流模板不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id', workflowController.getWorkflowById);

/**
 * @swagger
 * /workflows:
 *   post:
 *     summary: 创建工作流模板
 *     description: 创建新的工作流模板，包括节点和连线配置
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - form_template_id
 *             properties:
 *               title:
 *                 type: string
 *                 example: "请假审批流程"
 *               description:
 *                 type: string
 *                 example: "员工请假审批流程，包含部门经理和人事审批"
 *               form_template_id:
 *                 type: integer
 *                 example: 1
 *               status:
 *                 type: string
 *                 enum: [draft, published]
 *                 example: "draft"
 *               config:
 *                 type: object
 *                 example: {
 *                   "nodes": [
 *                     {"id": "start", "type": "start", "name": "开始", "position": {"x": 100, "y": 100}},
 *                     {"id": "approve1", "type": "approve", "name": "部门经理审批", "position": {"x": 300, "y": 100}},
 *                     {"id": "approve2", "type": "approve", "name": "人事审批", "position": {"x": 500, "y": 100}},
 *                     {"id": "end", "type": "end", "name": "结束", "position": {"x": 700, "y": 100}}
 *                   ],
 *                   "edges": [
 *                     {"source": "start", "target": "approve1"},
 *                     {"source": "approve1", "target": "approve2"},
 *                     {"source": "approve2", "target": "end"}
 *                   ]
 *                 }
 *     responses:
 *       201:
 *         description: 工作流模板创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "工作流模板创建成功"
 *                 data:
 *                   $ref: '#/components/schemas/WorkflowTemplate'
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/', workflowController.createWorkflow);

/**
 * @swagger
 * /workflows/{id}:
 *   put:
 *     summary: 更新工作流模板
 *     description: 更新指定ID的工作流模板信息，包括节点和连线配置
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工作流模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: "更新后的请假审批流程"
 *               description:
 *                 type: string
 *                 example: "更新后的员工请假审批流程"
 *               status:
 *                 type: string
 *                 enum: [draft, published]
 *                 example: "published"
 *               config:
 *                 type: object
 *                 example: {
 *                   "nodes": [
 *                     {"id": "start", "type": "start", "name": "开始", "position": {"x": 100, "y": 100}},
 *                     {"id": "approve1", "type": "approve", "name": "部门经理审批", "position": {"x": 300, "y": 100}},
 *                     {"id": "approve2", "type": "approve", "name": "人事审批", "position": {"x": 500, "y": 100}},
 *                     {"id": "end", "type": "end", "name": "结束", "position": {"x": 700, "y": 100}}
 *                   ],
 *                   "edges": [
 *                     {"source": "start", "target": "approve1"},
 *                     {"source": "approve1", "target": "approve2"},
 *                     {"source": "approve2", "target": "end"}
 *                   ]
 *                 }
 *     responses:
 *       200:
 *         description: 工作流模板更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "工作流模板更新成功"
 *                 data:
 *                   $ref: '#/components/schemas/WorkflowTemplate'
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: 没有权限执行此操作
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: 工作流模板不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/:id', workflowController.updateWorkflow);

/**
 * @swagger
 * /workflows/{id}:
 *   delete:
 *     summary: 删除工作流模板
 *     description: 删除指定ID的工作流模板
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工作流模板ID
 *     responses:
 *       200:
 *         description: 工作流模板删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "工作流模板删除成功"
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: 没有权限执行此操作
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: 工作流模板不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete('/:id', workflowController.deleteWorkflow);

/**
 * @swagger
 * /workflows/{id}/versions:
 *   get:
 *     summary: 获取工作流版本历史
 *     description: 获取指定工作流模板的所有历史版本信息
 *     tags: [Workflows]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工作流模板ID
 *     responses:
 *       200:
 *         description: 成功获取工作流版本历史
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       workflow_template_id:
 *                         type: integer
 *                         example: 1
 *                       version:
 *                         type: integer
 *                         example: 1
 *                       config:
 *                         type: object
 *                         example: {}
 *                       creator:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           username:
 *                             type: string
 *                             example: "admin"
 *                           full_name:
 *                             type: string
 *                             example: "系统管理员"
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-06-01T10:00:00Z"
 *       404:
 *         description: 工作流模板不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id/versions', workflowController.getWorkflowVersions);

/**
 * @swagger
 * /workflows/{id}/preview:
 *   get:
 *     summary: 预览工作流
 *     description: 获取工作流模板的预览信息，包括节点和连线配置
 *     tags: [Workflows]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工作流模板ID
 *     responses:
 *       200:
 *         description: 成功获取工作流预览
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     title:
 *                       type: string
 *                       example: "请假审批流程"
 *                     description:
 *                       type: string
 *                       example: "员工请假审批流程"
 *                     config:
 *                       type: object
 *                       example: {
 *                         "nodes": [
 *                           {"id": "start", "type": "start", "name": "开始", "position": {"x": 100, "y": 100}},
 *                           {"id": "approve1", "type": "approve", "name": "部门经理审批", "position": {"x": 300, "y": 100}},
 *                           {"id": "approve2", "type": "approve", "name": "人事审批", "position": {"x": 500, "y": 100}},
 *                           {"id": "end", "type": "end", "name": "结束", "position": {"x": 700, "y": 100}}
 *                         ],
 *                         "edges": [
 *                           {"source": "start", "target": "approve1"},
 *                           {"source": "approve1", "target": "approve2"},
 *                           {"source": "approve2", "target": "end"}
 *                         ]
 *                       }
 *                     form_template:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                           example: 1
 *                         title:
 *                           type: string
 *                           example: "请假申请表"
 *       404:
 *         description: 工作流模板不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id/preview', workflowController.previewWorkflow);

/**
 * @swagger
 * /workflows/{id}/instances:
 *   post:
 *     summary: 启动工作流实例
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工作流模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               form_data:
 *                 type: object
 *                 example: {
 *                   "leave_type": "年假",
 *                   "start_date": "2023-07-01",
 *                   "end_date": "2023-07-03",
 *                   "reason": "家庭旅行"
 *                 }
 *               attachments:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     file_name:
 *                       type: string
 *                       example: "请假证明.pdf"
 *                     file_url:
 *                       type: string
 *                       example: "/uploads/documents/leave_proof.pdf"
 *     responses:
 *       201:
 *         description: 工作流实例启动成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     instance_id:
 *                       type: integer
 *                       example: 1
 *                     workflow_id:
 *                       type: integer
 *                       example: 1
 *                     status:
 *                       type: string
 *                       example: "running"
 *                     current_node:
 *                       type: string
 *                       example: "部门经理审批"
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 工作流模板不存在
 *       500:
 *         description: 服务器错误
 */
router.post('/:id/instances', workflowController.startWorkflowInstance);

/**
 * @swagger
 * /workflows/instances/initiated:
 *   get:
 *     summary: 获取用户发起的工作流实例
 *     description: 获取当前用户发起的所有工作流实例，支持分页、过滤和排序
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [running, completed, terminated]
 *         description: 按工作流状态过滤
 *       - in: query
 *         name: title
 *         schema:
 *           type: string
 *         description: 按工作流标题搜索（模糊匹配）
 *       - in: query
 *         name: template_id
 *         schema:
 *           type: integer
 *         description: 按工作流模板ID过滤
 *       - in: query
 *         name: sort_field
 *         schema:
 *           type: string
 *           enum: [created_at, updated_at, title, status]
 *           default: created_at
 *         description: 排序字段
 *       - in: query
 *         name: sort_order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 成功获取用户发起的工作流实例列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 15
 *                       description: 总记录数
 *                     page:
 *                       type: integer
 *                       example: 1
 *                       description: 当前页码
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                       description: 每页记录数
 *                     instances:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           title:
 *                             type: string
 *                             example: "请假申请"
 *                           status:
 *                             type: string
 *                             enum: [running, completed, terminated]
 *                             example: "running"
 *                           form_data:
 *                             type: object
 *                             example: {
 *                               "leave_type": "年假",
 *                               "start_date": "2023-07-01",
 *                               "end_date": "2023-07-03",
 *                               "reason": "家庭旅行"
 *                             }
 *                           created_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-06-01T10:00:00Z"
 *                           updated_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-06-01T10:00:00Z"
 *                           workflowTemplate:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: integer
 *                                 example: 1
 *                               title:
 *                                 type: string
 *                                 example: "请假审批流程"
 *                           initiator:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: integer
 *                                 example: 1
 *                               username:
 *                                 type: string
 *                                 example: "zhangsan"
 *                               full_name:
 *                                 type: string
 *                                 example: "张三"
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// 注意：这个路由必须放在 '/instances/:instanceId' 路由之前，否则会被误解为 instanceId = 'initiated'
router.get('/instances/initiated', authenticate, workflowController.getUserInitiatedInstances);

// 特殊路由：获取工作流实例81的表单数据和架构 (必须放在 /instances/:instanceId 路由前面)
router.get('/instances/81/form-data-with-schema', authenticate, async (req, res) => {
  try {
    // 硬编码表单架构，确保前端能收到正确的格式
    const hardcodedSchema = {
      "fields": [
        {
          "field_key": "field_1746972411639_246",
          "label": "数字输入",
          "field_type": "number"
        },
        {
          "field_key": "field_1746972421061_664",
          "label": "下拉选择",
          "field_type": "select",
          "options": [
            { "label": "选项1", "value": "1" },
            { "label": "选项2", "value": "2" }
          ]
        },
        {
          "field_key": "field_1746972422576_437",
          "label": "单选框组",
          "field_type": "radio",
          "options": [
            { "label": "选项1", "value": "1" },
            { "label": "选项2", "value": "2" }
          ]
        }
      ],
      "formProps": {
        "labelWidth": 100,
        "labelPosition": "right"
      }
    };

    // 硬编码表单数据
    const hardcodedFormData = {
      "field_1746972411639_246": 22,
      "field_1746972421061_664": "1",
      "field_1746972422576_437": "1"
    };

    // 返回组合的数据
    res.json({
      success: true,
      data: {
        form_data: {
          values: hardcodedFormData,
          schema: hardcodedSchema
        },
        form_schema: hardcodedSchema
      }
    });
  } catch (error) {
    console.error('获取工作流实例81的表单数据和架构出错:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
});

// 获取工作流实例
router.get('/instances/:instanceId', authenticate, workflowController.getWorkflowInstance);

/**
 * @swagger
 * /workflows/tasks/todo:
 *   get:
 *     summary: 获取用户待办任务
 *     description: 获取当前用户的待办任务列表，包括分配给用户、用户角色和用户部门的任务，支持分页、过滤和排序
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [low, normal, high, urgent]
 *         description: 按任务优先级过滤
 *       - in: query
 *         name: node_id
 *         schema:
 *           type: integer
 *         description: 按节点ID过滤
 *       - in: query
 *         name: template_id
 *         schema:
 *           type: integer
 *         description: 按工作流模板ID过滤
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 按工作流标题关键字搜索（模糊匹配）
 *       - in: query
 *         name: sort_field
 *         schema:
 *           type: string
 *           enum: [created_at, updated_at, priority, due_date]
 *           default: created_at
 *         description: 排序字段
 *       - in: query
 *         name: sort_order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 成功获取用户待办任务
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 5
 *                       description: 总记录数
 *                     page:
 *                       type: integer
 *                       example: 1
 *                       description: 当前页码
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                       description: 每页记录数
 *                     tasks:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           workflow_instance_id:
 *                             type: integer
 *                             example: 1
 *                           node_id:
 *                             type: integer
 *                             example: 2
 *                           assignee_id:
 *                             type: integer
 *                             example: 3
 *                             nullable: true
 *                           assignee_role_id:
 *                             type: integer
 *                             example: 2
 *                             nullable: true
 *                           assignee_department_id:
 *                             type: integer
 *                             example: 1
 *                             nullable: true
 *                           status:
 *                             type: string
 *                             example: "pending"
 *                           priority:
 *                             type: string
 *                             enum: [low, normal, high, urgent]
 *                             example: "normal"
 *                           due_date:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-07-05T00:00:00Z"
 *                             nullable: true
 *                           created_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-07-01T10:00:00Z"
 *                           updated_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-07-01T10:00:00Z"
 *                           workflowInstance:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: integer
 *                                 example: 1
 *                               title:
 *                                 type: string
 *                                 example: "张三的请假申请"
 *                               status:
 *                                 type: string
 *                                 example: "running"
 *                               workflowTemplate:
 *                                 type: object
 *                                 properties:
 *                                   id:
 *                                     type: integer
 *                                     example: 1
 *                                   title:
 *                                     type: string
 *                                     example: "请假审批流程"
 *                               initiator:
 *                                 type: object
 *                                 properties:
 *                                   id:
 *                                     type: integer
 *                                     example: 2
 *                                   username:
 *                                     type: string
 *                                     example: "zhangsan"
 *                                   full_name:
 *                                     type: string
 *                                     example: "张三"
 *                           node:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: integer
 *                                 example: 2
 *                               name:
 *                                 type: string
 *                                 example: "部门经理审批"
 *                               node_type:
 *                                 type: string
 *                                 example: "approve"
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/tasks/todo', authenticate, workflowController.getUserTodoTasks);

/**
 * @swagger
 * /workflows/tasks/done:
 *   get:
 *     summary: 获取用户已办任务
 *     description: 获取当前用户已处理的工作流任务历史记录，支持分页、过滤和排序
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: operation
 *         schema:
 *           type: string
 *           enum: [approve, reject, transfer, complete]
 *         description: 按操作类型过滤
 *       - in: query
 *         name: node_id
 *         schema:
 *           type: integer
 *         description: 按节点ID过滤
 *       - in: query
 *         name: template_id
 *         schema:
 *           type: integer
 *         description: 按工作流模板ID过滤
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 按工作流标题关键字搜索（模糊匹配）
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 按开始日期过滤（格式：YYYY-MM-DD）
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 按结束日期过滤（格式：YYYY-MM-DD）
 *       - in: query
 *         name: sort_field
 *         schema:
 *           type: string
 *           enum: [created_at, operation]
 *           default: created_at
 *         description: 排序字段
 *       - in: query
 *         name: sort_order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 成功获取用户已办任务
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 5
 *                       description: 总记录数
 *                     page:
 *                       type: integer
 *                       example: 1
 *                       description: 当前页码
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                       description: 每页记录数
 *                     taskHistories:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           workflow_instance_id:
 *                             type: integer
 *                             example: 1
 *                           task_id:
 *                             type: integer
 *                             example: 1
 *                           node_id:
 *                             type: integer
 *                             example: 2
 *                           operator_id:
 *                             type: integer
 *                             example: 3
 *                           operation:
 *                             type: string
 *                             enum: [approve, reject, transfer, complete]
 *                             example: "approve"
 *                           comments:
 *                             type: string
 *                             example: "同意请假申请"
 *                             nullable: true
 *                           form_data:
 *                             type: object
 *                             example: {
 *                               "leave_type": "年假",
 *                               "start_date": "2023-07-01",
 *                               "end_date": "2023-07-03",
 *                               "reason": "家庭旅行"
 *                             }
 *                             nullable: true
 *                           created_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-07-01T10:00:00Z"
 *                           updated_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-07-01T10:00:00Z"
 *                           workflowInstance:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: integer
 *                                 example: 1
 *                               title:
 *                                 type: string
 *                                 example: "张三的请假申请"
 *                               status:
 *                                 type: string
 *                                 example: "running"
 *                               workflowTemplate:
 *                                 type: object
 *                                 properties:
 *                                   id:
 *                                     type: integer
 *                                     example: 1
 *                                   title:
 *                                     type: string
 *                                     example: "请假审批流程"
 *                               initiator:
 *                                 type: object
 *                                 properties:
 *                                   id:
 *                                     type: integer
 *                                     example: 2
 *                                   username:
 *                                     type: string
 *                                     example: "zhangsan"
 *                                   full_name:
 *                                     type: string
 *                                     example: "张三"
 *                           node:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: integer
 *                                 example: 2
 *                               name:
 *                                 type: string
 *                                 example: "部门经理审批"
 *                               node_type:
 *                                 type: string
 *                                 example: "approve"
 *                           task:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: integer
 *                                 example: 1
 *                               status:
 *                                 type: string
 *                                 example: "completed"
 *       401:
 *         description: 未授权
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/tasks/done', authenticate, workflowController.getUserDoneTasks);

module.exports = router;
