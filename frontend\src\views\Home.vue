<template>
  <div class="home">
    <div class="welcome-section">
      <h1>欢迎使用工作流系统</h1>
      <p class="subtitle">高效、灵活的业务流程管理平台</p>

      <el-row :gutter="24" class="stats-row">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" v-for="(stat, index) in statistics" :key="index">
          <el-card class="stat-card" shadow="hover" :style="{
            background: `linear-gradient(135deg, var(--${stat.color}-color-light), var(--${stat.color}-color))`,
            color: 'white'
          }">
            <el-icon :class="['stat-icon']"><component :is="stat.icon" /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
            <div class="stat-card-decoration"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <h2 class="section-title">功能模块</h2>
    <el-row :gutter="24">
      <el-col :xs="24" :sm="12" :md="8" :lg="8" v-for="(item, index) in modules" :key="index">
        <el-card class="module-card" shadow="hover">
          <div class="module-icon">
            <el-icon><component :is="item.icon" /></el-icon>
          </div>
          <div class="module-content">
            <h3>{{ item.title }}</h3>
            <p>{{ item.description }}</p>
            <el-button type="primary" @click="$router.push(item.route)" class="module-button">
              进入模块
              <el-icon class="el-icon--right"><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="module-card-bg"></div>
        </el-card>
      </el-col>
    </el-row>

    <h2 class="section-title">最近活动</h2>
    <el-card shadow="hover" class="activity-card">
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in recentActivities"
          :key="index"
          :type="activity.type"
          :color="activity.color"
          :timestamp="activity.time"
        >
          {{ activity.content }}
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'

// 模块数据
const modules = ref([
  {
    title: '部门配置',
    description: '管理组织架构，设置部门层级关系和人员配置',
    route: '/department',
    icon: 'OfficeBuilding'
  },
  {
    title: '成员管理',
    description: '管理部门成员，分配用户到部门',
    route: '/members',
    icon: 'UserFilled'
  },
  {
    title: '表单设计器',
    description: '拖拽式设计表单，支持多种控件和验证规则',
    route: '/form-design',
    icon: 'Document'
  },
  {
    title: '工作流设计',
    description: '可视化设计工作流程，配置节点和流转规则',
    route: '/workflow-design',
    icon: 'Connection'
  },
  {
    title: '工作流填写',
    description: '基于设计好的表单进行数据填写和提交',
    route: '/workflow-form',
    icon: 'Edit'
  },
  {
    title: '工作流流转',
    description: '处理待办任务，执行审批操作，查看流程状态',
    route: '/workflow-process',
    icon: 'Share'
  }
])

// 统计数据
const statistics = ref([
  {
    label: '待办任务',
    value: '12',
    icon: 'Bell',
    color: 'warning'
  },
  {
    label: '已办任务',
    value: '48',
    icon: 'Check',
    color: 'success'
  },
  {
    label: '表单模板',
    value: '8',
    icon: 'Document',
    color: 'primary'
  },
  {
    label: '工作流模板',
    value: '5',
    icon: 'Connection',
    color: 'info'
  }
])

// 在组件挂载时设置颜色变量

onMounted(() => {
  // 添加自定义颜色变量
  document.documentElement.style.setProperty('--primary-color-light', '#3b82f6');
  document.documentElement.style.setProperty('--success-color-light', '#34d399');
  document.documentElement.style.setProperty('--warning-color-light', '#fbbf24');
  document.documentElement.style.setProperty('--info-color-light', '#60a5fa');
});

// 最近活动
const recentActivities = ref([
  {
    content: '张三提交了请假申请',
    time: '2025-05-01 10:30',
    type: 'primary',
    color: '#409EFF'
  },
  {
    content: '李四审批了报销申请',
    time: '2025-05-01 09:15',
    type: 'success',
    color: '#67C23A'
  },
  {
    content: '王五创建了新的表单模板',
    time: '2025-04-30 16:45',
    type: 'info',
    color: '#909399'
  },
  {
    content: '系统管理员更新了部门结构',
    time: '2025-04-30 14:20',
    type: 'warning',
    color: '#E6A23C'
  }
])
</script>

<style scoped>
.home {
  padding: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: var(--spacing-3xl);
  text-align: center;
  position: relative;
  padding: var(--spacing-3xl) 0;
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color-light), var(--primary-color));
  border-radius: var(--border-radius-full);
}

h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
  background: linear-gradient(45deg, var(--primary-color), var(--primary-color-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacing-2xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin: var(--spacing-2xl) 0 var(--spacing-xl);
  position: relative;
  padding-left: var(--spacing-md);
  border-left: 4px solid var(--primary-color);
  display: flex;
  align-items: center;
}

.section-title::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, var(--border-color), transparent);
  margin-left: var(--spacing-lg);
}

.stats-row {
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  height: 120px;
  border-radius: var(--border-radius-lg);
  border: none;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  z-index: 1;
}

.stat-icon {
  font-size: 42px;
  margin-right: var(--spacing-lg);
  z-index: 2;
  background: rgba(255, 255, 255, 0.2);
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-lg);
}

.stat-info {
  display: flex;
  flex-direction: column;
  z-index: 2;
}

.stat-value {
  font-size: 36px;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  margin-bottom: var(--spacing-sm);
}

.stat-label {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: rgba(255, 255, 255, 0.9);
}

.stat-card-decoration {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30%, 30%);
  z-index: 0;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  width: 20px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  z-index: 0;
}

.primary {
  color: var(--primary-color);
}

.success {
  color: var(--success-color);
}

.warning {
  color: var(--warning-color);
}

.info {
  color: var(--info-color);
}

.module-card {
  margin-bottom: var(--spacing-xl);
  height: 240px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s var(--animation-timing-function);
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color-split);
  background: var(--background-color-light);
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--box-shadow-dropdown);
  border-color: transparent;
}

.module-icon {
  font-size: 40px;
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
  background: var(--primary-color-bg);
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-lg);
  margin-left: auto;
  margin-right: auto;
  transition: all 0.3s;
}

.module-card:hover .module-icon {
  transform: scale(1.1);
  background: var(--primary-color);
  color: white;
}

.module-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 var(--spacing-lg);
  text-align: center;
}

.module-content h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.module-content p {
  flex: 1;
  margin-bottom: var(--spacing-lg);
  color: var(--text-color-secondary);
  line-height: 1.6;
}

.module-content .el-button {
  margin-bottom: var(--spacing-lg);
  width: 100%;
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.module-button {
  position: relative;
  overflow: hidden;
}

.module-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  transition: all 0.6s;
  z-index: 1;
}

.module-button:hover::before {
  left: 100%;
}

.module-card-bg {
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, var(--primary-color-bg) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0.5;
  z-index: 0;
  border-radius: 50%;
  transform: translate(30%, -30%);
  transition: all 0.3s;
}

.module-card:hover .module-card-bg {
  transform: translate(20%, -20%) scale(1.2);
  opacity: 0.7;
}

.activity-card {
  margin-bottom: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
}

.el-timeline {
  padding: var(--spacing-md) var(--spacing-lg);
}

.el-timeline-item {
  padding-bottom: var(--spacing-xl);
}

.el-timeline-item__content {
  font-size: var(--font-size-base);
  color: var(--text-color);
}

.el-timeline-item__timestamp {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
}

.el-timeline-item__node {
  background-color: var(--primary-color);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .home {
    padding: var(--spacing-lg);
  }

  h1 {
    font-size: var(--font-size-2xl);
  }

  .subtitle {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-xl);
  }

  .section-title {
    font-size: var(--font-size-lg);
    margin: var(--spacing-xl) 0 var(--spacing-lg);
  }

  .module-card {
    height: auto;
    min-height: 220px;
  }

  .stat-card {
    height: auto;
    min-height: 100px;
  }

  .stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
  }

  .stat-value {
    font-size: 28px;
  }
}
</style>
