/**
 * 测试登录端点
 */
const axios = require('axios');

async function testLogin() {
  try {
    console.log('测试登录端点...');

    const response = await axios.post('http://localhost:3003/api/users/login', {
      username: 'admin',
      password: 'admin123'
    });

    console.log('登录成功!');
    console.log('响应状态码:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));

    return response.data;
  } catch (error) {
    console.error('登录失败!');
    if (error.response) {
      console.error('响应状态码:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('错误:', error.message);
    }

    return null;
  }
}

// 执行测试
testLogin();
