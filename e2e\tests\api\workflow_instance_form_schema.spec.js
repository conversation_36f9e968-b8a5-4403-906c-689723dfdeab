import { test, expect } from '@playwright/test';

// IMPORTANT: Update this with your actual API base URL
const API_BASE_URL = 'http://localhost:3001'; // e.g., http://localhost:YOUR_BACKEND_PORT

// IMPORTANT: Update these with existing workflow instance IDs from your test database
// Instance ID that IS expected to have a form schema linked and populated
const TEST_INSTANCE_ID_WITH_SCHEMA = 81; // Replace!
// Instance ID that is NOT expected to have a form schema (e.g., linked to a non-existent form template, or form template has no schema)
const TEST_INSTANCE_ID_WITHOUT_SCHEMA = 82; // Replace!
// Instance ID for an edge case: form_data exists, but workflow/form template structure might be incomplete. Schema should be null.
const TEST_INSTANCE_ID_EDGE_CASE = 83; // Replace or set to null to skip test

test.describe('Workflow Instance API - Form Schema in form_data', () => {
  test('should return form_data with populated .values and .schema for an instance WITH a linked form schema', async ({ request }) => {
    test.skip(!TEST_INSTANCE_ID_WITH_SCHEMA, 'TEST_INSTANCE_ID_WITH_SCHEMA not set');

    const response = await request.get(`${API_BASE_URL}/api/workflows/instances/${TEST_INSTANCE_ID_WITH_SCHEMA}`);
    expect(response.ok(), `API request failed with status ${response.status()}: ${await response.text()}`).toBeTruthy();

    const responseBody = await response.json();
    expect(responseBody.success, 'Response body .success was not true').toBe(true);
    expect(responseBody.data, 'Response body .data was not defined').toBeDefined();

    const instanceData = responseBody.data;
    expect(instanceData.form_data, 'instance.form_data was not defined').toBeDefined();
    
    expect(instanceData.form_data.values, 'instance.form_data.values was not defined').toBeDefined();
    expect(typeof instanceData.form_data.values).toBe('object'); 

    expect(instanceData.form_data.schema, 'instance.form_data.schema was not defined').toBeDefined();
    expect(typeof instanceData.form_data.schema).toBe('object');
    expect(instanceData.form_data.schema).not.toBeNull();
    // TODO: Add more specific assertions about the schema's content if you know what it should be
    // e.g., expect(Object.keys(instanceData.form_data.schema).length).toBeGreaterThan(0);
  });

  test('should return form_data with populated .values and NULL .schema for an instance WITHOUT a linked/valid form schema', async ({ request }) => {
    test.skip(!TEST_INSTANCE_ID_WITHOUT_SCHEMA, 'TEST_INSTANCE_ID_WITHOUT_SCHEMA not set');
    
    const response = await request.get(`${API_BASE_URL}/api/workflows/instances/${TEST_INSTANCE_ID_WITHOUT_SCHEMA}`);
    expect(response.ok(), `API request failed with status ${response.status()}: ${await response.text()}`).toBeTruthy();

    const responseBody = await response.json();
    expect(responseBody.success, 'Response body .success was not true').toBe(true);
    expect(responseBody.data, 'Response body .data was not defined').toBeDefined();

    const instanceData = responseBody.data;
    expect(instanceData.form_data, 'instance.form_data was not defined').toBeDefined();

    expect(instanceData.form_data.values, 'instance.form_data.values was not defined').toBeDefined();
    expect(typeof instanceData.form_data.values).toBe('object');

    expect(instanceData.form_data.schema, 'instance.form_data.schema was not null as expected').toBeNull();
  });

  test('should correctly handle structure for instance with form_data but potentially incomplete template linkage (edge case)', async ({ request }) => {
    test.skip(!TEST_INSTANCE_ID_EDGE_CASE, 'TEST_INSTANCE_ID_EDGE_CASE not set, skipping this test.');

    const response = await request.get(`${API_BASE_URL}/api/workflows/instances/${TEST_INSTANCE_ID_EDGE_CASE}`);
    expect(response.ok(), `API request failed with status ${response.status()}: ${await response.text()}`).toBeTruthy();

    const responseBody = await response.json();
    expect(responseBody.success).toBe(true);
    expect(responseBody.data).toBeDefined();
    
    const instanceData = responseBody.data;
    expect(instanceData.form_data).toBeDefined();
    expect(instanceData.form_data.values).toBeDefined();
    expect(typeof instanceData.form_data.values).toBe('object');
    expect(instanceData.form_data.schema).toBeNull(); // Expecting null due to incomplete linkage
  });
}); 